#!/bin/bash

echo "🚀 快速更新前端..."

# 进入前端目录
cd frontend-app

# 检查是否有dist目录，如果没有则先安装依赖
if [ ! -d "dist" ]; then
    echo "📦 首次构建，安装依赖..."
    npm install
fi

# 构建前端
echo "🔨 构建前端..."
npm run build-only

# 检查构建是否成功
if [ $? -eq 0 ]; then
    echo "✅ 前端构建成功!"
    
    # 重启前端容器（如果正在运行）
    cd ..
    echo "🔄 重启前端容器..."
    docker-compose -f docker-compose.prod.yml restart frontend
    
    echo "🎉 前端更新完成!"
    echo "📱 访问地址: https://aiknowledgebase.csicollege.cn:3443"
else
    echo "❌ 前端构建失败!"
    exit 1
fi
