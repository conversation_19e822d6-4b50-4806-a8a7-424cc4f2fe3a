<template>
  <div class="chart-container">
    <div v-if="chartData" class="chart-wrapper">
      <h3 v-if="chartData.title" class="chart-title">{{ chartData.title }}</h3>
      <canvas ref="chartCanvas" class="chart-canvas"></canvas>
    </div>
    <div v-else class="error-message">
      <el-icon><Warning /></el-icon>
      <span>图表数据格式错误</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { Chart, registerables } from 'chart.js'
import { Warning } from '@element-plus/icons-vue'

Chart.register(...registerables)

interface ChartData {
  type: 'pie' | 'bar' | 'line' | 'doughnut' | 'radar' | 'polarArea'
  title?: string
  data: {
    labels: string[]
    datasets: Array<{
      label?: string
      data: number[]
      backgroundColor?: string | string[]
      borderColor?: string | string[]
      borderWidth?: number
    }>
  }
  options?: any
}

const props = defineProps<{
  chartData: ChartData
}>()

const chartCanvas = ref<HTMLCanvasElement>()
let chartInstance: Chart | null = null

const defaultColors = [
  '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
  '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
]

const createChart = () => {
  if (!chartCanvas.value || !props.chartData) {
    return
  }

  // 销毁现有图表
  if (chartInstance) {
    chartInstance.destroy()
  }

  const ctx = chartCanvas.value.getContext('2d')
  if (!ctx) {
    return
  }

  // 处理数据集颜色
  const datasets = props.chartData.data.datasets.map((dataset, index) => ({
    ...dataset,
    backgroundColor: dataset.backgroundColor || (
      props.chartData.type === 'pie' || props.chartData.type === 'doughnut'
        ? defaultColors.slice(0, props.chartData.data.labels.length)
        : defaultColors[index % defaultColors.length]
    ),
    borderColor: dataset.borderColor || (
      props.chartData.type === 'line'
        ? defaultColors[index % defaultColors.length]
        : 'rgba(255, 255, 255, 0.8)'
    ),
    borderWidth: dataset.borderWidth || (
      props.chartData.type === 'pie' || props.chartData.type === 'doughnut' ? 2 : 1
    )
  }))

  // 默认配置
  const defaultOptions = {
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        top: 20,
        bottom: 40, // 为 X轴标题预留空间
        left: 20,
        right: 20
      }
    },
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true
      }
    },
    scales: props.chartData.type === 'pie' || props.chartData.type === 'doughnut' ? undefined : {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      },
      x: {
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          font: {
            size: 11
          }
        }
      }
    }
  }

  // 构建最终配置
  const mergedOptions = {
    ...defaultOptions,
    ...props.chartData.options
  }

  // 特殊处理 scales 配置，确保轴标题正确显示
  if (props.chartData.type !== 'pie' && props.chartData.type !== 'doughnut') {
    // 获取 Python 解析的轴标题
    const pythonScales = props.chartData.options?.scales
    const xTitle = pythonScales?.x?.title
    const yTitle = pythonScales?.y?.title

    // 重新构建 scales 配置
    mergedOptions.scales = {
      x: {
        ...defaultOptions.scales?.x,
        title: xTitle ? {
          display: true,
          text: xTitle.text,
          font: {
            size: 14,
            weight: 'bold'
          },
          color: '#333'
        } : undefined
      },
      y: {
        ...defaultOptions.scales?.y,
        title: yTitle ? {
          display: true,
          text: yTitle.text,
          font: {
            size: 14,
            weight: 'bold'
          },
          color: '#333'
        } : undefined
      }
    }
  }



  chartInstance = new Chart(ctx, {
    type: props.chartData.type,
    data: {
      labels: props.chartData.data.labels,
      datasets
    },
    options: mergedOptions
  })


}

onMounted(async () => {
  await nextTick()
  createChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.destroy()
  }
})

watch(() => props.chartData, async () => {
  await nextTick()
  createChart()
}, { deep: true })
</script>

<style scoped>
.chart-container {
  width: 100%;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 16px 0;
}

.chart-wrapper {
  width: 100%;
  height: 500px; /* 进一步增加高度以容纳轴标题 */
  position: relative;
  padding-bottom: 40px; /* 为 X轴标题预留空间 */
  box-sizing: border-box;
}

.chart-title {
  text-align: center;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.chart-canvas {
  width: 100% !important;
  height: 100% !important;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #f56565;
  font-size: 14px;
  padding: 20px;
}

.dark .chart-container {
  background: #1f2937;
}

.dark .chart-title {
  color: #f3f4f6;
}
</style>
