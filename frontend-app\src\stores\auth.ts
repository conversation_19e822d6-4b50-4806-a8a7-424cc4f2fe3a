import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginRequest, RegisterRequest } from '@/types'
import { authAPI } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const user = ref<UserInfo | null>(null)
  const token = ref<string | null>(localStorage.getItem('token'))
  const loading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.is_admin === true)
  const isUser = computed(() => user.value?.is_admin === false)

  // 设置用户信息
  const setUser = (userData: UserInfo) => {
    user.value = userData
  }

  // 设置token
  const setToken = (newToken: string) => {
    token.value = newToken
    localStorage.setItem('token', newToken)
  }

  // 清除认证信息
  const clearAuth = () => {
    user.value = null
    token.value = null
    localStorage.removeItem('token')
  }

  // 登录
  const login = async (loginForm: LoginRequest) => {
    loading.value = true
    try {
      const response = await authAPI.login(loginForm)
      const { user: userData, access_token } = response

      setUser(userData)
      setToken(access_token)

      return { success: true, user: userData }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.detail || '登录失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 注册
  const register = async (registerForm: RegisterRequest) => {
    loading.value = true
    try {
      const userData = await authAPI.register(registerForm)

      setUser(userData)

      return { success: true, user: userData }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.detail || '注册失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 退出登录
  const logout = async () => {
    try {
      // 后端没有logout端点，直接清除本地认证信息
      clearAuth()
    } catch (error) {
      console.error('退出登录失败:', error)
      clearAuth()
    }
  }

  // 获取用户信息
  const fetchUserInfo = async () => {
    if (!token.value) return false

    try {
      const userData = await authAPI.getCurrentUser()
      setUser(userData)
      return true
    } catch (error) {
      clearAuth()
      return false
    }
  }

  // 更新用户信息
  const updateProfile = async (profileData: Partial<UserInfo>) => {
    loading.value = true
    try {
      const userData = await authAPI.updateProfile(profileData)
      setUser(userData)
      return { success: true, user: userData }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.detail || '更新失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 修改密码
  const changePassword = async (currentPassword: string, newPassword: string) => {
    loading.value = true
    try {
      await authAPI.changePassword({
        current_password: currentPassword,
        new_password: newPassword
      })
      return { success: true, message: '密码修改成功' }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.detail || '密码修改失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 初始化认证状态
  const initAuth = async () => {
    if (token.value) {
      await fetchUserInfo()
    }
  }

  return {
    // 状态
    user,
    token,
    loading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isUser,
    
    // 方法
    setUser,
    setToken,
    clearAuth,
    login,
    register,
    logout,
    fetchUserInfo,
    updateProfile,
    changePassword,
    initAuth
  }
})
