#!/bin/bash

# 强制重建Docker镜像脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 1. 停止所有容器
log_info "停止所有容器..."
docker-compose -f docker-compose.prod.yml down --volumes --remove-orphans || true

# 2. 删除项目相关的镜像
log_info "删除项目镜像..."
docker rmi aiknowledgebase-backend aiknowledgebase-frontend || true

# 3. 删除所有悬空镜像
log_info "清理悬空镜像..."
docker image prune -f

# 4. 删除构建缓存
log_info "清理构建缓存..."
docker builder prune -f

# 5. 显示当前requirements.txt内容
log_info "当前requirements.txt内容:"
cat backend/requirements.txt

# 6. 强制重新构建后端镜像
log_info "强制重新构建后端镜像（无缓存）..."
docker build --no-cache --pull -t aiknowledgebase-backend ./backend

# 7. 构建前端镜像
log_info "构建前端镜像..."
docker build --no-cache --pull -t aiknowledgebase-frontend ./frontend-app

# 8. 验证镜像是否包含email-validator
log_info "验证后端镜像依赖..."
docker run --rm aiknowledgebase-backend pip list | grep -E "(email-validator|numpy|scikit-learn)" || log_warn "某些依赖可能未正确安装"

# 9. 启动服务
log_info "启动服务..."
docker-compose -f docker-compose.prod.yml up -d

# 10. 等待服务启动
log_info "等待服务启动..."
sleep 10

# 11. 检查服务状态
log_info "检查服务状态..."
docker-compose -f docker-compose.prod.yml ps

# 12. 查看后端日志
log_info "查看后端启动日志..."
docker-compose -f docker-compose.prod.yml logs --tail=20 backend

log_success "重建完成！"
log_info "访问地址: http://localhost:3000"
