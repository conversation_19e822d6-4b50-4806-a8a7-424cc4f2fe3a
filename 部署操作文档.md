# AI知识库系统 - 部署操作文档

## 系统概述

这是一个基于Vue3 + FastAPI的AI知识库管理系统，支持多种编程语言的项目管理和知识库功能。

## 当前状态

✅ **系统已成功部署并运行**
- 前端：Vue3 + Element Plus
- 后端：FastAPI + Python
- 数据库：SQLite
- 访问地址：http://localhost

## 目录结构

```
aiknowledgebase/
├── frontend/           # 前端Vue3项目
│   ├── src/           # 源代码
│   ├── dist/          # 构建产物
│   └── package.json   # 依赖配置
├── backend/           # 后端FastAPI项目
│   ├── app.py         # 主应用文件
│   ├── requirements.txt # Python依赖
│   └── database.db    # SQLite数据库
├── docker-compose.prod.yml # Docker生产环境配置
├── deploy-fast.sh     # Docker快速部署脚本
├── deploy-simple.sh   # 简单部署脚本（无Docker）
└── 部署操作文档.md    # 本文档
```

## 部署方式

### 方式一：Docker部署（推荐）

**优点：** 环境隔离，部署简单
**缺点：** 需要Docker环境，构建时间较长

```bash
# 1. 确保Docker已安装并运行
docker --version
docker-compose --version

# 2. 执行快速部署脚本
./deploy-fast.sh

# 3. 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 4. 查看日志
docker-compose -f docker-compose.prod.yml logs -f

# 5. 停止服务
docker-compose -f docker-compose.prod.yml down
```

### 方式二：简单部署（当前使用）

**优点：** 不依赖Docker，启动快速
**缺点：** 需要本地环境配置

```bash
# 1. 执行简单部署脚本
./deploy-simple.sh start

# 2. 停止服务
./deploy-simple.sh stop

# 3. 重启服务
./deploy-simple.sh restart
```

## 环境要求

### 基础环境
- **Node.js**: 16.0+ (用于前端构建)
- **Python**: 3.8+ (用于后端运行)
- **Git**: 用于代码管理

### 可选环境
- **Docker**: 用于容器化部署
- **Nginx**: 用于生产环境反向代理

## 功能说明

### 主要功能
1. **项目管理**: 支持PHP、Java、Node、Go、Python等多种语言项目
2. **知识库管理**: 项目文档和知识点管理
3. **搜索功能**: 全文搜索项目和知识库内容
4. **分类管理**: 按技术栈分类管理项目

### 界面说明
- **顶部导航**: 按编程语言分类的项目标签
- **搜索栏**: 支持项目名称和内容搜索
- **项目列表**: 显示项目基本信息（网站名、状态、项目量等）
- **操作按钮**: 新增项目、编辑、删除等操作

## 常用操作

### 添加新项目
1. 点击"新增项目"按钮
2. 填写项目信息：
   - 网站名称
   - 项目描述
   - 技术栈选择
   - 项目状态
3. 保存项目

### 管理项目
1. 在项目列表中找到目标项目
2. 点击操作按钮进行编辑或删除
3. 可以修改项目状态、描述等信息

### 搜索项目
1. 在顶部搜索框输入关键词
2. 系统会实时搜索匹配的项目
3. 支持按项目名称、描述等字段搜索

## 故障排除

### 常见问题

**1. 前端无法访问**
```bash
# 检查前端服务是否运行
curl http://localhost

# 查看前端日志
tail -f frontend.log
```

**2. 后端API错误**
```bash
# 检查后端服务
curl http://localhost:8000/health

# 查看后端日志
tail -f backend.log
```

**3. 数据库连接问题**
```bash
# 检查数据库文件
ls -la backend/database.db

# 重新初始化数据库
cd backend
python -c "from app import init_db; init_db()"
```

### 重新部署
```bash
# 停止所有服务
./deploy-simple.sh stop

# 清理旧文件
rm -f *.log *.pid

# 重新部署
./deploy-simple.sh start
```

## 维护建议

### 日常维护
1. **定期备份数据库**: `cp backend/database.db backup/database_$(date +%Y%m%d).db`
2. **查看日志**: 定期检查 `backend.log` 和 `frontend.log`
3. **更新依赖**: 定期更新前后端依赖包

### 性能优化
1. **数据库优化**: 定期清理无用数据
2. **静态资源**: 使用CDN加速前端资源
3. **缓存策略**: 配置适当的缓存策略

## 开发调试

### 前端开发
```bash
cd frontend
npm install
npm run dev    # 开发模式，支持热重载
npm run build  # 构建生产版本
```

### 后端开发
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python app.py  # 启动开发服务器
```

### API接口测试
```bash
# 健康检查
curl http://localhost:8000/health

# 获取项目列表
curl http://localhost:8000/api/projects

# 添加新项目
curl -X POST http://localhost:8000/api/projects \
  -H "Content-Type: application/json" \
  -d '{"name":"测试项目","description":"测试描述","category":"PHP"}'
```

## 配置说明

### 前端配置 (frontend/vite.config.js)
```javascript
export default {
  server: {
    port: 3000,
    proxy: {
      '/api': 'http://localhost:8000'
    }
  }
}
```

### 后端配置 (backend/app.py)
```python
# 数据库配置
DATABASE_URL = "sqlite:///./database.db"

# 服务器配置
if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 数据库管理

### 查看数据库内容
```bash
cd backend
sqlite3 database.db
.tables          # 查看所有表
.schema projects # 查看表结构
SELECT * FROM projects; # 查看数据
.quit
```

### 备份与恢复
```bash
# 备份数据库
cp backend/database.db backup/database_backup_$(date +%Y%m%d_%H%M%S).db

# 恢复数据库
cp backup/database_backup_20250728_120000.db backend/database.db
```

## 安全建议

### 生产环境配置
1. **修改默认端口**: 避免使用默认的80和8000端口
2. **启用HTTPS**: 配置SSL证书
3. **设置防火墙**: 限制不必要的端口访问
4. **定期更新**: 保持依赖包最新版本

### 访问控制
```bash
# 限制后端API访问（示例）
iptables -A INPUT -p tcp --dport 8000 -s 127.0.0.1 -j ACCEPT
iptables -A INPUT -p tcp --dport 8000 -j DROP
```

## 监控与日志

### 日志文件位置
- 前端日志: `frontend.log`
- 后端日志: `backend.log`
- Nginx日志: `/var/log/nginx/access.log`

### 监控脚本
```bash
#!/bin/bash
# monitor.sh - 简单的服务监控脚本

check_service() {
    if curl -s http://localhost:8000/health > /dev/null; then
        echo "✅ 后端服务正常"
    else
        echo "❌ 后端服务异常"
        ./deploy-simple.sh restart
    fi

    if curl -s http://localhost > /dev/null; then
        echo "✅ 前端服务正常"
    else
        echo "❌ 前端服务异常"
    fi
}

check_service
```

## 扩展功能

### 添加新的编程语言支持
1. 修改前端导航配置
2. 更新后端分类枚举
3. 添加对应的图标和样式

### 集成外部服务
- **文件存储**: 集成OSS或S3
- **搜索引擎**: 集成Elasticsearch
- **缓存系统**: 集成Redis
- **消息队列**: 集成RabbitMQ

## 技术支持

### 问题排查流程
1. **查看日志文件**: `tail -f *.log`
2. **检查服务状态**: `ps aux | grep python`
3. **验证网络连接**: `netstat -tlnp | grep :8000`
4. **重启服务**: `./deploy-simple.sh restart`

### 常用命令
```bash
# 查看端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :8000

# 查看进程
ps aux | grep python
ps aux | grep nginx

# 查看系统资源
top
df -h
free -h
```

### 联系方式
- 技术文档: 本文档
- 问题反馈: 通过Git Issues
- 紧急联系: 系统管理员

---

**最后更新**: 2025-07-28
**版本**: v1.0
**维护者**: AI Assistant

**快速启动命令**: `./deploy-simple.sh start`
**访问地址**: http://localhost
