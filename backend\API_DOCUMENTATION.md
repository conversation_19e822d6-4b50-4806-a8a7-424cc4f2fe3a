# 后端API文档

## 概述

本文档描述了智能问答系统后端的所有API接口，包括认证、用户管理、知识库管理、文档管理、聊天功能、AI模型管理、统计数据和管理员功能。

**基础URL**: `http://localhost:8000`

## 目录

1. [认证API](#认证api)
2. [用户API](#用户api)
3. [知识库API](#知识库api)
4. [文档API](#文档api)
5. [聊天API](#聊天api)
6. [AI模型API](#ai模型api)
7. [统计API](#统计api)
8. [管理员API](#管理员api)

---

## 认证API

### 用户注册
- **路由**: `POST /api/auth/register`
- **功能**: 注册新用户
- **参数**:
  ```json
  {
    "username": "string",
    "email": "string", 
    "password": "string",
    "display_name": "string (可选)"
  }
  ```
- **响应**: 用户信息和访问令牌

### 用户登录
- **路由**: `POST /api/auth/login`
- **功能**: 用户登录认证
- **参数**:
  ```json
  {
    "username": "string",
    "password": "string"
  }
  ```
- **响应**: 访问令牌和用户信息

### 获取当前用户信息
- **路由**: `GET /api/auth/me`
- **功能**: 获取当前登录用户的信息
- **认证**: 需要Bearer Token
- **响应**: 当前用户详细信息

### 更新用户资料
- **路由**: `PUT /api/auth/profile`
- **功能**: 更新用户个人资料
- **认证**: 需要Bearer Token
- **参数**:
  ```json
  {
    "display_name": "string (可选)",
    "bio": "string (可选)",
    "avatar_url": "string (可选)"
  }
  ```

### 修改密码
- **路由**: `PUT /api/auth/password`
- **功能**: 修改用户密码
- **认证**: 需要Bearer Token
- **参数**:
  ```json
  {
    "current_password": "string",
    "new_password": "string"
  }
  ```

---

## 用户API

### 获取用户列表
- **路由**: `GET /api/users/`
- **功能**: 获取所有用户列表（仅管理员）
- **认证**: 需要管理员权限
- **响应**: 用户列表

### 获取用户资料
- **路由**: `GET /api/users/profile`
- **功能**: 获取当前用户资料（别名）
- **认证**: 需要Bearer Token
- **响应**: 用户信息

### 获取当前用户信息
- **路由**: `GET /api/users/me`
- **功能**: 获取当前用户信息
- **认证**: 需要Bearer Token
- **响应**: 用户详细信息

---

## 知识库API

### 获取知识库列表
- **路由**: `GET /api/knowledge-bases/`
- **功能**: 获取用户的知识库列表
- **认证**: 需要Bearer Token
- **查询参数**:
  - `offset`: 偏移量（默认0）
  - `limit`: 限制数量（默认50）
- **响应**: 知识库列表

### 创建知识库
- **路由**: `POST /api/knowledge-bases/`
- **功能**: 创建新的知识库
- **认证**: 需要Bearer Token
- **参数**:
  ```json
  {
    "name": "string",
    "description": "string (可选)"
  }
  ```
- **响应**: 创建的知识库信息

### 获取知识库详情
- **路由**: `GET /api/knowledge-bases/{kb_id}`
- **功能**: 获取特定知识库的详细信息
- **认证**: 需要Bearer Token
- **路径参数**:
  - `kb_id`: 知识库ID
- **响应**: 知识库详情和文档列表

### 更新知识库
- **路由**: `PUT /api/knowledge-bases/{kb_id}`
- **功能**: 更新知识库信息
- **认证**: 需要Bearer Token
- **路径参数**:
  - `kb_id`: 知识库ID
- **参数**:
  ```json
  {
    "name": "string (可选)",
    "description": "string (可选)"
  }
  ```

### 删除知识库
- **路由**: `DELETE /api/knowledge-bases/{kb_id}`
- **功能**: 删除知识库及其所有文档
- **认证**: 需要Bearer Token
- **路径参数**:
  - `kb_id`: 知识库ID

---

## 文档API

### 获取文档列表
- **路由**: `GET /api/documents/`
- **功能**: 获取文档列表
- **认证**: 需要Bearer Token
- **查询参数**:
  - `kb_id`: 知识库ID（可选）

### 获取知识库文档列表
- **路由**: `GET /api/documents/kb/{kb_id}`
- **功能**: 获取指定知识库的文档列表
- **认证**: 需要Bearer Token
- **路径参数**:
  - `kb_id`: 知识库ID
- **查询参数**:
  - `offset`: 偏移量（默认0）
  - `limit`: 限制数量（默认100）

### 上传文档
- **路由**: `POST /api/documents/upload/{kb_id}`
- **功能**: 上传文档到知识库
- **认证**: 需要Bearer Token
- **路径参数**:
  - `kb_id`: 知识库ID
- **参数**: 
  - `file`: 上传的文件（multipart/form-data）
- **响应**: 上传的文档信息

### 批量上传文档
- **路由**: `POST /api/documents/batch-upload/{kb_id}`
- **功能**: 批量上传多个文档到知识库
- **认证**: 需要Bearer Token
- **路径参数**:
  - `kb_id`: 知识库ID
- **参数**: 
  - `files`: 多个上传文件（multipart/form-data）
- **响应**: 上传结果统计

### 获取文档详情
- **路由**: `GET /api/documents/{doc_id}`
- **功能**: 获取文档详细信息
- **认证**: 需要Bearer Token
- **路径参数**:
  - `doc_id`: 文档ID

### 删除文档
- **路由**: `DELETE /api/documents/{doc_id}`
- **功能**: 删除单个文档
- **认证**: 需要Bearer Token
- **路径参数**:
  - `doc_id`: 文档ID

### 批量删除文档
- **路由**: `DELETE /api/documents/batch-delete`
- **功能**: 批量删除多个文档
- **认证**: 需要Bearer Token
- **参数**:
  ```json
  {
    "document_ids": [1, 2, 3]
  }
  ```
- **响应**: 删除结果统计

### 文档向量化
- **路由**: `POST /api/documents/{doc_id}/vectorize`
- **功能**: 手动触发文档向量化处理
- **认证**: 需要Bearer Token
- **路径参数**:
  - `doc_id`: 文档ID

---

## 聊天API

### 获取聊天会话列表
- **路由**: `GET /api/chat/sessions`
- **功能**: 获取用户的聊天会话列表
- **认证**: 需要Bearer Token
- **查询参数**:
  - `offset`: 偏移量（默认0）
  - `limit`: 限制数量（默认50）

### 创建聊天会话
- **路由**: `POST /api/chat/sessions`
- **功能**: 创建新的聊天会话
- **认证**: 需要Bearer Token
- **参数**:
  ```json
  {
    "title": "string (可选)"
  }
  ```

### 删除聊天会话
- **路由**: `DELETE /api/chat/sessions/{session_id}`
- **功能**: 删除聊天会话及其所有消息
- **认证**: 需要Bearer Token
- **路径参数**:
  - `session_id`: 会话ID

### 获取聊天消息列表
- **路由**: `GET /api/chat/sessions/{session_id}/messages`
- **功能**: 获取聊天会话的消息列表
- **认证**: 需要Bearer Token
- **路径参数**:
  - `session_id`: 会话ID
- **查询参数**:
  - `offset`: 偏移量（默认0）
  - `limit`: 限制数量（默认50）

### 获取聊天历史记录
- **路由**: `GET /api/chat/sessions/{session_id}/messages/history`
- **功能**: 获取聊天历史记录（可控制数量）
- **认证**: 需要Bearer Token
- **路径参数**:
  - `session_id`: 会话ID
- **查询参数**:
  - `limit`: 限制返回的消息数量（可选）
  - `include_system`: 是否包含系统消息（默认false）

### 流式聊天
- **路由**: `POST /api/chat/sessions/{session_id}/stream`
- **功能**: 发送消息并获取AI流式响应
- **认证**: 需要Bearer Token
- **路径参数**:
  - `session_id`: 会话ID
- **参数**:
  ```json
  {
    "message": "string",
    "model_id": "string",
    "knowledge_base_ids": [1, 2, 3],
    "history_limit": 10
  }
  ```
- **响应**: Server-Sent Events流

### 删除聊天消息
- **路由**: `DELETE /api/chat/messages/{message_id}`
- **功能**: 删除单条聊天消息
- **认证**: 需要Bearer Token
- **路径参数**:
  - `message_id`: 消息ID

### 批量删除聊天消息
- **路由**: `DELETE /api/chat/messages/batch`
- **功能**: 批量删除聊天消息
- **认证**: 需要Bearer Token
- **参数**:
  ```json
  {
    "message_ids": [1, 2, 3]
  }
  ```

### 重新生成消息
- **路由**: `POST /api/chat/messages/regenerate`
- **功能**: 重新生成AI消息
- **认证**: 需要Bearer Token
- **参数**:
  ```json
  {
    "message_id": 1,
    "model_id": "string",
    "knowledge_base_ids": [1, 2, 3]
  }
  ```

### 清空聊天会话
- **路由**: `DELETE /api/chat/sessions/{session_id}/messages`
- **功能**: 清空聊天会话的所有消息
- **认证**: 需要Bearer Token
- **路径参数**:
  - `session_id`: 会话ID

---

## AI模型API

### 获取AI模型列表
- **路由**: `GET /api/ai/models`
- **功能**: 获取可用的AI模型列表
- **响应**: AI模型列表

### 获取用户API密钥
- **路由**: `GET /api/ai/api-keys`
- **功能**: 获取用户的API密钥列表
- **认证**: 需要Bearer Token

### 创建用户API密钥
- **路由**: `POST /api/ai/api-keys`
- **功能**: 创建新的API密钥
- **认证**: 需要Bearer Token
- **参数**:
  ```json
  {
    "provider_name": "string",
    "api_key": "string",
    "description": "string (可选)"
  }
  ```

### 更新用户API密钥
- **路由**: `PUT /api/ai/api-keys/{key_id}`
- **功能**: 更新API密钥
- **认证**: 需要Bearer Token
- **路径参数**:
  - `key_id`: 密钥ID
- **参数**:
  ```json
  {
    "api_key": "string",
    "description": "string (可选)"
  }
  ```

### 删除用户API密钥
- **路由**: `DELETE /api/ai/api-keys/{key_id}`
- **功能**: 删除API密钥
- **认证**: 需要Bearer Token
- **路径参数**:
  - `key_id`: 密钥ID

---

## 统计API

### 获取仪表盘统计
- **路由**: `GET /api/stats/dashboard`
- **功能**: 获取用户首页基础统计数据
- **认证**: 需要Bearer Token
- **响应**:
  ```json
  {
    "knowledge_bases": 5,
    "documents": 23,
    "chat_sessions": 12,
    "storage_used": 1.25,
    "storage_total": 10.0,
    "storage_percent": 12
  }
  ```

### 获取详细统计
- **路由**: `GET /api/stats/detailed`
- **功能**: 获取详细统计数据
- **认证**: 需要Bearer Token
- **响应**:
  ```json
  {
    "knowledge_bases": 5,
    "documents": 23,
    "chat_sessions": 12,
    "total_messages": 156,
    "storage_used_bytes": 1342177280,
    "storage_used_mb": 1280.0,
    "storage_used_gb": 1.25,
    "storage_total_gb": 10.0,
    "storage_percent": 12,
    "recent_activity": {
      "knowledge_bases_created": 2,
      "documents_uploaded": 8,
      "chat_sessions_created": 3,
      "messages_sent": 45
    }
  }
  ```

### 获取活动统计
- **路由**: `GET /api/stats/activity`
- **功能**: 获取最近几天的活动统计
- **认证**: 需要Bearer Token
- **查询参数**:
  - `days`: 统计天数（默认7天）
- **响应**: 每日活动统计数组

---

## 管理员API

### 获取系统统计
- **路由**: `GET /api/admin/stats`
- **功能**: 获取系统整体统计信息
- **认证**: 需要管理员权限
- **响应**: 系统统计数据

### 获取所有用户
- **路由**: `GET /api/admin/users`
- **功能**: 获取所有用户列表
- **认证**: 需要管理员权限
- **查询参数**:
  - `offset`: 偏移量（默认0）
  - `limit`: 限制数量（默认50）
  - `status_filter`: 状态过滤（可选）

### 更新用户状态
- **路由**: `PUT /api/admin/users/{user_id}/status`
- **功能**: 更新用户状态
- **认证**: 需要管理员权限
- **路径参数**:
  - `user_id`: 用户ID
- **参数**:
  ```json
  {
    "status": "active|disabled|pending"
  }
  ```

### 获取操作日志
- **路由**: `GET /api/admin/logs`
- **功能**: 获取系统操作日志
- **认证**: 需要管理员权限
- **查询参数**:
  - `offset`: 偏移量（默认0）
  - `limit`: 限制数量（默认50）
  - `action_filter`: 操作类型过滤（可选）

### 获取系统设置
- **路由**: `GET /api/admin/settings`
- **功能**: 获取系统设置
- **认证**: 需要管理员权限

### 更新系统设置
- **路由**: `PUT /api/admin/settings/{setting_key}`
- **功能**: 更新系统设置
- **认证**: 需要管理员权限
- **路径参数**:
  - `setting_key`: 设置键名
- **参数**:
  ```json
  {
    "value": "string"
  }
  ```

---

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误类型",
  "message": "错误描述",
  "details": {}
}
```

### 分页响应
```json
{
  "items": [],
  "total": 100,
  "page": 1,
  "pageSize": 20,
  "totalPages": 5
}
```

---

## 认证说明

大部分API需要在请求头中包含Bearer Token：

```
Authorization: Bearer <access_token>
```

管理员API需要管理员权限的用户才能访问。

---

## 错误代码

- `400`: 请求参数错误
- `401`: 未认证或认证失败
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 数据验证失败
- `500`: 服务器内部错误

---

## 支持的文件类型

文档上传支持以下文件类型：
- 文本文件：`.txt`, `.md`
- 文档文件：`.pdf`, `.doc`, `.docx`
- 其他格式根据系统配置

---

## 限制说明

- 单个文件最大大小：50MB（可配置）
- 用户存储空间：10GB（可配置）
- API请求频率：根据用户类型限制
- 批量操作：单次最多处理100个项目

---

## 使用示例

### 完整的API调用流程示例

#### 1. 用户注册和登录
```bash
# 注册用户
curl -X POST http://localhost:8000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "display_name": "测试用户"
  }'

# 用户登录
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'
```

#### 2. 创建知识库和上传文档
```bash
# 创建知识库
curl -X POST http://localhost:8000/api/knowledge-bases/ \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "我的知识库",
    "description": "测试知识库"
  }'

# 上传文档
curl -X POST http://localhost:8000/api/documents/upload/1 \
  -H "Authorization: Bearer <token>" \
  -F "file=@document.txt"

# 批量上传文档
curl -X POST http://localhost:8000/api/documents/batch-upload/1 \
  -H "Authorization: Bearer <token>" \
  -F "files=@doc1.txt" \
  -F "files=@doc2.txt" \
  -F "files=@doc3.txt"
```

#### 3. 创建聊天会话和发送消息
```bash
# 创建聊天会话
curl -X POST http://localhost:8000/api/chat/sessions \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "测试聊天"
  }'

# 发送流式聊天消息
curl -X POST http://localhost:8000/api/chat/sessions/1/stream \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "请介绍一下机器学习",
    "model_id": "Qwen/Qwen2.5-7B-Instruct",
    "knowledge_base_ids": [1],
    "history_limit": 10
  }'
```

#### 4. 获取统计数据
```bash
# 获取基础统计
curl -X GET http://localhost:8000/api/stats/dashboard \
  -H "Authorization: Bearer <token>"

# 获取详细统计
curl -X GET http://localhost:8000/api/stats/detailed \
  -H "Authorization: Bearer <token>"

# 获取活动统计
curl -X GET "http://localhost:8000/api/stats/activity?days=7" \
  -H "Authorization: Bearer <token>"
```

## 数据模型

### 用户模型
```json
{
  "id": 1,
  "username": "testuser",
  "email": "<EMAIL>",
  "display_name": "测试用户",
  "avatar_url": "https://example.com/avatar.jpg",
  "bio": "用户简介",
  "is_admin": false,
  "status": "active",
  "last_login_at": "2025-07-25T10:30:00Z",
  "created_at": "2025-07-20T08:00:00Z",
  "updated_at": "2025-07-25T10:30:00Z"
}
```

### 知识库模型
```json
{
  "id": 1,
  "owner_id": 1,
  "name": "我的知识库",
  "description": "测试知识库",
  "created_at": "2025-07-25T09:00:00Z",
  "updated_at": "2025-07-25T09:00:00Z"
}
```

### 文档模型
```json
{
  "id": 1,
  "kb_id": 1,
  "uploader_id": 1,
  "filename": "document.txt",
  "storage_path": "uploads/uuid_document.txt",
  "file_type": "text/plain",
  "file_size": 1024,
  "status": "completed",
  "error_message": null,
  "created_at": "2025-07-25T09:15:00Z",
  "updated_at": "2025-07-25T09:16:00Z"
}
```

### 聊天会话模型
```json
{
  "id": 1,
  "user_id": 1,
  "title": "测试聊天",
  "created_at": "2025-07-25T10:00:00Z",
  "updated_at": "2025-07-25T10:30:00Z"
}
```

### 聊天消息模型
```json
{
  "id": 1,
  "session_id": 1,
  "role": "user",
  "content": "请介绍一下机器学习",
  "model_id_used": 1,
  "referenced_kbs": [1, 2],
  "created_at": "2025-07-25T10:15:00Z"
}
```

### AI模型提供商模型
```json
{
  "id": 1,
  "provider_name": "硅基流动",
  "model_name": "Qwen/Qwen2.5-7B-Instruct",
  "is_active": true,
  "allow_system_key_use": true,
  "created_at": "2025-07-25T08:00:00Z",
  "updated_at": "2025-07-25T08:00:00Z"
}
```

## 状态码说明

### 文档状态
- `pending`: 等待处理
- `processing`: 正在处理
- `completed`: 处理完成
- `failed`: 处理失败

### 用户状态
- `active`: 活跃用户
- `disabled`: 已禁用
- `pending`: 待审核

### 聊天消息角色
- `user`: 用户消息
- `assistant`: AI助手消息
- `system`: 系统消息

## 配置说明

### 环境变量
```bash
# 数据库配置
DATABASE_URL=postgresql://user:password@localhost/dbname

# Redis配置
REDIS_URL=redis://localhost:6379

# AI服务配置
SILICONFLOW_API_KEY=your_api_key
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# 文件存储配置
MAX_FILE_SIZE=50  # MB
UPLOAD_DIR=uploads

# 安全配置
SECRET_KEY=your_secret_key
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### 支持的AI模型
- **硅基流动**:
  - `Qwen/Qwen2.5-7B-Instruct`
  - `Qwen/Qwen2.5-14B-Instruct`
  - `Qwen/Qwen2.5-32B-Instruct`
  - `Qwen/Qwen2.5-72B-Instruct`
  - `deepseek-ai/DeepSeek-V2.5`
  - `meta-llama/Meta-Llama-3.1-8B-Instruct`

## 开发指南

### 本地开发环境搭建
```bash
# 1. 克隆项目
git clone <repository_url>
cd backend

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 4. 初始化数据库
python init_database.py

# 5. 启动服务
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 测试
```bash
# 运行所有测试
python -m pytest

# 运行特定测试
python test_stats_api.py
python test_new_apis.py
python test_simple_upload.py
```

## 性能优化建议

### 数据库优化
- 为经常查询的字段添加索引
- 使用数据库连接池
- 定期清理过期数据

### 缓存策略
- 使用Redis缓存频繁访问的数据
- 缓存AI响应结果
- 缓存用户会话信息

### 文件存储优化
- 使用对象存储服务（如AWS S3）
- 实现文件压缩和去重
- 定期清理未使用的文件

## 安全考虑

### 认证和授权
- 使用JWT令牌进行身份验证
- 实现基于角色的访问控制
- 定期轮换API密钥

### 数据保护
- 敏感数据加密存储
- 实现数据备份策略
- 遵循数据隐私法规

### API安全
- 实现请求频率限制
- 输入数据验证和清理
- 防止SQL注入和XSS攻击

---

*文档版本：v1.0*
*最后更新：2025-07-25*
