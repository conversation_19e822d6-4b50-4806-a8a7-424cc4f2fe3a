# API快速参考

## 基础信息
- **基础URL**: `http://localhost:8000`
- **认证方式**: Bearer Token
- **内容类型**: `application/json`

## 认证API

| 方法 | 路由 | 功能 | 认证 |
|------|------|------|------|
| POST | `/api/auth/register` | 用户注册 | ❌ |
| POST | `/api/auth/login` | 用户登录 | ❌ |
| GET | `/api/auth/me` | 获取当前用户信息 | ✅ |
| PUT | `/api/auth/profile` | 更新用户资料 | ✅ |
| PUT | `/api/auth/password` | 修改密码 | ✅ |

## 知识库API

| 方法 | 路由 | 功能 | 认证 |
|------|------|------|------|
| GET | `/api/knowledge-bases/` | 获取知识库列表 | ✅ |
| POST | `/api/knowledge-bases/` | 创建知识库 | ✅ |
| GET | `/api/knowledge-bases/{kb_id}` | 获取知识库详情 | ✅ |
| PUT | `/api/knowledge-bases/{kb_id}` | 更新知识库 | ✅ |
| DELETE | `/api/knowledge-bases/{kb_id}` | 删除知识库 | ✅ |

## 文档API

| 方法 | 路由 | 功能 | 认证 |
|------|------|------|------|
| GET | `/api/documents/kb/{kb_id}` | 获取文档列表 | ✅ |
| POST | `/api/documents/upload/{kb_id}` | 上传文档 | ✅ |
| POST | `/api/documents/batch-upload/{kb_id}` | 批量上传文档 | ✅ |
| GET | `/api/documents/{doc_id}` | 获取文档详情 | ✅ |
| DELETE | `/api/documents/{doc_id}` | 删除文档 | ✅ |
| DELETE | `/api/documents/batch-delete` | 批量删除文档 | ✅ |
| POST | `/api/documents/{doc_id}/vectorize` | 文档向量化 | ✅ |

## 聊天API

| 方法 | 路由 | 功能 | 认证 |
|------|------|------|------|
| GET | `/api/chat/sessions` | 获取聊天会话列表 | ✅ |
| POST | `/api/chat/sessions` | 创建聊天会话 | ✅ |
| DELETE | `/api/chat/sessions/{session_id}` | 删除聊天会话 | ✅ |
| GET | `/api/chat/sessions/{session_id}/messages` | 获取消息列表 | ✅ |
| GET | `/api/chat/sessions/{session_id}/messages/history` | 获取历史记录 | ✅ |
| POST | `/api/chat/sessions/{session_id}/stream` | 流式聊天 | ✅ |
| DELETE | `/api/chat/messages/{message_id}` | 删除消息 | ✅ |
| DELETE | `/api/chat/messages/batch` | 批量删除消息 | ✅ |
| POST | `/api/chat/messages/regenerate` | 重新生成消息 | ✅ |
| DELETE | `/api/chat/sessions/{session_id}/messages` | 清空会话消息 | ✅ |

## AI模型API

| 方法 | 路由 | 功能 | 认证 |
|------|------|------|------|
| GET | `/api/ai/models` | 获取AI模型列表 | ❌ |
| GET | `/api/ai/api-keys` | 获取用户API密钥 | ✅ |
| POST | `/api/ai/api-keys` | 创建API密钥 | ✅ |
| PUT | `/api/ai/api-keys/{key_id}` | 更新API密钥 | ✅ |
| DELETE | `/api/ai/api-keys/{key_id}` | 删除API密钥 | ✅ |

## 统计API

| 方法 | 路由 | 功能 | 认证 |
|------|------|------|------|
| GET | `/api/stats/dashboard` | 获取基础统计 | ✅ |
| GET | `/api/stats/detailed` | 获取详细统计 | ✅ |
| GET | `/api/stats/activity` | 获取活动统计 | ✅ |

## 管理员API

| 方法 | 路由 | 功能 | 认证 |
|------|------|------|------|
| GET | `/api/admin/stats` | 获取系统统计 | 🔒 |
| GET | `/api/admin/users` | 获取所有用户 | 🔒 |
| PUT | `/api/admin/users/{user_id}/status` | 更新用户状态 | 🔒 |
| GET | `/api/admin/logs` | 获取操作日志 | 🔒 |
| GET | `/api/admin/settings` | 获取系统设置 | 🔒 |
| PUT | `/api/admin/settings/{setting_key}` | 更新系统设置 | 🔒 |

## 常用请求示例

### 登录获取Token
```bash
curl -X POST http://localhost:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "testuser", "password": "test123"}'
```

### 创建知识库
```bash
curl -X POST http://localhost:8000/api/knowledge-bases/ \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{"name": "我的知识库", "description": "测试知识库"}'
```

### 上传文档
```bash
curl -X POST http://localhost:8000/api/documents/upload/1 \
  -H "Authorization: Bearer <token>" \
  -F "file=@document.txt"
```

### 流式聊天
```bash
curl -X POST http://localhost:8000/api/chat/sessions/1/stream \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "你好",
    "model_id": "Qwen/Qwen2.5-7B-Instruct",
    "knowledge_base_ids": [1]
  }'
```

### 获取统计数据
```bash
curl -X GET http://localhost:8000/api/stats/dashboard \
  -H "Authorization: Bearer <token>"
```

## 响应格式

### 成功响应
```json
{
  "id": 1,
  "name": "示例数据",
  "created_at": "2025-07-25T10:00:00Z"
}
```

### 错误响应
```json
{
  "detail": "错误描述"
}
```

### 分页响应
```json
{
  "items": [...],
  "total": 100,
  "page": 1,
  "pageSize": 20
}
```

## 状态码

- `200`: 成功
- `201`: 创建成功
- `400`: 请求错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `422`: 验证失败
- `500`: 服务器错误

## 图标说明

- ❌: 无需认证
- ✅: 需要用户认证
- 🔒: 需要管理员权限

## 支持的AI模型

- `Qwen/Qwen2.5-7B-Instruct`
- `Qwen/Qwen2.5-14B-Instruct`
- `deepseek-ai/DeepSeek-V2.5`
- `meta-llama/Meta-Llama-3.1-8B-Instruct`

## 文件类型支持

- 文本文件: `.txt`, `.md`
- 文档文件: `.pdf`, `.doc`, `.docx`
- 最大文件大小: 50MB

## 限制说明

- 用户存储空间: 10GB
- 批量操作: 最多100个项目
- API请求频率: 根据用户类型限制

---

*快速参考版本：v1.0*
