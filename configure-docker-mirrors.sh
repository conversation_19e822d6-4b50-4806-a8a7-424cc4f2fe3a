#!/bin/bash

# Docker镜像源配置脚本
# 适用于Windows Docker Desktop和Linux Docker

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检测操作系统
detect_os() {
    if [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]] || [[ -n "$WINDIR" ]]; then
        echo "windows"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    else
        echo "unknown"
    fi
}

# 配置Docker镜像源
configure_docker_mirrors() {
    local os=$(detect_os)
    
    log_info "检测到操作系统: $os"
    
    case $os in
        "windows")
            configure_windows_docker
            ;;
        "linux")
            configure_linux_docker
            ;;
        "macos")
            configure_macos_docker
            ;;
        *)
            log_error "不支持的操作系统"
            exit 1
            ;;
    esac
}

# Windows Docker Desktop配置
configure_windows_docker() {
    log_info "配置Windows Docker Desktop镜像源..."
    
    cat << 'EOF'
请手动配置Docker Desktop镜像源：

1. 打开Docker Desktop
2. 点击设置图标（齿轮）
3. 选择 "Docker Engine"
4. 在JSON配置中添加以下内容：

{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerproxy.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://reg-mirror.qiniu.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false
}

5. 点击 "Apply & Restart"
6. 等待Docker重启完成

EOF
    
    log_warn "请按照上述步骤手动配置Docker Desktop"
}

# Linux Docker配置
configure_linux_docker() {
    log_info "配置Linux Docker镜像源..."
    
    # 创建docker配置目录
    sudo mkdir -p /etc/docker
    
    # 创建daemon.json配置文件
    sudo tee /etc/docker/daemon.json > /dev/null << 'EOF'
{
  "registry-mirrors": [
    "https://docker.m.daocloud.io",
    "https://dockerproxy.com",
    "https://docker.mirrors.ustc.edu.cn",
    "https://reg-mirror.qiniu.com"
  ],
  "insecure-registries": [],
  "debug": false,
  "experimental": false,
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "100m",
    "max-file": "3"
  }
}
EOF
    
    # 重启Docker服务
    sudo systemctl daemon-reload
    sudo systemctl restart docker
    
    log_success "Linux Docker镜像源配置完成"
}

# macOS Docker Desktop配置
configure_macos_docker() {
    log_info "配置macOS Docker Desktop镜像源..."
    
    cat << 'EOF'
请手动配置Docker Desktop镜像源：

1. 打开Docker Desktop
2. 点击设置图标（齿轮）
3. 选择 "Docker Engine"
4. 在JSON配置中添加镜像源配置
5. 点击 "Apply & Restart"

EOF
    
    log_warn "请按照上述步骤手动配置Docker Desktop"
}

# 测试镜像源
test_docker_mirrors() {
    log_info "测试Docker镜像源..."
    
    # 拉取一个小镜像测试
    if docker pull hello-world:latest; then
        log_success "Docker镜像源配置成功！"
    else
        log_error "Docker镜像源配置可能有问题"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始配置Docker镜像源..."
    
    # 检查Docker是否安装
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 配置镜像源
    configure_docker_mirrors
    
    # 等待用户确认（Windows/macOS需要手动配置）
    local os=$(detect_os)
    if [[ "$os" == "windows" ]] || [[ "$os" == "macos" ]]; then
        read -p "请确认已完成Docker Desktop配置，按回车继续测试..."
    fi
    
    # 测试镜像源
    test_docker_mirrors
    
    log_success "Docker镜像源配置完成！"
}

main "$@"
