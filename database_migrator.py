#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess
import psycopg2
from datetime import datetime
import json

class DatabaseMigrator:
    def __init__(self):
        self.source_config = {
            'host': 'localhost',
            'port': 5432,
            'user': 'postgres',  # 修改为你的用户名
            'database': 'aiknowledgebase',  # 修改为你的数据库名
            'password': '111222'  # 修改为你的密码
        }
        
        self.docker_config = {
            'container_name': 'ai-knowledge-postgres-prod',
            'host': 'localhost',
            'port': 5433,  # Docker映射的端口
            'user': 'postgres',
            'database': 'aiknowledgebase',
            'password': '111222'
        }
    
    def test_connection(self, config, name="数据库"):
        """测试数据库连接"""
        try:
            print(f"🔍 测试{name}连接...")
            conn = psycopg2.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database']
            )
            cursor = conn.cursor()
            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]
            print(f"✅ {name}连接成功")
            print(f"   版本: {version[:50]}...")
            cursor.close()
            conn.close()
            return True
        except Exception as e:
            print(f"❌ {name}连接失败: {e}")
            return False
    
    def get_table_info(self, config):
        """获取表信息"""
        try:
            conn = psycopg2.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database']
            )
            cursor = conn.cursor()
            
            # 获取表列表
            cursor.execute("""
                SELECT schemaname, tablename, n_live_tup 
                FROM pg_stat_user_tables 
                ORDER BY n_live_tup DESC;
            """)
            tables = cursor.fetchall()
            
            print(f"\n📊 数据库表信息:")
            print("-" * 50)
            for schema, table, rows in tables:
                print(f"  {schema}.{table}: {rows} 行")
            
            cursor.close()
            conn.close()
            return tables
        except Exception as e:
            print(f"❌ 获取表信息失败: {e}")
            return []
    
    def export_database(self):
        """导出源数据库"""
        print("\n📊 开始导出源数据库...")
        
        # 创建导出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_dir = f"database_export_{timestamp}"
        os.makedirs(export_dir, exist_ok=True)
        
        # 设置环境变量
        env = os.environ.copy()
        env['PGPASSWORD'] = self.source_config['password']
        
        # 导出命令
        dump_cmd = [
            'pg_dump',
            '-h', self.source_config['host'],
            '-p', str(self.source_config['port']),
            '-U', self.source_config['user'],
            '-d', self.source_config['database'],
            '--verbose',
            '--no-owner',
            '--no-privileges'
        ]
        
        backup_file = os.path.join(export_dir, 'full_backup.sql')
        
        try:
            print(f"📁 导出到: {backup_file}")
            with open(backup_file, 'w', encoding='utf-8') as f:
                result = subprocess.run(dump_cmd, stdout=f, stderr=subprocess.PIPE, 
                                      env=env, text=True)
            
            if result.returncode == 0:
                print("✅ 数据库导出成功")
                file_size = os.path.getsize(backup_file) / 1024 / 1024
                print(f"   文件大小: {file_size:.2f} MB")
                return backup_file
            else:
                print(f"❌ 导出失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"❌ 导出异常: {e}")
            return None
    
    def import_to_docker(self, sql_file):
        """导入到Docker数据库"""
        print(f"\n📊 开始导入到Docker数据库...")
        
        # 检查Docker容器状态
        try:
            result = subprocess.run(['docker', 'ps'], capture_output=True, text=True)
            if self.docker_config['container_name'] not in result.stdout:
                print("❌ Docker容器未运行")
                print("请先启动: docker-compose -f docker-compose.prod.yml up -d")
                return False
        except Exception as e:
            print(f"❌ 检查Docker状态失败: {e}")
            return False
        
        try:
            # 复制文件到容器
            print("📁 复制文件到Docker容器...")
            copy_cmd = ['docker', 'cp', sql_file, 
                       f"{self.docker_config['container_name']}:/tmp/import.sql"]
            subprocess.run(copy_cmd, check=True)
            
            # 导入数据
            print("📊 导入数据...")
            import_cmd = [
                'docker', 'exec', '-i', self.docker_config['container_name'],
                'psql', '-U', self.docker_config['user'], 
                '-d', self.docker_config['database'],
                '-f', '/tmp/import.sql'
            ]
            
            result = subprocess.run(import_cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ 数据导入成功")
                
                # 清理临时文件
                cleanup_cmd = ['docker', 'exec', self.docker_config['container_name'],
                              'rm', '/tmp/import.sql']
                subprocess.run(cleanup_cmd)
                
                return True
            else:
                print(f"❌ 导入失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 导入异常: {e}")
            return False
    
    def verify_migration(self):
        """验证迁移结果"""
        print("\n🔍 验证迁移结果...")
        
        # 获取源数据库表信息
        print("\n📊 源数据库:")
        source_tables = self.get_table_info(self.source_config)
        
        # 获取目标数据库表信息
        print("\n📊 目标数据库:")
        target_tables = self.get_table_info(self.docker_config)
        
        # 比较
        if len(source_tables) == len(target_tables):
            print("\n✅ 表数量匹配")
        else:
            print(f"\n⚠️  表数量不匹配: 源({len(source_tables)}) vs 目标({len(target_tables)})")
    
    def run_migration(self):
        """执行完整迁移流程"""
        print("🚀 数据库迁移工具")
        print("=" * 50)
        
        # 1. 测试源数据库连接
        if not self.test_connection(self.source_config, "源数据库"):
            print("请检查源数据库配置")
            return False
        
        # 2. 获取源数据库信息
        self.get_table_info(self.source_config)
        
        # 3. 导出数据库
        backup_file = self.export_database()
        if not backup_file:
            return False
        
        # 4. 测试目标数据库连接
        if not self.test_connection(self.docker_config, "Docker数据库"):
            print("请检查Docker数据库状态")
            return False
        
        # 5. 导入数据库
        if not self.import_to_docker(backup_file):
            return False
        
        # 6. 验证迁移结果
        self.verify_migration()
        
        print("\n🎉 数据库迁移完成!")
        return True

def main():
    # 检查依赖
    try:
        import psycopg2
    except ImportError:
        print("❌ 缺少依赖: pip install psycopg2-binary")
        sys.exit(1)
    
    # 检查pg_dump
    try:
        subprocess.run(['pg_dump', '--version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ 未找到pg_dump命令，请安装PostgreSQL客户端工具")
        sys.exit(1)
    
    # 运行迁移
    migrator = DatabaseMigrator()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == 'test':
            # 只测试连接
            migrator.test_connection(migrator.source_config, "源数据库")
            migrator.test_connection(migrator.docker_config, "Docker数据库")
        elif sys.argv[1] == 'export':
            # 只导出
            migrator.export_database()
        elif sys.argv[1] == 'import' and len(sys.argv) > 2:
            # 只导入
            migrator.import_to_docker(sys.argv[2])
        else:
            print("用法:")
            print("  python database_migrator.py          # 完整迁移")
            print("  python database_migrator.py test     # 测试连接")
            print("  python database_migrator.py export   # 只导出")
            print("  python database_migrator.py import <file>  # 只导入")
    else:
        # 完整迁移流程
        migrator.run_migration()

if __name__ == "__main__":
    main()
