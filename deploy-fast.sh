#!/bin/bash

# AI知识库完整部署脚本（集成镜像源加速）
# 结合原quick-fix.sh和镜像源加速配置
# 作者：AI助手
# 日期：$(date +%Y-%m-%d)

set -e

echo "🚀 AI知识库完整部署脚本（加速版）"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用root用户运行此脚本"
        exit 1
    fi
    log_success "Root权限检查通过"
}

# 检查必要文件
check_files() {
    log_info "检查必要文件..."

    if [ ! -f "docker-compose.prod.yml" ]; then
        log_error "docker-compose.prod.yml 文件不存在"
        exit 1
    fi

    if [ ! -f "nginx.prod.conf" ]; then
        log_error "nginx.prod.conf 文件不存在"
        exit 1
    fi

    if [ ! -d "ssl" ]; then
        log_warning "ssl 目录不存在，将创建临时目录"
        mkdir -p ssl
    fi

    log_success "必要文件检查通过"
}

# 检查Docker是否安装
check_docker() {
    log_info "检查Docker环境..."

    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi

    log_success "Docker环境检查通过"
}

# 配置Docker镜像源
configure_docker_mirrors() {
    log_info "配置Docker国内镜像源..."
    
    # 检查是否为Linux系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        DOCKER_CONFIG_DIR="/etc/docker"
        sudo mkdir -p $DOCKER_CONFIG_DIR
        
        if [ -f "docker-daemon.json" ]; then
            sudo cp docker-daemon.json $DOCKER_CONFIG_DIR/daemon.json
            log_success "Docker镜像源配置完成"
            
            # 重启Docker服务
            log_info "重启Docker服务..."
            sudo systemctl restart docker
            log_success "Docker服务重启完成"
        else
            log_warning "docker-daemon.json文件不存在，跳过Docker镜像源配置"
        fi
    else
        log_warning "非Linux系统，请手动配置Docker镜像源"
        log_info "请将docker-daemon.json的内容添加到Docker Desktop的设置中"
    fi
}

# 停止现有服务并清理
cleanup_services() {
    log_info "停止现有服务..."
    docker compose down 2>/dev/null || true
    docker compose -f docker-compose.prod.yml down 2>/dev/null || true

    log_info "清理Docker资源..."
    docker system prune -f
    docker builder prune -f

    log_success "服务清理完成"
}

# 检查前端构建
check_frontend_build() {
    log_info "检查前端构建..."

    # 检查前端目录是否存在
    if [ ! -d "frontend-app" ]; then
        log_error "frontend-app 目录不存在"
        exit 1
    fi

    # 检查package.json是否存在
    if [ ! -f "frontend-app/package.json" ]; then
        log_error "frontend-app/package.json 文件不存在"
        exit 1
    fi

    if [ -d "frontend-app/dist" ]; then
        log_success "发现已有前端构建产物"
    else
        log_info "使用Docker构建前端..."
        log_info "当前目录: $(pwd)"
        log_info "前端目录内容:"
        ls -la frontend-app/

        docker run --rm \
            -v "$(pwd)/frontend-app:/app" \
            -w /app \
            node:20-alpine \
            sh -c "ls -la && npm config set registry https://registry.npmmirror.com && npm install && npm run build-only"

        if [ ! -d "frontend-app/dist" ]; then
            log_error "前端构建失败"
            exit 1
        fi
        log_success "前端构建完成"
    fi
}

# 构建镜像
build_images() {
    log_info "开始构建Docker镜像..."

    # 使用BuildKit加速构建
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1

    # 构建镜像（使用no-cache确保使用最新配置）
    docker compose -f docker-compose.prod.yml build --no-cache

    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."

    # 启动服务
    docker compose -f docker-compose.prod.yml up -d

    log_success "服务启动完成"

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 90
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."

    # 检查容器状态
    docker compose -f docker-compose.prod.yml ps

    # 检查服务健康状态
    log_info "检查服务健康状态..."
    for i in {1..5}; do
        log_info "尝试 $i/5: 检查服务响应..."

        # 检查HTTP
        if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200\|301\|302"; then
            log_success "HTTP服务正常"
            break
        else
            log_info "HTTP服务还未就绪，等待10秒..."
            sleep 10
        fi
    done

    # 显示服务日志
    log_info "显示最近日志..."
    echo "=== 前端日志 ==="
    docker compose -f docker-compose.prod.yml logs frontend --tail=5
    echo ""
    echo "=== 后端日志 ==="
    docker compose -f docker-compose.prod.yml logs backend --tail=5
    echo ""
    echo "=== 数据库日志 ==="
    docker compose -f docker-compose.prod.yml logs postgres --tail=3

    # 显示容器状态
    echo ""
    log_info "容器详细状态:"
    docker compose -f docker-compose.prod.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
}

# 显示部署信息
show_deployment_info() {
    echo ""
    log_success "🎉 部署完成！"
    echo "=================================="
    echo "🌐 访问地址:"
    echo "  - HTTPS: https://aiknowledgebase.csicollege.cn"
    echo "  - HTTP: http://aiknowledgebase.csicollege.cn"
    echo "  - 本地: http://localhost"
    echo ""
    echo "📝 默认账户:"
    echo "  - 管理员: admin / testpass123"
    echo "  - 测试用户: testuser / testpass123"
    echo ""
    echo "🔧 管理命令:"
    echo "  查看日志: docker compose -f docker-compose.prod.yml logs -f"
    echo "  重启服务: docker compose -f docker-compose.prod.yml restart"
    echo "  停止服务: docker compose -f docker-compose.prod.yml down"
    echo ""
    echo "🔍 故障排除:"
    echo "  1. 如果无法访问，检查防火墙端口80和443"
    echo "  2. 如果SSL错误，检查证书文件在ssl/目录下"
    echo "  3. 查看详细日志: docker compose -f docker-compose.prod.yml logs -f [service_name]"
    echo ""
}

# 主函数
main() {
    log_info "AI知识库完整部署开始..."

    check_root
    check_files
    check_docker
    configure_docker_mirrors
    cleanup_services
    check_frontend_build
    build_images
    start_services
    check_services
    show_deployment_info

    log_success "部署完成！🎉"
}

# 执行主函数
main "$@"
