#!/bin/bash

# 简单部署脚本 - 不使用Docker
# 适用于网络环境较差的情况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要工具
check_requirements() {
    log_info "检查必要工具..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python &> /dev/null && ! command -v python3 &> /dev/null; then
        log_error "Python 未安装，请先安装 Python"
        exit 1
    fi
    
    # 检查nginx
    if ! command -v nginx &> /dev/null; then
        log_warn "Nginx 未安装，将使用简单的HTTP服务器"
    fi
    
    log_success "工具检查完成"
}

# 构建前端
build_frontend() {
    log_info "构建前端..."
    
    cd frontend
    
    # 安装依赖（如果node_modules不存在）
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        npm install --registry=https://registry.npmmirror.com
    fi
    
    # 构建
    log_info "构建前端应用..."
    npm run build
    
    cd ..
    log_success "前端构建完成"
}

# 准备后端
prepare_backend() {
    log_info "准备后端..."
    
    cd backend
    
    # 创建虚拟环境（如果不存在）
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python -m venv venv || python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate || source venv/Scripts/activate
    
    # 安装依赖
    log_info "安装后端依赖..."
    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
    
    cd ..
    log_success "后端准备完成"
}

# 创建简单的nginx配置
create_nginx_config() {
    log_info "创建Nginx配置..."
    
    cat > nginx.conf << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    server {
        listen 80;
        server_name localhost;
        
        # 前端静态文件
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        
        # API代理到后端
        location /api/ {
            proxy_pass http://127.0.0.1:8000/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
EOF
    
    log_success "Nginx配置创建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动后端
    log_info "启动后端服务..."
    cd backend
    source venv/bin/activate || source venv/Scripts/activate
    nohup python app.py > ../backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > ../backend.pid
    cd ..
    
    # 等待后端启动
    sleep 3
    
    # 检查后端是否启动成功
    if curl -s http://localhost:8000/health > /dev/null; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
    else
        log_error "后端服务启动失败"
        exit 1
    fi
    
    # 启动前端服务器
    log_info "启动前端服务..."
    if command -v nginx &> /dev/null; then
        # 使用nginx
        sudo cp -r frontend/dist/* /usr/share/nginx/html/
        sudo nginx -s reload || sudo nginx
        log_success "前端服务启动成功 (Nginx)"
    else
        # 使用简单的HTTP服务器
        cd frontend/dist
        nohup python -m http.server 80 > ../../frontend.log 2>&1 &
        FRONTEND_PID=$!
        echo $FRONTEND_PID > ../../frontend.pid
        cd ../..
        log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
    fi
    
    log_success "所有服务启动完成！"
    log_info "访问地址: http://localhost"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    # 停止后端
    if [ -f "backend.pid" ]; then
        BACKEND_PID=$(cat backend.pid)
        kill $BACKEND_PID 2>/dev/null || true
        rm backend.pid
        log_info "后端服务已停止"
    fi
    
    # 停止前端
    if [ -f "frontend.pid" ]; then
        FRONTEND_PID=$(cat frontend.pid)
        kill $FRONTEND_PID 2>/dev/null || true
        rm frontend.pid
        log_info "前端服务已停止"
    fi
    
    log_success "所有服务已停止"
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            log_info "开始简单部署..."
            check_requirements
            build_frontend
            prepare_backend
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 2
            main start
            ;;
        *)
            echo "用法: $0 {start|stop|restart}"
            exit 1
            ;;
    esac
}

main "$@"
