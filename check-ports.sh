#!/bin/bash

# 端口检查和清理脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口占用
check_port() {
    local port=$1
    local service_name=$2
    
    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        local pid=$(netstat -tlnp 2>/dev/null | grep ":$port " | awk '{print $7}' | cut -d'/' -f1 | head -1)
        local process=$(ps -p $pid -o comm= 2>/dev/null || echo "unknown")
        log_warn "端口 $port 被占用 ($service_name) - 进程: $process (PID: $pid)"
        return 1
    else
        log_success "端口 $port 可用 ($service_name)"
        return 0
    fi
}

# 检查所有需要的端口
check_all_ports() {
    log_info "检查端口占用情况..."
    
    local ports=(
        "8080:前端服务"
        "8000:后端API"
        "5433:PostgreSQL"
        "6380:Redis"
        "8443:HTTPS"
    )
    
    local conflicts=0
    
    for port_info in "${ports[@]}"; do
        local port=$(echo $port_info | cut -d':' -f1)
        local service=$(echo $port_info | cut -d':' -f2)
        
        if ! check_port $port "$service"; then
            ((conflicts++))
        fi
    done
    
    if [ $conflicts -eq 0 ]; then
        log_success "所有端口都可用！"
        return 0
    else
        log_error "发现 $conflicts 个端口冲突"
        return 1
    fi
}

# 停止占用端口的Docker容器
stop_docker_containers() {
    log_info "停止可能冲突的Docker容器..."
    
    # 停止当前项目的容器
    docker-compose -f docker-compose.prod.yml down 2>/dev/null || true
    
    # 查找并停止占用目标端口的容器
    local ports=(8080 8000 5433 6380 8443)
    
    for port in "${ports[@]}"; do
        local container_ids=$(docker ps --format "table {{.ID}}\t{{.Ports}}" | grep ":$port->" | awk '{print $1}' || true)
        
        if [ -n "$container_ids" ]; then
            log_info "停止占用端口 $port 的容器..."
            echo "$container_ids" | xargs -r docker stop
        fi
    done
    
    log_success "Docker容器清理完成"
}

# 显示端口占用详情
show_port_details() {
    log_info "详细端口占用信息:"
    echo
    
    local ports=(8080 8000 5433 6380 8443 80 443 5432 6379)
    
    for port in "${ports[@]}"; do
        echo "端口 $port:"
        if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            netstat -tlnp 2>/dev/null | grep ":$port " | while read line; do
                echo "  $line"
            done
        else
            echo "  未被占用"
        fi
        echo
    done
}

# 建议解决方案
suggest_solutions() {
    log_info "解决端口冲突的建议:"
    echo
    echo "1. 停止系统服务:"
    echo "   sudo systemctl stop nginx    # 如果nginx占用80端口"
    echo "   sudo systemctl stop apache2  # 如果apache占用80端口"
    echo "   sudo systemctl stop postgresql # 如果PostgreSQL占用5432端口"
    echo "   sudo systemctl stop redis     # 如果Redis占用6379端口"
    echo
    echo "2. 或者使用不同端口 (已在docker-compose.prod.yml中配置):"
    echo "   前端: http://localhost:8080"
    echo "   后端: http://localhost:8000"
    echo "   PostgreSQL: localhost:5433"
    echo "   Redis: localhost:6380"
    echo
    echo "3. 强制清理Docker容器:"
    echo "   $0 clean-docker"
    echo
}

# 清理Docker资源
clean_docker() {
    log_info "清理Docker资源..."
    
    # 停止所有容器
    docker stop $(docker ps -aq) 2>/dev/null || true
    
    # 删除所有容器
    docker rm $(docker ps -aq) 2>/dev/null || true
    
    # 清理网络
    docker network prune -f
    
    # 清理卷
    docker volume prune -f
    
    # 清理镜像
    docker image prune -f
    
    log_success "Docker资源清理完成"
}

# 主函数
main() {
    case "${1:-check}" in
        "check")
            check_all_ports
            if [ $? -ne 0 ]; then
                echo
                suggest_solutions
            fi
            ;;
        "details")
            show_port_details
            ;;
        "clean-docker")
            stop_docker_containers
            ;;
        "clean-all")
            clean_docker
            ;;
        "suggest")
            suggest_solutions
            ;;
        *)
            echo "用法: $0 {check|details|clean-docker|clean-all|suggest}"
            echo
            echo "  check        - 检查端口占用情况"
            echo "  details      - 显示详细端口信息"
            echo "  clean-docker - 停止冲突的Docker容器"
            echo "  clean-all    - 清理所有Docker资源"
            echo "  suggest      - 显示解决建议"
            exit 1
            ;;
    esac
}

main "$@"
