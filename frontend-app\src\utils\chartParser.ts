interface ChartData {
  type: 'pie' | 'bar' | 'line' | 'doughnut' | 'radar' | 'polarArea'
  title?: string
  data: {
    labels: string[]
    datasets: Array<{
      label?: string
      data: number[]
      backgroundColor?: string | string[]
      borderColor?: string | string[]
      borderWidth?: number
    }>
  }
  options?: any
}

/**
 * 解析AI生成的图表代码块
 * 支持多种格式：JSON、JavaScript对象、ECharts配置、简化的数据格式
 */
export function parseChartCode(code: string): ChartData | null {
  try {
    // 清理代码，移除注释和多余空白
    const cleanCode = code
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .trim()

    // 优先检查是否为ECharts配置
    if (cleanCode.includes('option') || cleanCode.includes('series') || cleanCode.includes('xAxis') || cleanCode.includes('yAxis') || cleanCode.includes('title')) {
      const echartsResult = parseEChartsOption(cleanCode);
      if (echartsResult) {
        return echartsResult;
      }
    }

    // 尝试解析为JSON
    if (cleanCode.startsWith('{') && cleanCode.endsWith('}')) {
      try {
        const parsed = JSON.parse(cleanCode)

        // 检查是否为ECharts配置
        if (isEChartsOption(parsed)) {
          return convertEChartsToChartJS(parsed)
        }

        return validateAndNormalizeChartData(parsed)
      } catch {
        // 如果JSON解析失败，尝试作为JavaScript对象
        const jsResult = parseJavaScriptObject(cleanCode);
        if (jsResult) return jsResult;

        // 最后尝试作为ECharts option
        return parseEChartsOption(cleanCode);
      }
    }

    // 尝试解析 Python matplotlib 代码
    if (cleanCode.includes('matplotlib') || cleanCode.includes('plt.')) {
      return parsePythonMatplotlib(cleanCode)
    }

    // 尝试解析简化格式
    return parseSimplifiedFormat(cleanCode)
  } catch (error) {
    console.error('图表解析错误:', error)
    return null
  }
}

/**
 * 解析JavaScript对象格式
 */
function parseJavaScriptObject(code: string): ChartData | null {
  try {
    // 使用Function构造器安全地执行代码
    const func = new Function(`return ${code}`)
    const result = func()
    return validateAndNormalizeChartData(result)
  } catch (error) {
    console.error('JavaScript对象解析错误:', error)
    return null
  }
}

/**
 * 解析 Python matplotlib 代码
 */
function parsePythonMatplotlib(code: string): ChartData | null {
  try {
    console.log('尝试解析 Python matplotlib 代码:', code)

    // 提取标题
    let title = ''
    const titleMatch = code.match(/plt\.title\(['"]([^'"]+)['"]\)/)
    if (titleMatch) {
      title = titleMatch[1]
    }

    // 提取 xlabel 和 ylabel
    let xlabel = ''
    let ylabel = ''
    const xlabelMatch = code.match(/plt\.xlabel\(['"]([^'"]+)['"]\)/)
    const ylabelMatch = code.match(/plt\.ylabel\(['"]([^'"]+)['"]\)/)
    if (xlabelMatch) {
      xlabel = xlabelMatch[1]
    }
    if (ylabelMatch) {
      ylabel = ylabelMatch[1]
    }

    // 提取标签数据
    let labels: string[] = []
    const labelsMatch = code.match(/labels\s*=\s*\[([^\]]+)\]/)
    if (labelsMatch) {
      labels = labelsMatch[1]
        .split(',')
        .map(s => s.trim().replace(/['"]/g, ''))
        .filter(Boolean)
    }

    // 提取数值数据
    let data: number[] = []
    const dataMatches = [
      code.match(/data\s*=\s*\[([^\]]+)\]/),
      code.match(/values\s*=\s*\[([^\]]+)\]/),
      code.match(/plt\.(?:bar|plot|pie)\([^,]*,\s*\[([^\]]+)\]/),
    ]

    for (const match of dataMatches) {
      if (match) {
        data = match[1]
          .split(',')
          .map(s => parseFloat(s.trim()))
          .filter(n => !isNaN(n))
        break
      }
    }

    // 确定图表类型
    let chartType: ChartData['type'] = 'bar'
    if (code.includes('plt.pie')) {
      chartType = 'pie'
    } else if (code.includes('plt.plot')) {
      chartType = 'line'
    } else if (code.includes('plt.bar')) {
      chartType = 'bar'
    }

    // 如果没有找到标签，生成默认标签
    if (labels.length === 0 && data.length > 0) {
      labels = data.map((_, index) => `项目${index + 1}`)
    }

    // 验证数据
    if (data.length === 0) {
      console.log('未找到有效的数据')
      return null
    }

    const chartData: ChartData = {
      type: chartType,
      title: title || undefined,
      data: {
        labels: labels.slice(0, data.length),
        datasets: [{
          label: ylabel || '数据',
          data: data,
          backgroundColor: chartType === 'pie' ?
            ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'] :
            '#36A2EB',
          borderColor: chartType === 'line' ? '#36A2EB' : 'rgba(255, 255, 255, 0.8)',
          borderWidth: 1,
          pointRadius: chartType === 'line' ? 0 : undefined, // 移除折线图的圆点
          pointHoverRadius: chartType === 'line' ? 4 : undefined
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: chartType !== 'line', // 折线图不显示图例
            position: 'top' as const
          }
        },
        scales: chartType !== 'pie' ? {
          x: {
            title: {
              display: !!xlabel,
              text: xlabel,
              font: {
                size: 14,
                weight: 'bold'
              },
              color: '#333'
            }
          },
          y: {
            title: {
              display: !!ylabel,
              text: ylabel,
              font: {
                size: 14,
                weight: 'bold'
              },
              color: '#333'
            }
          }
        } : undefined
      }
    }

    return chartData
  } catch (error) {
    console.error('Python matplotlib 解析错误:', error)
    return null
  }
}

/**
 * 解析简化格式（如CSV样式的数据）
 */
function parseSimplifiedFormat(code: string): ChartData | null {
  const lines = code.split('\n').filter(line => line.trim())
  
  if (lines.length < 2) return null

  // 检查是否包含图表类型声明
  let chartType: ChartData['type'] = 'bar'
  let title = ''
  let dataStartIndex = 0

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].toLowerCase().trim()
    if (line.includes('type:') || line.includes('图表类型:')) {
      const typeMatch = line.match(/(pie|bar|line|doughnut|radar|polararea)/i)
      if (typeMatch) {
        chartType = typeMatch[1].toLowerCase() as ChartData['type']
      }
      dataStartIndex = i + 1
    } else if (line.includes('title:') || line.includes('标题:')) {
      title = line.split(':')[1]?.trim() || ''
      dataStartIndex = i + 1
    } else if (line.includes(',') || line.includes('\t')) {
      // 找到数据行
      break
    }
  }

  // 解析数据
  const dataLines = lines.slice(dataStartIndex)
  if (dataLines.length < 2) return null

  // 第一行作为标签
  const labels = dataLines[0].split(/[,\t]/).map(s => s.trim()).filter(Boolean)
  
  // 后续行作为数据
  const datasets = []
  for (let i = 1; i < dataLines.length; i++) {
    const values = dataLines[i].split(/[,\t]/)
    const label = values[0]?.trim()
    const data = values.slice(1).map(v => parseFloat(v.trim())).filter(n => !isNaN(n))
    
    if (data.length > 0) {
      datasets.push({
        label: label || `数据系列 ${i}`,
        data
      })
    }
  }

  if (datasets.length === 0) return null

  return {
    type: chartType,
    title: title || undefined,
    data: {
      labels: labels.slice(0, Math.max(...datasets.map(d => d.data.length))),
      datasets
    }
  }
}

/**
 * 验证和标准化图表数据
 */
function validateAndNormalizeChartData(data: any): ChartData | null {
  if (!data || typeof data !== 'object') return null

  // 检查必需字段
  if (!data.type || !data.data || !data.data.labels || !data.data.datasets) {
    return null
  }

  // 验证图表类型
  const validTypes = ['pie', 'bar', 'line', 'doughnut', 'radar', 'polarArea']
  if (!validTypes.includes(data.type)) {
    data.type = 'bar' // 默认类型
  }

  // 验证数据格式
  if (!Array.isArray(data.data.labels) || !Array.isArray(data.data.datasets)) {
    return null
  }

  // 标准化数据集
  data.data.datasets = data.data.datasets.map((dataset: any) => ({
    label: dataset.label || '数据',
    data: Array.isArray(dataset.data) ? dataset.data.map(Number).filter(n => !isNaN(n)) : [],
    backgroundColor: dataset.backgroundColor,
    borderColor: dataset.borderColor,
    borderWidth: dataset.borderWidth
  })).filter((dataset: any) => dataset.data.length > 0)

  if (data.data.datasets.length === 0) return null

  return data as ChartData
}

/**
 * 从文本中提取图表代码块
 */
export function extractChartBlocks(text: string): string[] {
  const chartBlocks: string[] = []

  console.log('开始提取图表代码块，输入文本:', text)

  // 重置正则表达式的lastIndex
  const resetRegex = (regex: RegExp) => {
    regex.lastIndex = 0
    return regex
  }

  // 只匹配明确标记为图表的代码块
  const chartCodeBlockPatterns = [
    // 明确标记为图表的代码块
    resetRegex(/```(?:json|chart|图表|echarts|option)\s*\n([\s\S]*?)\n```/gi),
    // JavaScript代码块中的图表配置
    resetRegex(/```(?:javascript|js)\s*\n([\s\S]*?)\n```/gi),
  ]

  // 首先尝试从明确标记的代码块中提取
  for (const pattern of chartCodeBlockPatterns) {
    let match
    while ((match = pattern.exec(text)) !== null) {
      const code = match[1].trim()
      console.log('找到标记的代码块:', code.substring(0, 100) + '...')

      // 对于明确标记的代码块，进行更严格的验证
      if (isCompleteChartConfig(code)) {
        console.log('确认为完整图表配置，添加到结果中')
        chartBlocks.push(code)
      } else {
        console.log('不是完整图表配置，跳过')
      }
    }
    // 重置正则表达式状态
    pattern.lastIndex = 0
  }

  // 如果没有找到明确标记的代码块，尝试HTML中的script标签
  if (chartBlocks.length === 0) {
    const scriptPattern = resetRegex(/<script[^>]*>([\s\S]*?)<\/script>/gi)
    let match
    while ((match = scriptPattern.exec(text)) !== null) {
      const code = match[1].trim()
      console.log('找到script标签内容:', code.substring(0, 100) + '...')

      if (isCompleteChartConfig(code)) {
        console.log('确认为完整图表配置，添加到结果中')
        chartBlocks.push(code)
      }
    }
  }

  // 如果没有找到明确标记的代码块，尝试查找特定的变量赋值模式
  if (chartBlocks.length === 0) {
    console.log('没有找到明确标记的代码块，尝试查找变量赋值模式')

    // 查找option = { ... } 模式（ECharts）
    const optionMatch = text.match(/option\s*=\s*({[\s\S]*?});?\s*(?:\n|$)/gi)
    if (optionMatch) {
      for (const match of optionMatch) {
        const objMatch = match.match(/{[\s\S]*}/);
        if (objMatch) {
          const code = objMatch[0];
          console.log('找到option变量赋值:', code.substring(0, 100) + '...')
          if (isCompleteChartConfig(code)) {
            console.log('确认为完整ECharts配置，添加到结果中')
            chartBlocks.push(code)
          }
        }
      }
    }

    // 查找chartConfig = { ... } 模式（Chart.js）
    const chartConfigMatch = text.match(/(?:chartConfig|config|chartOptions)\s*=\s*({[\s\S]*?});?\s*(?:\n|$)/gi)
    if (chartConfigMatch) {
      for (const match of chartConfigMatch) {
        const objMatch = match.match(/{[\s\S]*}/);
        if (objMatch) {
          const code = objMatch[0];
          console.log('找到图表配置变量赋值:', code.substring(0, 100) + '...')
          if (isCompleteChartConfig(code)) {
            console.log('确认为完整Chart.js配置，添加到结果中')
            chartBlocks.push(code)
          }
        }
      }
    }
  }

  console.log('最终提取的图表代码块:', chartBlocks)
  return chartBlocks
}

/**
 * 判断是否为ECharts配置对象
 */
function isEChartsOption(obj: any): boolean {
  if (!obj || typeof obj !== 'object') return false

  // 检查ECharts特有的属性
  const echartsKeys = ['series', 'xAxis', 'yAxis', 'legend', 'tooltip', 'grid', 'title']
  return echartsKeys.some(key => obj.hasOwnProperty(key))
}

/**
 * 解析ECharts option代码
 */
function parseEChartsOption(code: string): ChartData | null {
  try {
    console.log('尝试解析ECharts代码:', code);

    // 清理代码
    let cleanCode = code.trim();

    // 提取option对象的多种模式
    let optionStr = '';

    // 模式1: option = { ... }
    let optionMatch = cleanCode.match(/option\s*=\s*({[\s\S]*})/);
    if (optionMatch) {
      optionStr = optionMatch[1];
    }
    // 模式2: 直接是对象 { ... }
    else if (cleanCode.startsWith('{') && cleanCode.endsWith('}')) {
      optionStr = cleanCode;
    }
    // 模式3: 包含option的完整代码
    else if (cleanCode.includes('option')) {
      const match = cleanCode.match(/{[\s\S]*}/);
      if (match) {
        optionStr = match[0];
      }
    }

    if (!optionStr) {
      console.log('未找到有效的option对象');
      return null;
    }

    console.log('提取的option字符串:', optionStr);

    // 尝试解析为JavaScript对象
    let option;
    try {
      // 使用Function构造器更安全地解析
      option = new Function('return ' + optionStr)();
    } catch (e) {
      console.log('Function解析失败，尝试eval:', e);
      option = eval(`(${optionStr})`);
    }

    console.log('解析后的option对象:', option);

    return convertEChartsToChartJS(option);
  } catch (error) {
    console.error('ECharts解析错误:', error);
    return null;
  }
}

/**
 * 将ECharts配置转换为Chart.js格式
 */
function convertEChartsToChartJS(echartsOption: any): ChartData | null {
  try {
    console.log('开始转换ECharts配置:', echartsOption);

    if (!echartsOption.series || !Array.isArray(echartsOption.series)) {
      console.log('没有找到series数组');
      return null;
    }

    const firstSeries = echartsOption.series[0];
    const chartType = mapEChartsTypeToChartJS(firstSeries.type);

    console.log('图表类型:', chartType, '原始类型:', firstSeries.type);

    // 提取标题
    const title = echartsOption.title?.text || '';

    // 提取数据和标签
    let labels: string[] = [];
    let datasets: any[] = [];

    if (firstSeries.type === 'pie') {
      // 饼图处理
      labels = firstSeries.data?.map((item: any) => item.name || '') || [];
      datasets = [{
        label: firstSeries.name || '数据',
        data: firstSeries.data?.map((item: any) => item.value || 0) || [],
        backgroundColor: generateColors(labels.length)
      }];
    } else {
      // 其他图表类型
      // 处理X轴数据
      if (echartsOption.xAxis) {
        if (Array.isArray(echartsOption.xAxis)) {
          labels = echartsOption.xAxis[0]?.data || [];
        } else {
          labels = echartsOption.xAxis.data || [];
        }
      } else if (firstSeries.data && Array.isArray(firstSeries.data)) {
        labels = firstSeries.data.map((_: any, index: number) => `项目${index + 1}`);
      }

      console.log('提取的标签:', labels);

      // 处理系列数据
      datasets = echartsOption.series.map((series: any, index: number) => {
        const seriesData = Array.isArray(series.data) ? series.data : [];
        console.log(`系列${index}数据:`, seriesData);

        return {
          label: series.name || `系列${index + 1}`,
          data: seriesData,
          backgroundColor: series.itemStyle?.color || generateColors(1)[0],
          borderColor: series.itemStyle?.borderColor || series.itemStyle?.color || generateColors(1)[0],
          borderWidth: 2,
          fill: chartType === 'line' ? false : true
        };
      });
    }

    console.log('转换后的数据集:', datasets);

    const result = {
      type: chartType,
      title,
      data: {
        labels,
        datasets
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: true,
            position: 'top' as const
          },
          title: {
            display: !!title,
            text: title
          }
        },
        scales: chartType !== 'pie' ? {
          y: {
            beginAtZero: true
          }
        } : undefined
      }
    };

    console.log('最终转换结果:', result);
    return result;
  } catch (error) {
    console.error('ECharts转换错误:', error);
    return null;
  }
}

/**
 * 映射ECharts图表类型到Chart.js类型
 */
function mapEChartsTypeToChartJS(echartsType: string): string {
  const typeMap: { [key: string]: string } = {
    'line': 'line',
    'bar': 'bar',
    'pie': 'pie',
    'scatter': 'scatter',
    'radar': 'radar',
    'doughnut': 'doughnut'
  };

  return typeMap[echartsType] || 'bar';
}

/**
 * 生成颜色数组
 */
function generateColors(count: number): string[] {
  const colors = [
    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF',
    '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
  ];

  const result = [];
  for (let i = 0; i < count; i++) {
    result.push(colors[i % colors.length]);
  }
  return result;
}

/**
 * 判断代码是否为完整的图表配置
 */
function isCompleteChartConfig(code: string): boolean {
  // 首先排除CSS样式
  if (isCSSCode(code)) {
    console.log('检测到CSS代码，跳过')
    return false
  }

  // 排除太短的代码片段
  if (code.length < 100) {
    console.log('代码片段太短，跳过')
    return false
  }

  const lowerCode = code.toLowerCase()

  // 必须是JSON对象格式
  if (!code.trim().startsWith('{') || !code.trim().endsWith('}')) {
    console.log('不是JSON对象格式，跳过')
    return false
  }

  // 检查Chart.js完整配置结构
  const hasChartJSStructure = (lowerCode.includes('"type":') || lowerCode.includes("'type':")) &&
                             (lowerCode.includes('"data":') || lowerCode.includes("'data':")) &&
                             (lowerCode.includes('"datasets":') || lowerCode.includes("'datasets':")) &&
                             (lowerCode.includes('"options":') || lowerCode.includes("'options':"))

  // 检查ECharts完整配置结构
  const hasEChartsStructure = (lowerCode.includes('series') && lowerCode.includes('data')) &&
                             (lowerCode.includes('xaxis') || lowerCode.includes('yaxis') ||
                              lowerCode.includes('title') || lowerCode.includes('legend'))

  if (!hasChartJSStructure && !hasEChartsStructure) {
    console.log('不包含完整的图表配置结构，跳过')
    return false
  }

  // 排除只包含数据片段的对象
  const isDataFragment = lowerCode.match(/^\s*{\s*"data"\s*:\s*\[.*?\]\s*}\s*$/i) ||
                         lowerCode.match(/^\s*{\s*'data'\s*:\s*\[.*?\]\s*}\s*$/i) ||
                         lowerCode.match(/^\s*{\s*"data"\s*:\s*\[.*?\]\s*,\s*"labels"\s*:\s*\[.*?\]\s*}\s*$/i)

  if (isDataFragment) {
    console.log('检测到数据片段，不是完整配置，跳过')
    return false
  }

  // 确保包含足够的配置信息（至少300字符）
  if (code.length < 300) {
    console.log('配置信息不够完整，跳过')
    return false
  }

  console.log('确认为完整的图表配置')
  return true
}

/**
 * 判断代码是否为CSS样式
 */
function isCSSCode(code: string): boolean {
  const cssKeywords = [
    'font-family:', 'display:', 'flex-direction:', 'align-items:', 'justify-content:',
    'min-height:', 'margin:', 'background-color:', 'width:', 'max-width:',
    'padding:', 'border-radius:', 'box-shadow:', 'color:', 'margin-bottom:',
    'rgba(', 'px;', 'vh;', '%', '#fff', '#f4f4f4', '#333'
  ]

  const lowerCode = code.toLowerCase()

  // 如果包含多个CSS属性，很可能是CSS
  const cssCount = cssKeywords.filter(keyword => lowerCode.includes(keyword)).length

  return cssCount >= 3 // 包含3个或以上CSS关键词就认为是CSS
}

/**
 * 生成示例图表数据
 */
export function generateExampleChart(type: ChartData['type'] = 'bar'): ChartData {
  const examples = {
    pie: {
      type: 'pie' as const,
      title: '销售占比',
      data: {
        labels: ['产品A', '产品B', '产品C', '产品D'],
        datasets: [{
          label: '销售额',
          data: [30, 25, 20, 25]
        }]
      }
    },
    bar: {
      type: 'bar' as const,
      title: '月度销售额',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
          label: '销售额(万元)',
          data: [12, 19, 15, 25, 22, 30]
        }]
      }
    },
    line: {
      type: 'line' as const,
      title: '用户增长趋势',
      data: {
        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
        datasets: [{
          label: '用户数量',
          data: [100, 150, 200, 280, 350, 420]
        }]
      }
    }
  }
  
  return examples[type] || examples.bar
}
