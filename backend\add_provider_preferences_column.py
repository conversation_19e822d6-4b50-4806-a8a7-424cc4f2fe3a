#!/usr/bin/env python3
"""
添加provider_preferences字段到user_settings表
"""
import asyncio
from sqlmodel import create_engine, text
from app.config import settings

def add_provider_preferences_column():
    """添加provider_preferences字段"""
    # 创建数据库引擎
    engine = create_engine(settings.database_url.replace('asyncpg', 'psycopg2'), echo=True)
    
    # 添加字段
    with engine.connect() as connection:
        try:
            # 检查字段是否已存在
            result = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name='user_settings' AND column_name='provider_preferences'
            """))
            
            if result.fetchone() is None:
                # 字段不存在，添加它
                connection.execute(text("""
                    ALTER TABLE user_settings 
                    ADD COLUMN provider_preferences TEXT
                """))
                connection.commit()
                print("provider_preferences字段添加成功！")
            else:
                print("provider_preferences字段已存在，跳过添加。")
                
        except Exception as e:
            print(f"添加字段失败: {e}")
            connection.rollback()

if __name__ == "__main__":
    add_provider_preferences_column()
