#!/bin/bash

# SSL证书设置脚本

echo "🔐 设置SSL证书..."

# 创建SSL目录
mkdir -p ssl

# 检查证书文件是否存在
if [ ! -f "aiknowledgebase.csicollege.cn/full_chain.pem" ]; then
    echo "❌ 未找到SSL证书文件: aiknowledgebase.csicollege.cn/full_chain.pem"
    echo "请确保证书文件存在于正确位置"
    exit 1
fi

if [ ! -f "aiknowledgebase.csicollege.cn/private.key" ]; then
    echo "❌ 未找到SSL私钥文件: aiknowledgebase.csicollege.cn/private.key"
    echo "请确保私钥文件存在于正确位置"
    exit 1
fi

# 复制证书文件到ssl目录
echo "📋 复制SSL证书文件..."
cp "aiknowledgebase.csicollege.cn/full_chain.pem" ssl/
cp "aiknowledgebase.csicollege.cn/private.key" ssl/

# 设置正确的权限
chmod 644 ssl/full_chain.pem
chmod 600 ssl/private.key

echo "✅ SSL证书设置完成！"
echo "证书文件位置："
echo "  - 证书: ssl/full_chain.pem"
echo "  - 私钥: ssl/private.key"

# 验证证书
echo "🔍 验证证书信息..."
openssl x509 -in ssl/full_chain.pem -text -noout | grep -E "(Subject:|Not After)"
