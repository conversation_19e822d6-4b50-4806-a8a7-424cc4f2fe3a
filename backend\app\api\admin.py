"""
管理员功能API
"""
import json
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlmodel import Session, select, func
from app.core.database import SessionDep
from app.core.auth import get_current_admin_user
from app.models.user import User, UserQuota
from app.models.knowledge_base import KnowledgeBase, Document
from app.models.system import OperationLog, SystemSetting
from app.models.ai import AIProvider, AIModel, ChatSession, ChatMessage
from passlib.context import CryptContext
from app.schemas.admin import (
    UserAdminResponse,
    UserStatusUpdate,
    UserCreateRequest,
    UserUpdateRequest,
    UserQuotaUpdate,
    UserQuotaResponse,
    SystemStats,
    OperationLogResponse,
    SystemSettingUpdate,
    SystemSettingResponse,
    AIModelAdminResponse,
    AIModelUpdateRequest,
    AIProviderResponse
)

router = APIRouter()

# 密码加密
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


@router.get("/stats", response_model=SystemStats)
async def get_system_stats(
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """获取系统统计信息"""
    # 统计用户数量
    user_count = session.exec(select(func.count(User.id))).first()

    # 统计知识库数量
    kb_count = session.exec(select(func.count(KnowledgeBase.id))).first()

    # 统计文档数量
    doc_count = session.exec(select(func.count(Document.id))).first()

    # 统计聊天会话数量
    session_count = session.exec(select(func.count(ChatSession.id))).first()

    # 统计消息数量
    message_count = session.exec(select(func.count(ChatMessage.id))).first()

    # 统计今日活跃用户（假设有last_login_at字段）
    from datetime import datetime, timedelta
    today = datetime.utcnow().date()
    active_users_today = session.exec(
        select(func.count(User.id)).where(
            func.date(User.last_login_at) == today
        )
    ).first()

    # 统计存储使用量
    storage_result = session.exec(select(func.sum(Document.file_size))).first()
    total_storage_mb = (storage_result or 0) / (1024 * 1024)

    return SystemStats(
        total_users=user_count or 0,
        total_knowledge_bases=kb_count or 0,
        total_documents=doc_count or 0,
        total_chat_sessions=session_count or 0,
        total_messages=message_count or 0,
        active_users_today=active_users_today or 0,
        total_storage_mb=round(total_storage_mb, 2)
    )


@router.get("/users", response_model=List[UserAdminResponse])
async def get_all_users(
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep,
    offset: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    status_filter: Optional[str] = Query(None)
):
    """获取所有用户列表"""
    statement = select(User).offset(offset).limit(limit)
    
    if status_filter:
        statement = statement.where(User.status == status_filter)
    
    users = session.exec(statement).all()
    return users


@router.put("/users/{user_id}/status")
async def update_user_status(
    user_id: int,
    status_update: UserStatusUpdate,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """更新用户状态"""
    # 查找用户
    statement = select(User).where(User.id == user_id)
    user = session.exec(statement).first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 不能禁用自己
    if user.id == admin_user.id and status_update.status == "disabled":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能禁用自己的账户"
        )
    
    # 更新状态
    old_status = user.status
    user.status = status_update.status
    session.add(user)
    session.commit()
    
    # 记录操作日志
    log_entry = OperationLog(
        user_id=admin_user.id,
        action="update_user_status",
        target_type="user",
        target_id=user.id,
        details=json.dumps({
            "old_status": old_status,
            "new_status": status_update.status
        })
    )
    session.add(log_entry)
    session.commit()
    
    return {"message": "用户状态更新成功"}


@router.post("/users", response_model=UserAdminResponse)
async def create_user(
    user_data: UserCreateRequest,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """创建新用户"""
    # 检查用户名是否已存在
    existing_user = session.exec(
        select(User).where(User.username == user_data.username)
    ).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )

    # 检查邮箱是否已存在
    existing_email = session.exec(
        select(User).where(User.email == user_data.email)
    ).first()
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )

    # 创建新用户
    hashed_password = pwd_context.hash(user_data.password)
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        password_hash=hashed_password,
        display_name=user_data.display_name,
        is_admin=user_data.is_admin,
        status="active"
    )

    session.add(new_user)
    session.commit()
    session.refresh(new_user)

    # 获取系统默认设置
    from app.models.system import SystemSetting
    from app.models.user import UserSettings

    # 获取默认存储配额
    default_storage_setting = session.exec(
        select(SystemSetting).where(SystemSetting.key == "default_storage_quota")
    ).first()
    default_storage_mb = int(default_storage_setting.value) if default_storage_setting else 1024

    # 获取默认模型设置
    default_model_setting = session.exec(
        select(SystemSetting).where(SystemSetting.key == "default_model_id")
    ).first()
    default_model_id = int(default_model_setting.value) if default_model_setting and default_model_setting.value else None

    # 创建用户配额（应用系统默认设置）
    user_quota = UserQuota(
        user_id=new_user.id,
        max_kbs=5,
        max_docs_per_kb=100,
        max_storage_mb=default_storage_mb
    )
    session.add(user_quota)

    # 创建用户设置（应用系统默认设置）
    user_settings = UserSettings(
        user_id=new_user.id,
        default_model_id=default_model_id
    )
    session.add(user_settings)

    session.commit()

    # 记录操作日志
    log_entry = OperationLog(
        user_id=admin_user.id,
        action="create_user",
        target_type="user",
        target_id=new_user.id,
        details=json.dumps({
            "username": user_data.username,
            "email": user_data.email,
            "is_admin": user_data.is_admin
        })
    )
    session.add(log_entry)
    session.commit()

    return new_user


@router.put("/users/{user_id}", response_model=UserAdminResponse)
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """更新用户信息"""
    # 查找用户
    user = session.exec(select(User).where(User.id == user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 记录原始值
    old_values = {
        "username": user.username,
        "email": user.email,
        "display_name": user.display_name,
        "is_admin": user.is_admin,
        "status": user.status
    }

    # 更新字段
    if user_data.username is not None:
        # 检查用户名是否已被其他用户使用
        existing_user = session.exec(
            select(User).where(
                User.username == user_data.username,
                User.id != user_id
            )
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        user.username = user_data.username

    if user_data.email is not None:
        # 检查邮箱是否已被其他用户使用
        existing_email = session.exec(
            select(User).where(
                User.email == user_data.email,
                User.id != user_id
            )
        ).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
        user.email = user_data.email

    if user_data.display_name is not None:
        user.display_name = user_data.display_name

    if user_data.is_admin is not None:
        # 不能取消自己的管理员权限
        if user.id == admin_user.id and not user_data.is_admin:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能取消自己的管理员权限"
            )
        user.is_admin = user_data.is_admin

    if user_data.status is not None:
        # 不能禁用自己
        if user.id == admin_user.id and user_data.status == "disabled":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能禁用自己的账户"
            )
        user.status = user_data.status

    session.add(user)
    session.commit()
    session.refresh(user)

    # 记录操作日志
    new_values = {
        "username": user.username,
        "email": user.email,
        "display_name": user.display_name,
        "is_admin": user.is_admin,
        "status": user.status
    }

    log_entry = OperationLog(
        user_id=admin_user.id,
        action="update_user",
        target_type="user",
        target_id=user.id,
        details=json.dumps({
            "old_values": old_values,
            "new_values": new_values
        })
    )
    session.add(log_entry)
    session.commit()

    return user


@router.delete("/users/{user_id}")
async def delete_user(
    user_id: int,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """删除用户"""
    # 查找用户
    user = session.exec(select(User).where(User.id == user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 不能删除自己
    if user.id == admin_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不能删除自己的账户"
        )

    # 记录用户信息用于日志
    user_info = {
        "username": user.username,
        "email": user.email,
        "is_admin": user.is_admin
    }

    # 删除用户相关数据（级联删除）
    try:
        # 删除用户配额
        user_quota = session.exec(select(UserQuota).where(UserQuota.user_id == user_id)).first()
        if user_quota:
            session.delete(user_quota)

        # 删除用户的知识库和相关文档
        user_kbs = session.exec(select(KnowledgeBase).where(KnowledgeBase.owner_id == user_id)).all()
        for kb in user_kbs:
            # 删除知识库下的文档
            docs = session.exec(select(Document).where(Document.kb_id == kb.id)).all()
            for doc in docs:
                session.delete(doc)
            session.delete(kb)

        # 删除用户的聊天会话和消息
        user_sessions = session.exec(select(ChatSession).where(ChatSession.user_id == user_id)).all()
        for chat_session in user_sessions:
            # 删除会话下的消息
            messages = session.exec(select(ChatMessage).where(ChatMessage.session_id == chat_session.id)).all()
            for message in messages:
                session.delete(message)
            session.delete(chat_session)

        # 删除用户本身
        session.delete(user)
        session.commit()

        # 记录操作日志
        log_entry = OperationLog(
            user_id=admin_user.id,
            action="delete_user",
            target_type="user",
            target_id=user_id,
            details=json.dumps(user_info)
        )
        session.add(log_entry)
        session.commit()

        return {"message": "用户删除成功"}

    except Exception as e:
        session.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除用户失败: {str(e)}"
        )


@router.get("/users/{user_id}/quota", response_model=UserQuotaResponse)
async def get_user_quota(
    user_id: int,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """获取用户配额"""
    # 检查用户是否存在
    user = session.exec(select(User).where(User.id == user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 获取用户配额
    quota = session.exec(select(UserQuota).where(UserQuota.user_id == user_id)).first()
    if not quota:
        # 如果没有配额记录，创建默认配额
        quota = UserQuota(
            user_id=user_id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(quota)
        session.commit()
        session.refresh(quota)

    return quota


@router.put("/users/{user_id}/quota", response_model=UserQuotaResponse)
async def update_user_quota(
    user_id: int,
    quota_data: UserQuotaUpdate,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """更新用户配额"""
    # 检查用户是否存在
    user = session.exec(select(User).where(User.id == user_id)).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )

    # 获取或创建用户配额
    quota = session.exec(select(UserQuota).where(UserQuota.user_id == user_id)).first()
    if not quota:
        quota = UserQuota(
            user_id=user_id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )

    # 记录原始值
    old_values = {
        "max_kbs": quota.max_kbs,
        "max_docs_per_kb": quota.max_docs_per_kb,
        "max_storage_mb": quota.max_storage_mb
    }

    # 更新配额
    if quota_data.max_kbs is not None:
        quota.max_kbs = quota_data.max_kbs
    if quota_data.max_docs_per_kb is not None:
        quota.max_docs_per_kb = quota_data.max_docs_per_kb
    if quota_data.max_storage_mb is not None:
        quota.max_storage_mb = quota_data.max_storage_mb

    session.add(quota)
    session.commit()
    session.refresh(quota)

    # 记录操作日志
    new_values = {
        "max_kbs": quota.max_kbs,
        "max_docs_per_kb": quota.max_docs_per_kb,
        "max_storage_mb": quota.max_storage_mb
    }

    log_entry = OperationLog(
        user_id=admin_user.id,
        action="update_user_quota",
        target_type="user_quota",
        target_id=user_id,
        details=json.dumps({
            "old_values": old_values,
            "new_values": new_values
        })
    )
    session.add(log_entry)
    session.commit()

    return quota


@router.get("/logs", response_model=List[OperationLogResponse])
async def get_operation_logs(
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep,
    offset: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    action_filter: Optional[str] = Query(None),
    user_id_filter: Optional[int] = Query(None)
):
    """获取操作日志"""
    statement = (
        select(OperationLog)
        .order_by(OperationLog.created_at.desc())
        .offset(offset)
        .limit(limit)
    )
    
    if action_filter:
        statement = statement.where(OperationLog.action == action_filter)
    
    if user_id_filter:
        statement = statement.where(OperationLog.user_id == user_id_filter)
    
    logs = session.exec(statement).all()
    
    # 处理details字段
    result = []
    for log in logs:
        log_dict = log.model_dump()
        if log.details:
            try:
                log_dict["details"] = json.loads(log.details)
            except:
                log_dict["details"] = None
        result.append(OperationLogResponse(**log_dict))
    
    return result


@router.get("/settings", response_model=List[SystemSettingResponse])
async def get_system_settings(
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """获取系统设置"""
    statement = select(SystemSetting)
    settings = session.exec(statement).all()
    return settings


@router.put("/settings/{setting_key}")
async def update_system_setting(
    setting_key: str,
    setting_update: SystemSettingUpdate,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """更新系统设置"""
    # 查找设置
    statement = select(SystemSetting).where(SystemSetting.key == setting_key)
    setting = session.exec(statement).first()
    
    if setting:
        # 更新现有设置
        old_value = setting.value
        setting.value = setting_update.value
        if setting_update.description is not None:
            setting.description = setting_update.description
    else:
        # 创建新设置
        setting = SystemSetting(
            key=setting_key,
            value=setting_update.value,
            description=setting_update.description
        )
        old_value = None
    
    session.add(setting)
    session.commit()
    
    # 记录操作日志
    log_entry = OperationLog(
        user_id=admin_user.id,
        action="update_system_setting",
        target_type="system_setting",
        details=json.dumps({
            "key": setting_key,
            "old_value": old_value,
            "new_value": setting_update.value
        })
    )
    session.add(log_entry)
    session.commit()
    
    return {"message": "系统设置更新成功"}


# AI模型管理API
@router.get("/ai-providers", response_model=List[AIProviderResponse])
async def get_ai_providers(
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """获取AI提供商列表"""
    providers = session.exec(select(AIProvider)).all()
    return providers


@router.post("/ai-providers", response_model=AIProviderResponse)
async def create_ai_provider(
    provider_data: dict,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """创建AI提供商"""
    from app.models.ai import AIProvider

    # 检查名称是否已存在
    existing = session.exec(select(AIProvider).where(AIProvider.name == provider_data["name"])).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="供应商名称已存在"
        )

    provider = AIProvider(**provider_data)
    session.add(provider)
    session.commit()
    session.refresh(provider)

    # 记录操作日志
    log_entry = OperationLog(
        user_id=admin_user.id,
        action="create_ai_provider",
        target_type="ai_provider",
        target_id=provider.id,
        details=json.dumps({"name": provider.name, "display_name": provider.display_name})
    )
    session.add(log_entry)
    session.commit()

    return provider


@router.put("/ai-providers/{provider_id}", response_model=AIProviderResponse)
async def update_ai_provider(
    provider_id: int,
    provider_data: dict,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """更新AI提供商"""
    provider = session.exec(select(AIProvider).where(AIProvider.id == provider_id)).first()
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="供应商不存在"
        )

    # 如果要更新名称，检查是否与其他供应商冲突
    if "name" in provider_data and provider_data["name"] != provider.name:
        existing = session.exec(select(AIProvider).where(AIProvider.name == provider_data["name"])).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="供应商名称已存在"
            )

    # 更新字段
    for field, value in provider_data.items():
        if hasattr(provider, field):
            setattr(provider, field, value)

    session.add(provider)
    session.commit()
    session.refresh(provider)

    # 记录操作日志
    log_entry = OperationLog(
        user_id=admin_user.id,
        action="update_ai_provider",
        target_type="ai_provider",
        target_id=provider.id,
        details=json.dumps({"name": provider.name, "display_name": provider.display_name})
    )
    session.add(log_entry)
    session.commit()

    return provider


@router.delete("/ai-providers/{provider_id}")
async def delete_ai_provider(
    provider_id: int,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """删除AI提供商"""
    try:
        provider = session.exec(select(AIProvider).where(AIProvider.id == provider_id)).first()
        if not provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="供应商不存在"
            )

        # 检查是否有关联的模型
        models = session.exec(select(AIModel).where(AIModel.provider_id == provider_id)).all()
        if models:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法删除：该供应商下还有模型，请先删除相关模型"
            )

        # 检查是否有关联的用户API密钥，如果有则一并删除
        from app.models.ai import UserAPIKey
        user_api_keys = session.exec(select(UserAPIKey).where(UserAPIKey.provider_id == provider_id)).all()
        if user_api_keys:
            # 删除相关的用户API密钥
            for api_key in user_api_keys:
                session.delete(api_key)

        # 保存供应商信息用于日志记录
        provider_info = {"name": provider.name, "display_name": provider.display_name}

        session.delete(provider)
        session.commit()

        # 记录操作日志
        log_entry = OperationLog(
            user_id=admin_user.id,
            action="delete_ai_provider",
            target_type="ai_provider",
            target_id=provider_id,
            details=json.dumps(provider_info)
        )
        session.add(log_entry)
        session.commit()

        return {"message": "供应商删除成功"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除供应商时发生错误: {str(e)}"
        )


@router.get("/ai-models", response_model=List[AIModelAdminResponse])
async def get_ai_models(
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep,
    provider_id: Optional[int] = Query(None)
):
    """获取AI模型列表"""
    statement = select(AIModel)

    if provider_id:
        statement = statement.where(AIModel.provider_id == provider_id)

    models = session.exec(statement).all()
    return models


@router.put("/ai-models/{model_id}", response_model=AIModelAdminResponse)
async def update_ai_model(
    model_id: int,
    model_data: AIModelUpdateRequest,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """更新AI模型配置"""
    # 查找模型
    model = session.exec(select(AIModel).where(AIModel.id == model_id)).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="AI模型不存在"
        )

    # 记录原始值
    old_values = {
        "display_name": model.display_name,
        "is_active": model.is_active,
        "system_api_key": "***" if model.system_api_key else None,
        "allow_system_key_use": model.allow_system_key_use,
        "max_tokens": model.max_tokens,
        "supports_streaming": model.supports_streaming,
        "cost_per_1k_tokens": model.cost_per_1k_tokens
    }

    # 更新字段（使用exclude_unset=True来区分未提供的字段和None值）
    update_data = model_data.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(model, field, value)

    session.add(model)
    session.commit()
    session.refresh(model)

    # 记录操作日志
    new_values = {
        "display_name": model.display_name,
        "is_active": model.is_active,
        "system_api_key": "***" if model.system_api_key else None,
        "allow_system_key_use": model.allow_system_key_use,
        "max_tokens": model.max_tokens,
        "supports_streaming": model.supports_streaming,
        "cost_per_1k_tokens": model.cost_per_1k_tokens
    }

    log_entry = OperationLog(
        user_id=admin_user.id,
        action="update_ai_model",
        target_type="ai_model",
        target_id=model.id,
        details=json.dumps({
            "model_name": model.model_name,
            "old_values": old_values,
            "new_values": new_values
        })
    )
    session.add(log_entry)
    session.commit()

    return model


@router.post("/ai-models", response_model=AIModelAdminResponse)
async def create_ai_model(
    model_data: dict,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """创建AI模型"""
    # 验证供应商是否存在
    provider = session.exec(select(AIProvider).where(AIProvider.id == model_data["provider_id"])).first()
    if not provider:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="供应商不存在"
        )

    # 检查同一供应商下是否已有相同模型名称
    existing = session.exec(
        select(AIModel).where(
            AIModel.provider_id == model_data["provider_id"],
            AIModel.model_name == model_data["model_name"]
        )
    ).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该供应商下已存在相同名称的模型"
        )

    model = AIModel(**model_data)
    session.add(model)
    session.commit()
    session.refresh(model)

    # 记录操作日志
    log_entry = OperationLog(
        user_id=admin_user.id,
        action="create_ai_model",
        target_type="ai_model",
        target_id=model.id,
        details=json.dumps({
            "model_name": model.model_name,
            "display_name": model.display_name,
            "provider_id": model.provider_id
        })
    )
    session.add(log_entry)
    session.commit()

    return model


@router.delete("/ai-models/{model_id}")
async def delete_ai_model(
    model_id: int,
    admin_user: User = Depends(get_current_admin_user),
    session: SessionDep = SessionDep
):
    """删除AI模型"""
    model = session.exec(select(AIModel).where(AIModel.id == model_id)).first()
    if not model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="模型不存在"
        )

    # 保存模型信息用于日志记录
    model_info = {
        "model_name": model.model_name,
        "display_name": model.display_name,
        "provider_id": model.provider_id
    }

    session.delete(model)
    session.commit()

    # 记录操作日志
    log_entry = OperationLog(
        user_id=admin_user.id,
        action="delete_ai_model",
        target_type="ai_model",
        target_id=model_id,
        details=json.dumps(model_info)
    )
    session.add(log_entry)
    session.commit()

    return {"message": "模型删除成功"}
