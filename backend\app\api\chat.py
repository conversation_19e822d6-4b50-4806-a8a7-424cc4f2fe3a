"""
AI聊天API
"""
import json
import asyncio
import hashlib
from typing import List, Optional, AsyncGenerator, Dict
from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import StreamingResponse
from sqlmodel import Session, select
from app.core.database import SessionDep
from app.core.auth import get_current_active_user


def parse_ai_error(error_str: str) -> str:
    """解析AI服务错误并返回友好的错误信息"""
    error_lower = error_str.lower()

    # 模型被禁用
    if 'model disabled' in error_lower or '30003' in error_str:
        return "当前选择的AI模型已被禁用，请在设置中选择其他可用的模型"

    # API密钥相关错误
    if any(keyword in error_lower for keyword in ['401', 'unauthorized', 'invalid api key', 'authentication']):
        return "API密钥无效或已过期，请在设置中检查并更新API密钥"

    # 配额不足
    if any(keyword in error_lower for keyword in ['quota', 'insufficient', 'limit exceeded', 'rate limit']):
        return "API配额不足或请求频率过高，请检查您的账户余额或稍后重试"

    # 网络连接错误
    if any(keyword in error_lower for keyword in ['network', 'timeout', 'connection', 'unreachable']):
        return "网络连接失败，请检查网络连接后重试"

    # 服务器错误
    if any(keyword in error_lower for keyword in ['500', 'internal server error', 'service unavailable']):
        return "AI服务暂时不可用，请稍后重试"

    # 模型不存在或不支持
    if any(keyword in error_lower for keyword in ['model not found', 'unsupported model', 'invalid model']):
        return "选择的AI模型不存在或不支持，请在设置中选择其他模型"

    # 不支持的AI提供商
    if any(keyword in error_lower for keyword in ['不支持的ai提供商', 'unsupported ai provider', 'unsupported provider']):
        return "当前选择的AI提供商暂不支持，请在设置中选择其他可用的AI模型"

    # 请求格式错误
    if any(keyword in error_lower for keyword in ['400', 'bad request', 'invalid request']):
        return "请求格式错误，请重试或联系技术支持"

    # 默认错误信息
    return f"AI服务调用失败，请检查模型设置和API密钥配置。错误详情：{error_str}"
from app.models.user import User
from app.models.ai import ChatSession, ChatMessage, AIProvider, AIModel
from app.schemas.ai import (
    ChatSessionCreate,
    ChatSessionResponse,
    ChatMessageResponse,
    ChatRequest
)
from pydantic import BaseModel
from app.services.ai_service import get_ai_service
from app.services.vector_service import get_vector_service
from app.services.cache_service import get_cache_service
import hashlib

router = APIRouter()


class RegenerateMessageRequest(BaseModel):
    """重新生成消息请求"""
    message_id: int
    model_id: int  # 修改为int类型，与数据库保持一致
    knowledge_base_ids: Optional[List[int]] = None
    history_limit: Optional[int] = 10
    relevance_threshold: Optional[float] = 0.4


class BatchDeleteMessagesRequest(BaseModel):
    """批量删除消息请求"""
    message_ids: List[int]


@router.post("/sessions", response_model=ChatSessionResponse)
async def create_chat_session(
    session_data: ChatSessionCreate,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """创建聊天会话"""
    new_session = ChatSession(
        user_id=current_user.id,
        title=session_data.title or "新对话"
    )
    
    session.add(new_session)
    session.commit()
    session.refresh(new_session)
    
    return new_session


@router.get("/sessions", response_model=List[ChatSessionResponse])
async def get_chat_sessions(
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep,
    offset: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100)
):
    """获取用户的聊天会话列表"""
    statement = (
        select(ChatSession)
        .where(ChatSession.user_id == current_user.id)
        .order_by(ChatSession.updated_at.desc())
        .offset(offset)
        .limit(limit)
    )
    sessions = session.exec(statement).all()
    return sessions


@router.get("/sessions/{session_id}/messages", response_model=List[ChatMessageResponse])
async def get_chat_messages(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep,
    offset: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100)
):
    """获取聊天会话的消息列表"""
    # 验证会话权限
    session_statement = select(ChatSession).where(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()
    
    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="聊天会话不存在"
        )
    
    # 获取消息列表
    statement = (
        select(ChatMessage)
        .where(ChatMessage.session_id == session_id)
        .order_by(ChatMessage.created_at.asc())
        .offset(offset)
        .limit(limit)
    )
    messages = session.exec(statement).all()
    
    # 处理referenced_kbs字段
    result = []
    for msg in messages:
        msg_dict = msg.model_dump()
        if msg.referenced_kbs:
            try:
                parsed_kbs = json.loads(msg.referenced_kbs)
                # 确保 referenced_kbs 是列表类型
                if isinstance(parsed_kbs, list):
                    msg_dict["referenced_kbs"] = parsed_kbs
                elif isinstance(parsed_kbs, dict):
                    # 如果是字典，尝试提取值或转换为空列表
                    msg_dict["referenced_kbs"] = []
                else:
                    msg_dict["referenced_kbs"] = []
            except:
                msg_dict["referenced_kbs"] = []
        else:
            msg_dict["referenced_kbs"] = []
        result.append(ChatMessageResponse(**msg_dict))
    
    return result


@router.post("/chat", response_model=ChatMessageResponse)
async def send_chat_message(
    chat_request: ChatRequest,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """发送聊天消息"""
    # 如果没有指定会话，创建新会话
    if not chat_request.session_id:
        new_session = ChatSession(
            user_id=current_user.id,
            title="新对话"
        )
        session.add(new_session)
        session.commit()
        session.refresh(new_session)
        session_id = new_session.id
    else:
        # 验证会话权限
        session_statement = select(ChatSession).where(
            ChatSession.id == chat_request.session_id,
            ChatSession.user_id == current_user.id
        )
        chat_session = session.exec(session_statement).first()
        
        if not chat_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="聊天会话不存在"
            )
        session_id = chat_request.session_id
    
    # 保存用户消息
    user_message = ChatMessage(
        session_id=session_id,
        role="user",
        content=chat_request.message,
        referenced_kbs=json.dumps(chat_request.knowledge_base_ids) if chat_request.knowledge_base_ids else None
    )
    
    session.add(user_message)
    session.commit()
    session.refresh(user_message)
    
    # 这里应该调用AI模型生成回复
    # 目前返回一个模拟回复
    ai_response = f"这是对 '{chat_request.message}' 的AI回复"
    
    # 保存AI回复
    ai_message = ChatMessage(
        session_id=session_id,
        role="assistant",
        content=ai_response,
        model_id_used=chat_request.model_id,
        referenced_kbs=json.dumps(chat_request.knowledge_base_ids) if chat_request.knowledge_base_ids else None
    )
    
    session.add(ai_message)
    session.commit()
    session.refresh(ai_message)
    
    # 处理返回数据
    result_dict = ai_message.model_dump()
    if ai_message.referenced_kbs:
        try:
            result_dict["referenced_kbs"] = json.loads(ai_message.referenced_kbs)
        except:
            result_dict["referenced_kbs"] = None
    
    return ChatMessageResponse(**result_dict)


@router.delete("/sessions/{session_id}")
async def delete_chat_session(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """删除聊天会话"""
    # 验证会话权限
    session_statement = select(ChatSession).where(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()
    
    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="聊天会话不存在"
        )
    
    # 先删除相关的消息
    messages_statement = select(ChatMessage).where(ChatMessage.session_id == session_id)
    messages = session.exec(messages_statement).all()
    for message in messages:
        session.delete(message)

    # 然后删除会话
    session.delete(chat_session)
    session.commit()
    
    return {"message": "聊天会话删除成功"}


@router.put("/sessions/{session_id}", response_model=ChatSessionResponse)
async def update_chat_session(
    session_id: int,
    session_data: ChatSessionCreate,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """更新聊天会话"""
    # 验证会话权限
    session_statement = select(ChatSession).where(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()

    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="聊天会话不存在"
        )

    # 更新会话信息
    if session_data.title:
        chat_session.title = session_data.title

    session.add(chat_session)
    session.commit()
    session.refresh(chat_session)

    return chat_session


async def stream_ai_response(
    message: str,
    model_name: str,
    knowledge_context: Optional[str] = None,
    chat_history: Optional[List[Dict]] = None
) -> AsyncGenerator[str, None]:
    """真实AI流式响应"""
    try:
        ai_service = get_ai_service()
        cache_service = get_cache_service()

        # 构建消息历史
        messages = []
        if chat_history:
            messages.extend(chat_history[-10:])  # 只保留最近10条消息

        messages.append({"role": "user", "content": message})

        # 暂时跳过缓存和AI调用，返回简单响应
        # TODO: 更新为使用新的模型ID系统
        yield f"data: {json.dumps({'content': '抱歉，AI服务正在升级中，请稍后再试。', 'type': 'content'})}\n\n"
        yield f"data: {json.dumps({'type': 'done'})}\n\n"
        return

        # 发送结束信号
        yield f"data: {json.dumps({'type': 'done'})}\n\n"

    except Exception as e:
        error_msg = f"AI服务错误: {str(e)}"
        yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n"
        yield f"data: {json.dumps({'type': 'done'})}\n\n"


async def stream_ai_response_with_save(
    session_id: int,
    user_message_id: int,
    message: str,
    model_name: str,
    ai_model: AIModel,  # 修正为AIModel类型
    knowledge_base_ids: Optional[List[int]] = None,
    knowledge_context: Optional[str] = None,
    chat_history: Optional[List[Dict]] = None,
    history_limit: Optional[int] = 10,
    relevance_threshold: Optional[float] = 0.7
) -> AsyncGenerator[str, None]:
    """流式AI响应并保存到数据库"""
    from app.core.database import get_session

    try:
        ai_service = get_ai_service()
        cache_service = get_cache_service()

        # 构建消息历史
        messages = []
        if chat_history:
            messages.extend(chat_history[-history_limit:])  # 使用用户指定的历史长度

        messages.append({"role": "user", "content": message})

        # 获取AI客户端并进行真实的AI调用
        from app.services.ai_service import get_ai_client_for_model

        # 获取当前用户ID（从session_id查找）
        db_session = next(get_session())
        try:
            session_stmt = select(ChatSession).where(ChatSession.id == session_id)
            chat_session = db_session.exec(session_stmt).first()
            if not chat_session:
                raise ValueError("聊天会话不存在")

            user_id = chat_session.user_id

            # 获取AI客户端
            ai_client = await get_ai_client_for_model(ai_model.id, user_id, db_session)

            # 构建系统消息
            system_content = """你是一个智能助手，具有以下能力：

1. **📊 数据可视化**：根据用户需求提供图表
2. **📚 知识问答**：基于知识库内容回答问题
3. **💬 对话交流**：进行自然、友好的对话

## 🎯 图表生成规则

根据用户的具体要求选择合适的格式：

### 📈 情况1：用户要求JSON配置或没有特殊要求
提供Chart.js JSON格式的图表配置：

```json
{
  "type": "bar",
  "data": {
    "labels": ["邓文雨", "孙海婷", "覃琴", "曾彦", "黄沁玥"],
    "datasets": [{
      "label": "作业数量",
      "data": [3, 3, 4, 4, 3],
      "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"]
    }]
  },
  "options": {
    "responsive": true,
    "plugins": {
      "title": {
        "display": true,
        "text": "各老师布置作业数量统计"
      }
    },
    "scales": {
      "y": {
        "beginAtZero": true
      }
    }
  }
}
```

### � 情况2：用户明确要求HTML或完整代码
提供完整的HTML页面，包含Chart.js库引用和完整的JavaScript代码：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表标题</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            width: 90%;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>

    <script>
        const ctx = document.getElementById('myChart').getContext('2d');
        const myChart = new Chart(ctx, {
            // 在这里放置完整的Chart.js配置
        });
    </script>
</body>
</html>
```

## 💡 重要提示
- **根据用户要求选择格式**：如果用户要求HTML，提供完整HTML；如果要求配置，提供JSON
- 确保代码格式正确，可以直接使用
- 数据要准确，标题和标签要清晰
- 支持的图表类型：bar, line, pie, doughnut, radar, polarArea"""

            # 如果有知识库上下文，添加到系统消息中
            if knowledge_context:
                print(f"[AI服务] 接收到知识库上下文，长度: {len(knowledge_context)}")
                print(f"[AI服务] 知识库上下文前200字符: {knowledge_context[:200]}...")
                system_content += f"\n\n## 📚 知识库内容\n请基于以下知识库内容回答用户问题：\n\n{knowledge_context}\n\n如果知识库中没有相关信息，请说明并基于你的知识回答。"
            else:
                print("[AI服务] 没有接收到知识库上下文")

            system_message = {
                "role": "system",
                "content": system_content
            }
            messages = [system_message] + messages

            # 进行AI流式调用
            full_response = ""
            async for chunk in ai_client.stream_chat(
                messages=messages,
                model=ai_model.model_name,
                temperature=0.7,
                max_tokens=ai_model.max_tokens or 8000  # 使用模型配置的max_tokens，默认8000
            ):
                if chunk:
                    full_response += chunk
                    yield f"data: {json.dumps({'content': chunk, 'type': 'content'})}\n\n"

        except Exception as e:
            # 如果AI调用失败，返回友好的错误信息
            error_message = parse_ai_error(str(e))
            full_response = error_message
            yield f"data: {json.dumps({'content': full_response, 'type': 'content'})}\n\n"
        finally:
            db_session.close()

        # 保存AI消息到数据库
        db_session_save = next(get_session())
        try:
            ai_message = ChatMessage(
                session_id=session_id,
                role="assistant",
                content=full_response,
                model_id_used=ai_model.id,  # 使用AI模型的ID而不是名称
                referenced_kbs=json.dumps(knowledge_base_ids or [])
            )
            db_session_save.add(ai_message)
            db_session_save.commit()
            db_session_save.refresh(ai_message)

            # 发送完成信号，包含完整的消息对象
            try:
                referenced_kbs_list = json.loads(ai_message.referenced_kbs) if ai_message.referenced_kbs else []
            except:
                referenced_kbs_list = []

            message_dict = {
                "id": ai_message.id,
                "session_id": ai_message.session_id,
                "role": ai_message.role,
                "content": ai_message.content,
                "created_at": ai_message.created_at.isoformat(),
                "model_id_used": ai_message.model_id_used,
                "referenced_kbs": referenced_kbs_list
            }
            yield f"data: {json.dumps({'type': 'done', 'message': message_dict})}\n\n"

        finally:
            db_session_save.close()

    except Exception as e:
        error_msg = f"AI服务错误: {str(e)}"
        yield f"data: {json.dumps({'content': error_msg, 'type': 'error'})}\n\n"
        yield f"data: {json.dumps({'type': 'done'})}\n\n"


@router.post("/sessions/{session_id}/stream")
async def stream_chat(
    session_id: int,
    chat_request: ChatRequest,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """流式聊天"""
    print(f"DEBUG: 接收到聊天请求 - session_id: {session_id}, model_id: {chat_request.model_id}, message: {chat_request.message[:50]}...")
    vector_service = get_vector_service()
    cache_service = get_cache_service()

    # 验证会话是否存在且属于当前用户
    session_statement = select(ChatSession).where(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()

    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="聊天会话不存在"
        )

    # 获取AI模型信息（通过模型ID查找）
    model_statement = select(AIModel).where(AIModel.id == chat_request.model_id)
    ai_model = session.exec(model_statement).first()

    if not ai_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"AI模型 {chat_request.model_id} 不存在"
        )

    # 获取知识库上下文
    knowledge_context = None
    if chat_request.knowledge_base_ids:
        print(f"正在获取知识库上下文，知识库IDs: {chat_request.knowledge_base_ids}")
        print(f"查询内容: {chat_request.message}")
        print(f"相关度阈值: {chat_request.relevance_threshold}")

        knowledge_context = await vector_service.get_knowledge_context(
            kb_ids=chat_request.knowledge_base_ids,
            query=chat_request.message,
            similarity_threshold=chat_request.relevance_threshold
        )

        print(f"获取到的知识库上下文长度: {len(knowledge_context) if knowledge_context else 0}")
        if knowledge_context:
            print(f"知识库上下文内容: {knowledge_context[:200]}...")
        else:
            print("未获取到知识库上下文")

    # 获取聊天历史（从数据库获取最新的历史记录）
    history_limit = getattr(chat_request, 'history_limit', 10)  # 默认获取最近10条
    history_statement = (
        select(ChatMessage)
        .where(ChatMessage.session_id == session_id)
        .order_by(ChatMessage.created_at.desc())
        .limit(history_limit * 2)  # 用户和AI消息成对出现
    )
    history_messages = session.exec(history_statement).all()
    history_messages.reverse()  # 按时间正序

    # 构建聊天历史
    chat_history = []
    for msg in history_messages:
        chat_history.append({
            "role": msg.role,
            "content": msg.content
        })

    # 保存用户消息
    user_message = ChatMessage(
        session_id=session_id,
        role="user",
        content=chat_request.message,
        model_id_used=ai_model.id,  # 使用AI模型的ID而不是名称
        referenced_kbs=json.dumps(chat_request.knowledge_base_ids or [])
    )
    session.add(user_message)
    session.commit()
    session.refresh(user_message)

    # 返回流式响应
    return StreamingResponse(
        stream_ai_response_with_save(
            session_id=session_id,
            user_message_id=user_message.id,
            message=chat_request.message,
            model_name=ai_model.model_name,
            ai_model=ai_model,  # 传递完整的AI模型对象
            knowledge_base_ids=chat_request.knowledge_base_ids,
            knowledge_context=knowledge_context,
            chat_history=chat_history,
            history_limit=chat_request.history_limit,
            relevance_threshold=chat_request.relevance_threshold
        ),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
        }
    )


@router.delete("/sessions/{session_id}/messages")
async def clear_chat_session(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """清空聊天会话的所有消息"""
    # 验证会话是否存在且属于当前用户
    session_statement = select(ChatSession).where(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()

    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="聊天会话不存在"
        )

    # 删除会话中的所有消息
    messages_statement = select(ChatMessage).where(ChatMessage.session_id == session_id)
    messages = session.exec(messages_statement).all()

    for message in messages:
        session.delete(message)

    session.commit()

    return {"message": f"已清空聊天会话 {session_id} 的所有消息"}


@router.delete("/messages/{message_id}")
async def delete_chat_message(
    message_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """删除单条聊天消息"""
    statement = select(ChatMessage).where(ChatMessage.id == message_id)
    message = session.exec(statement).first()

    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="消息不存在"
        )

    # 验证权限
    session_statement = select(ChatSession).where(
        ChatSession.id == message.session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()

    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权删除此消息"
        )

    session.delete(message)
    session.commit()

    return {"message": "消息删除成功"}


@router.delete("/messages/batch")
async def batch_delete_messages(
    request: BatchDeleteMessagesRequest,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """批量删除聊天消息"""
    deleted_count = 0
    failed_deletes = []

    for message_id in request.message_ids:
        try:
            statement = select(ChatMessage).where(ChatMessage.id == message_id)
            message = session.exec(statement).first()

            if not message:
                failed_deletes.append({
                    "message_id": message_id,
                    "error": "消息不存在"
                })
                continue

            # 验证权限
            session_statement = select(ChatSession).where(
                ChatSession.id == message.session_id,
                ChatSession.user_id == current_user.id
            )
            chat_session = session.exec(session_statement).first()

            if not chat_session:
                failed_deletes.append({
                    "message_id": message_id,
                    "error": "无权删除此消息"
                })
                continue

            session.delete(message)
            session.commit()
            deleted_count += 1

        except Exception as e:
            failed_deletes.append({
                "message_id": message_id,
                "error": str(e)
            })

    return {
        "message": f"批量删除完成，成功: {deleted_count}, 失败: {len(failed_deletes)}",
        "deleted_count": deleted_count,
        "failed_deletes": failed_deletes
    }


@router.post("/messages/regenerate")
async def regenerate_message(
    request: RegenerateMessageRequest,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """重新生成AI消息"""
    # 获取原消息
    statement = select(ChatMessage).where(ChatMessage.id == request.message_id)
    original_message = session.exec(statement).first()

    if not original_message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="消息不存在"
        )

    # 验证权限
    session_statement = select(ChatSession).where(
        ChatSession.id == original_message.session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()

    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权操作此消息"
        )

    # 只能重新生成AI消息
    if original_message.role != "assistant":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能重新生成AI消息"
        )

    # 获取用户的原始问题（前一条消息）
    prev_statement = (
        select(ChatMessage)
        .where(
            ChatMessage.session_id == original_message.session_id,
            ChatMessage.created_at < original_message.created_at,
            ChatMessage.role == "user"
        )
        .order_by(ChatMessage.created_at.desc())
        .limit(1)
    )
    user_message = session.exec(prev_statement).first()

    if not user_message:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="找不到对应的用户消息"
        )

    # 获取AI服务
    ai_service = get_ai_service()
    vector_service = get_vector_service()
    cache_service = get_cache_service()

    # 获取AI模型信息
    model_statement = select(AIModel).where(AIModel.id == request.model_id)
    ai_model = session.exec(model_statement).first()

    if not ai_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"AI模型 {request.model_id} 不存在"
        )

    try:
        # 获取知识库上下文
        knowledge_context = None
        if request.knowledge_base_ids:
            knowledge_context = await vector_service.get_knowledge_context(
                kb_ids=request.knowledge_base_ids,
                query=user_message.content
            )

        # 获取聊天历史
        chat_history = await cache_service.get_chat_history(original_message.session_id)

        # 构建消息历史
        messages = []
        if chat_history:
            messages.extend(chat_history[-10:])  # 只保留最近10条消息

        messages.append({"role": "user", "content": user_message.content})

        # 暂时跳过AI调用
        # TODO: 更新为使用新的模型ID系统
        full_response = "抱歉，AI服务正在升级中，请稍后再试。"

        # 更新原消息
        original_message.content = full_response
        original_message.model_id_used = ai_model.id
        original_message.referenced_kbs = json.dumps(request.knowledge_base_ids or [])

        session.add(original_message)
        session.commit()
        session.refresh(original_message)

        # 处理返回数据
        result_dict = original_message.model_dump()
        if original_message.referenced_kbs:
            try:
                result_dict["referenced_kbs"] = json.loads(original_message.referenced_kbs) if isinstance(original_message.referenced_kbs, str) else original_message.referenced_kbs
            except:
                result_dict["referenced_kbs"] = None

        return ChatMessageResponse(**result_dict)

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新生成消息失败: {str(e)}"
        )


async def stream_ai_response_with_regenerate(
    message_id: int,
    user_message_content: str,
    model_name: str,
    ai_model: AIModel,
    knowledge_base_ids: Optional[List[int]] = None,
    knowledge_context: Optional[str] = None,
    chat_history: Optional[List[Dict]] = None,
    history_limit: Optional[int] = 10,
    relevance_threshold: Optional[float] = 0.7
) -> AsyncGenerator[str, None]:
    """流式AI响应并更新现有消息"""
    from app.core.database import get_session

    ai_service = get_ai_service()
    cache_service = get_cache_service()

    try:
        # 获取提供商信息
        provider = None
        session_gen = get_session()
        session = next(session_gen)
        try:
            provider_statement = select(AIProvider).where(AIProvider.id == ai_model.provider_id)
            provider = session.exec(provider_statement).first()
            if not provider:
                raise HTTPException(status_code=404, detail="AI提供商未找到")
        finally:
            session.close()

        # 构建消息历史
        messages = []
        if chat_history:
            messages.extend(chat_history[-history_limit:])  # 使用用户指定的历史长度

        messages.append({"role": "user", "content": user_message_content})

        # 调用AI服务生成响应
        full_response = ""
        print(f"开始调用AI服务，提供商: {provider.name}, 模型: {model_name}")

        # 获取用户ID（从消息ID推导）
        db_session_gen = get_session()
        db_session = next(db_session_gen)
        try:
            message_statement = select(ChatMessage, ChatSession).join(ChatSession).where(ChatMessage.id == message_id)
            message_result = db_session.exec(message_statement).first()
            if not message_result:
                raise ValueError("找不到消息")
            _, chat_session = message_result
            user_id = chat_session.user_id
        finally:
            db_session.close()

        # 使用动态客户端创建方式
        from app.services.ai_service import get_ai_client_for_model
        client = await get_ai_client_for_model(ai_model.id, user_id, session)

        # 构建系统消息
        system_content = """你是一个智能助手，具有以下能力：

1. **📊 数据可视化**：根据用户需求提供图表
2. **📚 知识问答**：基于知识库内容回答问题
3. **💬 对话交流**：进行自然、友好的对话

## 🎯 图表生成规则

根据用户的具体要求选择合适的格式：

### 📈 情况1：用户没有特殊要求
提供Chart.js JSON格式的图表：

```json
{
  "type": "bar",
  "data": {
    "labels": ["邓文雨", "孙海婷", "覃琴", "曾彦", "黄沁玥"],
    "datasets": [{
      "label": "作业数量",
      "data": [3, 3, 4, 4, 3],
      "backgroundColor": ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"]
    }]
  },
  "options": {
    "responsive": true,
    "plugins": {
      "title": {
        "display": true,
        "text": "各老师布置作业数量统计"
      }
    },
    "scales": {
      "y": {
        "beginAtZero": true
      }
    }
  }
}
```

### � 情况2：用户明确要求HTML或完整代码
提供完整的HTML页面，包含Chart.js库引用和完整的JavaScript代码：

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图表标题</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .chart-container {
            width: 90%;
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="chart-container">
        <canvas id="myChart"></canvas>
    </div>

    <script>
        const ctx = document.getElementById('myChart').getContext('2d');
        const myChart = new Chart(ctx, {
            // 在这里放置完整的Chart.js配置
        });
    </script>
</body>
</html>
```

## 💡 重要提示
- **根据用户要求选择格式**：如果用户要求HTML，提供完整HTML；如果没有要求，提供JSON代码块并绘制相应图表
- 确保代码格式正确，可以直接使用
- 数据要准确，标题和标签要清晰
- 支持的图表类型：bar, line, pie, doughnut, radar, polarArea"""

        # 如果有知识库上下文，添加到系统消息中
        if knowledge_context:
            print(f"[AI服务] 接收到知识库上下文，长度: {len(knowledge_context)}")
            print(f"[AI服务] 知识库上下文前200字符: {knowledge_context[:500]}...")
            system_content += f"\n\n## 📚 知识库内容\n请基于以下知识库内容回答用户问题：\n\n{knowledge_context}\n\n如果知识库中没有相关信息，请说明并基于你的知识回答。"
        else:
            print("[AI服务] 没有接收到知识库上下文")

        system_message = {
            "role": "system",
            "content": system_content
        }
        messages = [system_message] + messages

        async for chunk in client.stream_chat(
            messages=messages,
            model=model_name,
            max_tokens=ai_model.max_tokens or 4000
        ):
            if chunk:
                full_response += chunk
                yield f"data: {json.dumps({'content': chunk, 'type': 'content'})}\n\n"

        print(f"AI服务调用完成，响应长度: {len(full_response)}")

        # 更新数据库中的消息
        db_session_gen = get_session()
        db_session = next(db_session_gen)
        try:
            statement = select(ChatMessage).where(ChatMessage.id == message_id)
            message = db_session.exec(statement).first()

            if message:
                message.content = full_response
                message.model_id_used = ai_model.id  # 使用正确的字段名
                from datetime import datetime
                message.updated_at = datetime.utcnow()

                # 更新引用的知识库
                if knowledge_base_ids:
                    message.referenced_kbs = json.dumps(knowledge_base_ids)

                db_session.add(message)
                db_session.commit()
                db_session.refresh(message)

                # 发送完成信号，包含更新后的消息
                result_dict = {
                    "id": message.id,
                    "content": message.content,
                    "role": message.role,
                    "created_at": message.created_at.isoformat() if message.created_at else None,
                    "updated_at": message.updated_at.isoformat() if message.updated_at else None,
                    "session_id": message.session_id,
                    "model_id_used": message.model_id_used,  # 使用正确的字段名
                    "referenced_kbs": None
                }

                if message.referenced_kbs:
                    try:
                        result_dict["referenced_kbs"] = json.loads(message.referenced_kbs) if isinstance(message.referenced_kbs, str) else message.referenced_kbs
                    except:
                        result_dict["referenced_kbs"] = None

                print(f"发送完成信号，消息ID: {message.id}")
                yield f"data: {json.dumps({'type': 'done', 'message': result_dict})}\n\n"
        except Exception as db_error:
            print(f"数据库操作出错: {str(db_error)}")
            yield f"data: {json.dumps({'content': f'数据库更新失败: {str(db_error)}', 'type': 'error'})}\n\n"
        finally:
            db_session.close()

    except Exception as e:
        print(f"重新生成消息出错: {str(e)}")
        error_msg = parse_ai_error(str(e))
        yield f"data: {json.dumps({'content': error_msg, 'type': 'content'})}\n\n"

        # 创建一个错误消息对象
        from datetime import datetime
        error_message = {
            "id": message_id,
            "content": error_msg,
            "role": "assistant",
            "created_at": datetime.now().isoformat(),
            "model_id_used": ai_model.id,
            "referenced_kbs": knowledge_base_ids or []
        }
        yield f"data: {json.dumps({'type': 'done', 'message': error_message})}\n\n"


@router.post("/messages/regenerate/stream")
async def regenerate_message_stream(
    request: RegenerateMessageRequest,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """流式重新生成AI消息"""
    # 获取原消息
    statement = select(ChatMessage).where(ChatMessage.id == request.message_id)
    original_message = session.exec(statement).first()

    if not original_message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="消息不存在"
        )

    # 验证权限
    session_statement = select(ChatSession).where(
        ChatSession.id == original_message.session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()

    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权操作此消息"
        )

    # 只能重新生成AI消息
    if original_message.role != "assistant":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只能重新生成AI消息"
        )

    # 获取用户的原始问题（前一条消息）
    prev_statement = (
        select(ChatMessage)
        .where(
            ChatMessage.session_id == original_message.session_id,
            ChatMessage.created_at < original_message.created_at,
            ChatMessage.role == "user"
        )
        .order_by(ChatMessage.created_at.desc())
        .limit(1)
    )
    user_message = session.exec(prev_statement).first()

    if not user_message:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="找不到对应的用户消息"
        )

    # 获取AI模型
    ai_model_statement = select(AIModel).where(AIModel.id == request.model_id)
    ai_model = session.exec(ai_model_statement).first()

    if not ai_model:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="AI模型不存在"
        )

    vector_service = get_vector_service()
    cache_service = get_cache_service()

    try:
        # 获取知识库上下文
        knowledge_context = None
        if request.knowledge_base_ids:
            knowledge_context = await vector_service.get_knowledge_context(
                kb_ids=request.knowledge_base_ids,
                query=user_message.content,
                similarity_threshold=request.relevance_threshold
            )

        # 获取聊天历史，使用用户指定的长度限制
        chat_history = await cache_service.get_chat_history(original_message.session_id)

        # 如果指定了历史长度限制，则截取相应数量的消息
        if request.history_limit and chat_history:
            chat_history = chat_history[-request.history_limit:]

        # 返回流式响应
        return StreamingResponse(
            stream_ai_response_with_regenerate(
                message_id=original_message.id,
                user_message_content=user_message.content,
                model_name=ai_model.model_name,  # 使用从数据库获取的模型名称
                ai_model=ai_model,
                knowledge_base_ids=request.knowledge_base_ids,
                knowledge_context=knowledge_context,
                chat_history=chat_history,
                history_limit=request.history_limit,
                relevance_threshold=request.relevance_threshold
            ),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*",
            }
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重新生成消息失败: {str(e)}"
        )


@router.get("/sessions/{session_id}/messages/history")
async def get_chat_history_with_limit(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep,
    limit: int = Query(None, ge=1, le=1000, description="限制返回的消息数量，不设置则返回全部"),
    include_system: bool = Query(False, description="是否包含系统消息")
):
    """获取聊天历史记录（可控制数量）"""
    # 验证会话权限
    session_statement = select(ChatSession).where(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    )
    chat_session = session.exec(session_statement).first()

    if not chat_session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="聊天会话不存在"
        )

    # 构建查询
    statement = select(ChatMessage).where(ChatMessage.session_id == session_id)

    if not include_system:
        statement = statement.where(ChatMessage.role.in_(["user", "assistant"]))

    statement = statement.order_by(ChatMessage.created_at.asc())

    if limit:
        # 如果设置了限制，获取最新的N条消息
        statement = statement.order_by(ChatMessage.created_at.desc()).limit(limit)
        messages = session.exec(statement).all()
        messages.reverse()  # 重新按时间正序排列
    else:
        messages = session.exec(statement).all()

    # 处理referenced_kbs字段
    result = []
    for msg in messages:
        msg_dict = msg.model_dump()
        if msg.referenced_kbs:
            try:
                msg_dict["referenced_kbs"] = json.loads(msg.referenced_kbs) if isinstance(msg.referenced_kbs, str) else msg.referenced_kbs
            except:
                msg_dict["referenced_kbs"] = None
        result.append(ChatMessageResponse(**msg_dict))

    return {
        "messages": result,
        "total_count": len(result),
        "session_id": session_id,
        "limited": limit is not None
    }
