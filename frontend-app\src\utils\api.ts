import axios from 'axios'
import { ElMessage } from 'element-plus'
import type { ApiResponse } from '@/types'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 统一处理响应数据
    const data = response.data as ApiResponse
    
    if (data.success === false) {
      ElMessage.error(data.message || '请求失败')
      return Promise.reject(new Error(data.message || '请求失败'))
    }
    
    return response
  },
  (error) => {
    // 统一错误处理
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('token')
          window.location.href = '/login'
          ElMessage.error('登录已过期，请重新登录')
          break
        case 403:
          ElMessage.error('没有权限访问该资源')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 422:
          // 表单验证错误
          if (data.errors) {
            const firstError = Object.values(data.errors)[0] as string[]
            ElMessage.error(firstError[0])
          } else {
            ElMessage.error(data.message || '请求参数错误')
          }
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API方法封装
export const apiMethods = {
  // 用户认证相关
  auth: {
    login: (data: any) => api.post('/auth/login', data),
    register: (data: any) => api.post('/auth/register', data),
    logout: () => api.post('/auth/logout'),
    me: () => api.get('/auth/me'),
    updateProfile: (data: any) => api.put('/auth/profile', data),
    changePassword: (data: any) => api.put('/auth/password', data),
  },

  // 知识库相关
  knowledgeBase: {
    list: (params?: any) => api.get('/knowledge-bases', { params }),
    create: (data: any) => api.post('/knowledge-bases', data),
    get: (id: number) => api.get(`/knowledge-bases/${id}`),
    update: (id: number, data: any) => api.put(`/knowledge-bases/${id}`, data),
    delete: (id: number) => api.delete(`/knowledge-bases/${id}`),
  },

  // 文档相关
  document: {
    list: (knowledgeBaseId: number, params?: any) => 
      api.get(`/knowledge-bases/${knowledgeBaseId}/documents`, { params }),
    upload: (knowledgeBaseId: number, formData: FormData) => 
      api.post(`/knowledge-bases/${knowledgeBaseId}/documents`, formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      }),
    get: (id: number) => api.get(`/documents/${id}`),
    delete: (id: number) => api.delete(`/documents/${id}`),
    download: (id: number) => api.get(`/documents/${id}/download`, {
      responseType: 'blob'
    }),
    batchDelete: (ids: number[]) => api.delete('/documents/batch', { data: { ids } }),
  },

  // 聊天相关
  chat: {
    sessions: (params?: any) => api.get('/chat/sessions', { params }),
    createSession: (data: any) => api.post('/chat/sessions', data),
    getSession: (id: number) => api.get(`/chat/sessions/${id}`),
    updateSession: (id: number, data: any) => api.put(`/chat/sessions/${id}`, data),
    deleteSession: (id: number) => api.delete(`/chat/sessions/${id}`),
    messages: (sessionId: number, params?: any) => 
      api.get(`/chat/sessions/${sessionId}/messages`, { params }),
    sendMessage: (sessionId: number, data: any) => 
      api.post(`/chat/sessions/${sessionId}/messages`, data),
  },

  // 用户设置相关
  settings: {
    get: () => api.get('/settings'),
    update: (data: any) => api.put('/settings', data),
    getApiKeys: () => api.get('/settings/api-keys'),
    updateApiKey: (provider: string, data: any) => 
      api.put(`/settings/api-keys/${provider}`, data),
  },

  // 管理员相关
  admin: {
    stats: () => api.get('/admin/stats'),
    users: (params?: any) => api.get('/admin/users', { params }),
    createUser: (data: any) => api.post('/admin/users', data),
    updateUser: (id: number, data: any) => api.put(`/admin/users/${id}`, data),
    deleteUser: (id: number) => api.delete(`/admin/users/${id}`),
    aiModels: () => api.get('/admin/ai-models'),
    updateAiModel: (id: string, data: any) => api.put(`/admin/ai-models/${id}`, data),
    logs: (params?: any) => api.get('/admin/logs', { params }),
  },

  // AI模型管理相关
  ai: {
    // 供应商管理
    providers: () => api.get('/ai/providers'),
    createProvider: (data: any) => api.post('/ai/providers', data),
    updateProvider: (id: number, data: any) => api.put(`/ai/providers/${id}`, data),
    deleteProvider: (id: number) => api.delete(`/ai/providers/${id}`),

    // 模型管理
    models: () => api.get('/ai/models'),
    createModel: (data: any) => api.post('/ai/models', data),
    updateModel: (id: number, data: any) => api.put(`/ai/models/${id}`, data),
    deleteModel: (id: number) => api.delete(`/ai/models/${id}`),

    // 用户API密钥管理
    apiKeys: () => api.get('/ai/api-keys'),
    createApiKey: (data: any) => api.post('/ai/api-keys', data),
    updateApiKey: (id: number, data: any) => api.put(`/ai/api-keys/${id}`, data),
    deleteApiKey: (id: number) => api.delete(`/ai/api-keys/${id}`),

    // 连接测试
    testConnection: (data: any) => api.post('/ai/test-connection', data),
  },
}

export { api }
export default api
