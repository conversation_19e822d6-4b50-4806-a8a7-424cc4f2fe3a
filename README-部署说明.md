# 🚀 AI知识库项目部署说明

## 📋 项目配置信息

### 🗄️ 数据库配置
- **数据库名**: `aiknowledgebase`
- **用户名**: `postgres`
- **密码**: `111222`
- **端口**: `5432`

### 🔐 安全配置
- **JWT密钥**: `abcXyz123_4x9KpQvE8jHmN2qRtSvWnZr5t7w-`
- **SiliconFlow API密钥**: `sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm`

### 🌐 域名配置
- **域名**: `aiknowledgebase.csicollege.cn`
- **SSL证书**: `aiknowledgebase.csicollege.cn/full_chain.pem`
- **SSL私钥**: `aiknowledgebase.csicollege.cn/private.key`

## 🚀 快速部署

### 方式一：宝塔面板一键部署（推荐）

1. **上传项目文件**到 `/www/wwwroot/aiknowledgebase`

2. **执行一键部署脚本**：
```bash
cd /www/wwwroot/aiknowledgebase
chmod +x deploy-baota.sh
./deploy-baota.sh
```

3. **访问网站**：
   - HTTPS: https://aiknowledgebase.csicollege.cn
   - HTTP: http://aiknowledgebase.csicollege.cn (自动重定向)

### 方式二：手动部署

1. **安装Docker和Docker Compose**：
```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash
systemctl start docker
systemctl enable docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

2. **设置SSL证书**：
```bash
chmod +x setup-ssl.sh
./setup-ssl.sh
```

3. **部署服务**：
```bash
chmod +x deploy.sh
./deploy.sh
# 选择 "2" 进入生产环境模式
```

## 📁 项目文件结构

```
aiknowledgebase/
├── backend/                    # 后端代码
├── frontend-app/              # 前端代码
├── aiknowledgebase.csicollege.cn/  # SSL证书目录
│   ├── full_chain.pem         # SSL证书
│   └── private.key            # SSL私钥
├── ssl/                       # 部署时SSL证书目录
├── docker-compose.yml         # Docker编排配置
├── docker-compose.prod.yml    # 生产环境配置
├── nginx.prod.conf           # 生产环境Nginx配置
├── .env.example              # 环境变量模板
├── init.sql                  # 数据库初始化脚本
├── deploy.sh                 # 通用部署脚本
├── deploy-baota.sh          # 宝塔专用部署脚本
└── setup-ssl.sh             # SSL设置脚本
```

## 🔧 服务管理命令

### 查看服务状态
```bash
docker-compose ps
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 更新服务
```bash
# 生产环境更新
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d

# 开发环境更新
docker-compose up --build -d
```

### 停止服务
```bash
docker-compose down
```

## 🔍 故障排除

### 1. 检查端口占用
```bash
netstat -tlnp | grep :80
netstat -tlnp | grep :443
```

### 2. 检查SSL证书
```bash
openssl x509 -in ssl/full_chain.pem -text -noout | grep -E "(Subject:|Not After)"
```

### 3. 检查服务健康状态
```bash
curl -f http://localhost/health
curl -f -k https://localhost/health
```

### 4. 查看容器资源使用
```bash
docker stats
```

## 📊 监控和维护

### 数据备份
```bash
# 备份数据库
docker-compose exec postgres pg_dump -U postgres aiknowledgebase > backup_$(date +%Y%m%d).sql

# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz backend/uploads/
```

### 日志管理
```bash
# 清理Docker日志
docker system prune -f

# 查看磁盘使用
docker system df
```

## 🌐 访问地址

- **前端**: https://aiknowledgebase.csicollege.cn
- **API文档**: https://aiknowledgebase.csicollege.cn/api/docs
- **后端健康检查**: https://aiknowledgebase.csicollege.cn/health

## 📞 技术支持

如遇到问题，请检查：
1. 域名DNS解析是否正确
2. 防火墙端口80、443是否开放
3. SSL证书是否有效
4. Docker服务是否正常运行

---

**部署完成后，您的AI知识库系统将在 https://aiknowledgebase.csicollege.cn 上运行！**
