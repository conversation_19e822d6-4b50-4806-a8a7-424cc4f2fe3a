# AI知识库系统 - 快速操作指南

## 🚀 一键启动

```bash
# 启动系统
./deploy-simple.sh start

# 访问地址
http://localhost
```

## 📋 常用命令

| 操作 | 命令 | 说明 |
|------|------|------|
| 启动服务 | `./deploy-simple.sh start` | 启动前后端服务 |
| 停止服务 | `./deploy-simple.sh stop` | 停止所有服务 |
| 重启服务 | `./deploy-simple.sh restart` | 重启所有服务 |
| 查看状态 | `ps aux \| grep python` | 查看后端进程 |
| 查看日志 | `tail -f backend.log` | 实时查看后端日志 |
| 备份数据 | `cp backend/database.db backup/` | 备份数据库 |

## 🔧 故障排除

### 服务无法启动
```bash
# 1. 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :8000

# 2. 杀死占用进程
kill -9 <PID>

# 3. 重新启动
./deploy-simple.sh start
```

### 前端无法访问
```bash
# 检查前端服务
curl http://localhost

# 如果失败，查看日志
tail -f frontend.log
```

### 后端API错误
```bash
# 检查后端健康状态
curl http://localhost:8000/health

# 查看后端日志
tail -f backend.log
```

## 📊 系统监控

### 快速检查脚本
```bash
#!/bin/bash
echo "=== 系统状态检查 ==="
echo "前端服务: $(curl -s http://localhost > /dev/null && echo '✅ 正常' || echo '❌ 异常')"
echo "后端服务: $(curl -s http://localhost:8000/health > /dev/null && echo '✅ 正常' || echo '❌ 异常')"
echo "数据库: $([ -f backend/database.db ] && echo '✅ 存在' || echo '❌ 缺失')"
```

### 资源使用情况
```bash
# CPU和内存使用
top -p $(pgrep -f "python app.py")

# 磁盘使用
df -h

# 数据库大小
ls -lh backend/database.db
```

## 🛠️ 开发模式

### 前端开发
```bash
cd frontend
npm run dev  # 开发服务器 http://localhost:3000
```

### 后端开发
```bash
cd backend
source venv/bin/activate
python app.py  # 开发服务器 http://localhost:8000
```

## 📁 重要文件位置

| 文件/目录 | 路径 | 说明 |
|-----------|------|------|
| 前端源码 | `frontend/src/` | Vue3源代码 |
| 前端构建 | `frontend/dist/` | 生产环境文件 |
| 后端源码 | `backend/app.py` | FastAPI主文件 |
| 数据库 | `backend/database.db` | SQLite数据库 |
| 配置文件 | `docker-compose.prod.yml` | Docker配置 |
| 部署脚本 | `deploy-simple.sh` | 简单部署脚本 |

## 🔐 安全检查清单

- [ ] 修改默认端口
- [ ] 设置防火墙规则
- [ ] 定期备份数据库
- [ ] 更新依赖包
- [ ] 监控日志文件
- [ ] 检查磁盘空间

## 📞 紧急联系

### 系统完全无法访问
1. 重启所有服务: `./deploy-simple.sh restart`
2. 检查系统资源: `top`, `df -h`
3. 查看系统日志: `tail -f /var/log/syslog`
4. 重启服务器: `sudo reboot`

### 数据丢失
1. 停止服务: `./deploy-simple.sh stop`
2. 恢复备份: `cp backup/database_*.db backend/database.db`
3. 重启服务: `./deploy-simple.sh start`

## 🎯 性能优化建议

### 数据库优化
```sql
-- 清理旧数据（示例）
DELETE FROM projects WHERE created_at < date('now', '-1 year');

-- 重建索引
REINDEX;

-- 分析表
ANALYZE;
```

### 系统优化
```bash
# 清理日志文件
truncate -s 0 *.log

# 清理临时文件
rm -rf /tmp/*

# 清理包缓存
npm cache clean --force
pip cache purge
```

---

**记住**: 
- 🔄 定期备份数据
- 📊 监控系统状态  
- 🔧 及时处理异常
- 📚 查阅完整文档: `部署操作文档.md`
