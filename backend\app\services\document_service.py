"""
完整版文档处理服务
支持多种文档格式的解析和分块处理，使用LangChain进行高级文档处理
"""
import logging
import os
import tempfile
from typing import List, Dict, Any, Optional, BinaryIO
from pathlib import Path
import asyncio
import aiohttp

# 文档处理相关导入
try:
    from pypdf import PdfReader
except ImportError:
    PdfReader = None

try:
    from docx import Document as DocxDocument
except ImportError:
    DocxDocument = None

try:
    from openpyxl import load_workbook
except ImportError:
    load_workbook = None

logger = logging.getLogger(__name__)

class DocumentServiceError(Exception):
    """文档服务异常"""
    pass

class AdvancedTextSplitter:
    """高级文本分块器"""
    
    def __init__(self, chunk_size: int = 1000, chunk_overlap: int = 200):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.separators = ["\n\n", "\n", "。", "！", "？", ". ", "! ", "? ", " ", ""]
    
    def split_text(self, text: str) -> List[str]:
        """智能文本分块"""
        if not text or len(text) <= self.chunk_size:
            return [text] if text else []
        
        chunks = []
        current_chunk = ""
        
        # 按段落分割
        paragraphs = text.split('\n\n')
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # 如果当前段落加上现有块超过大小限制
            if len(current_chunk) + len(paragraph) > self.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = ""
                
                # 如果单个段落太长，需要进一步分割
                if len(paragraph) > self.chunk_size:
                    sub_chunks = self._split_long_paragraph(paragraph)
                    chunks.extend(sub_chunks[:-1])  # 除了最后一个
                    current_chunk = sub_chunks[-1] if sub_chunks else ""
                else:
                    current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
        
        # 添加最后一个块
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return self._add_overlap(chunks)
    
    def _split_long_paragraph(self, paragraph: str) -> List[str]:
        """分割长段落"""
        chunks = []
        sentences = self._split_by_sentences(paragraph)
        
        current_chunk = ""
        for sentence in sentences:
            if len(current_chunk) + len(sentence) > self.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence
                else:
                    # 单个句子太长，强制分割
                    chunks.extend(self._force_split(sentence))
            else:
                current_chunk += sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _split_by_sentences(self, text: str) -> List[str]:
        """按句子分割"""
        sentences = []
        current = ""
        
        for char in text:
            current += char
            if char in "。！？.!?":
                sentences.append(current)
                current = ""
        
        if current:
            sentences.append(current)
        
        return sentences
    
    def _force_split(self, text: str) -> List[str]:
        """强制分割长文本"""
        chunks = []
        for i in range(0, len(text), self.chunk_size):
            chunks.append(text[i:i + self.chunk_size])
        return chunks
    
    def _add_overlap(self, chunks: List[str]) -> List[str]:
        """添加重叠内容"""
        if len(chunks) <= 1:
            return chunks
        
        overlapped_chunks = [chunks[0]]
        
        for i in range(1, len(chunks)):
            prev_chunk = chunks[i-1]
            current_chunk = chunks[i]
            
            # 从前一个块的末尾取重叠内容
            overlap_text = prev_chunk[-self.chunk_overlap:] if len(prev_chunk) > self.chunk_overlap else prev_chunk
            
            # 添加重叠内容到当前块
            overlapped_chunk = overlap_text + "\n" + current_chunk
            overlapped_chunks.append(overlapped_chunk)
        
        return overlapped_chunks

class DocumentProcessor:
    """文档处理器基类"""
    
    def __init__(self):
        self.text_splitter = AdvancedTextSplitter()
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理文件并返回分块"""
        raise NotImplementedError

class PDFProcessor(DocumentProcessor):
    """PDF文档处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理PDF文件"""
        try:
            if not PdfReader:
                raise DocumentServiceError("PDF处理库未安装")
            
            reader = PdfReader(file_path)
            full_text = ""
            page_contents = []
            
            # 提取所有页面的文本
            for page_num, page in enumerate(reader.pages):
                page_text = page.extract_text()
                page_contents.append({
                    "page": page_num + 1,
                    "content": page_text
                })
                full_text += f"\n\n--- 第{page_num + 1}页 ---\n\n" + page_text
            
            # 智能分块处理
            chunks = self.text_splitter.split_text(full_text)
            
            result = []
            for i, chunk in enumerate(chunks):
                # 尝试确定分块来源页面
                source_pages = self._identify_source_pages(chunk, page_contents)
                
                result.append({
                    "content": chunk,
                    "metadata": {
                        "chunk_index": i,
                        "source": file_path,
                        "file_type": file_type,
                        "total_chunks": len(chunks),
                        "total_pages": len(reader.pages),
                        "source_pages": source_pages,
                        "processing_method": "advanced_pdf"
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"PDF处理失败: {e}")
            raise DocumentServiceError(f"PDF处理失败: {str(e)}")
    
    def _identify_source_pages(self, chunk: str, page_contents: List[Dict]) -> List[int]:
        """识别分块来源页面"""
        source_pages = []
        chunk_words = set(chunk.lower().split())
        
        for page_info in page_contents:
            page_words = set(page_info["content"].lower().split())
            # 计算词汇重叠度
            overlap = len(chunk_words.intersection(page_words))
            if overlap > len(chunk_words) * 0.3:  # 30%重叠度阈值
                source_pages.append(page_info["page"])
        
        return source_pages

class WordProcessor(DocumentProcessor):
    """Word文档处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理Word文件"""
        try:
            if not DocxDocument:
                raise DocumentServiceError("Word处理库未安装")
            
            doc = DocxDocument(file_path)
            
            # 提取文档结构信息
            paragraphs_info = []
            full_text = ""
            
            for i, paragraph in enumerate(doc.paragraphs):
                para_text = paragraph.text.strip()
                if para_text:
                    paragraphs_info.append({
                        "index": i,
                        "text": para_text,
                        "style": paragraph.style.name if paragraph.style else "Normal"
                    })
                    full_text += para_text + "\n\n"
            
            # 提取表格内容
            tables_info = []
            for table_idx, table in enumerate(doc.tables):
                table_text = self._extract_table_text(table)
                if table_text:
                    tables_info.append({
                        "index": table_idx,
                        "content": table_text
                    })
                    full_text += f"\n\n--- 表格 {table_idx + 1} ---\n\n" + table_text
            
            # 智能分块处理
            chunks = self.text_splitter.split_text(full_text)
            
            result = []
            for i, chunk in enumerate(chunks):
                result.append({
                    "content": chunk,
                    "metadata": {
                        "chunk_index": i,
                        "source": file_path,
                        "file_type": file_type,
                        "total_chunks": len(chunks),
                        "total_paragraphs": len(paragraphs_info),
                        "total_tables": len(tables_info),
                        "processing_method": "advanced_word"
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Word处理失败: {e}")
            raise DocumentServiceError(f"Word处理失败: {str(e)}")
    
    def _extract_table_text(self, table) -> str:
        """提取表格文本"""
        table_text = ""
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                row_text.append(cell.text.strip())
            table_text += " | ".join(row_text) + "\n"
        return table_text

class ExcelProcessor(DocumentProcessor):
    """Excel文档处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理Excel文件"""
        try:
            if not load_workbook:
                raise DocumentServiceError("Excel处理库未安装")
            
            workbook = load_workbook(file_path, data_only=True)
            full_text = ""
            sheets_info = []
            
            # 处理每个工作表
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_text = f"工作表: {sheet_name}\n"
                
                # 获取有数据的区域
                if sheet.max_row > 0 and sheet.max_column > 0:
                    for row in sheet.iter_rows(min_row=1, max_row=sheet.max_row, 
                                             min_col=1, max_col=sheet.max_column, 
                                             values_only=True):
                        row_text = []
                        for cell in row:
                            if cell is not None:
                                row_text.append(str(cell))
                            else:
                                row_text.append("")
                        
                        if any(cell.strip() for cell in row_text):  # 跳过空行
                            sheet_text += " | ".join(row_text) + "\n"
                
                sheets_info.append({
                    "name": sheet_name,
                    "rows": sheet.max_row,
                    "columns": sheet.max_column
                })
                
                full_text += sheet_text + "\n\n"
            
            # 智能分块处理
            chunks = self.text_splitter.split_text(full_text)
            
            result = []
            for i, chunk in enumerate(chunks):
                result.append({
                    "content": chunk,
                    "metadata": {
                        "chunk_index": i,
                        "source": file_path,
                        "file_type": file_type,
                        "total_chunks": len(chunks),
                        "sheets_info": sheets_info,
                        "processing_method": "advanced_excel"
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"Excel处理失败: {e}")
            raise DocumentServiceError(f"Excel处理失败: {str(e)}")

class TextProcessor(DocumentProcessor):
    """文本文档处理器"""
    
    async def process_file(self, file_path: str, file_type: str) -> List[Dict[str, Any]]:
        """处理文本文件"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16']
            content = None
            used_encoding = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                        used_encoding = encoding
                        break
                except UnicodeDecodeError:
                    continue
            
            if content is None:
                raise DocumentServiceError("无法解码文本文件")
            
            # 智能分块处理
            chunks = self.text_splitter.split_text(content)
            
            result = []
            for i, chunk in enumerate(chunks):
                result.append({
                    "content": chunk,
                    "metadata": {
                        "chunk_index": i,
                        "source": file_path,
                        "file_type": file_type,
                        "total_chunks": len(chunks),
                        "encoding": used_encoding,
                        "file_size": len(content),
                        "processing_method": "advanced_text"
                    }
                })
            
            return result
            
        except Exception as e:
            logger.error(f"文本处理失败: {e}")
            raise DocumentServiceError(f"文本处理失败: {str(e)}")

class DocumentService:
    """完整版文档处理服务"""
    
    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        
        # 注册处理器（使用简化的文件类型）
        self.processors = {
            "pdf": PDFProcessor(),
            "docx": WordProcessor(),
            "doc": WordProcessor(),
            "xlsx": ExcelProcessor(),
            "xls": ExcelProcessor(),
            "txt": TextProcessor(),
        }
        
        # 文件扩展名映射（简化版本，适应数据库50字符限制）
        self.extension_map = {
            ".pdf": "pdf",
            ".docx": "docx",
            ".doc": "doc",
            ".xlsx": "xlsx",
            ".xls": "xls",
            ".txt": "txt",
        }

        # MIME类型映射（用于文档处理）
        self.mime_type_map = {
            ".pdf": "application/pdf",
            ".docx": "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            ".doc": "application/msword",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".xls": "application/vnd.ms-excel",
            ".txt": "text/plain",
        }
    
    def get_file_type(self, filename: str) -> Optional[str]:
        """根据文件名获取文件类型（简化版本）"""
        ext = Path(filename).suffix.lower()
        return self.extension_map.get(ext)

    def get_mime_type(self, filename: str) -> Optional[str]:
        """根据文件名获取MIME类型"""
        ext = Path(filename).suffix.lower()
        return self.mime_type_map.get(ext)
    
    def is_supported_file(self, filename: str) -> bool:
        """检查是否支持该文件类型"""
        return self.get_file_type(filename) is not None
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """保存上传的文件"""
        try:
            # 生成唯一文件名
            timestamp = str(int(asyncio.get_event_loop().time()))
            safe_filename = f"{timestamp}_{filename}"
            file_path = self.upload_dir / safe_filename

            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存文件
            with open(file_path, "wb") as f:
                f.write(file_content)

            return str(file_path)

        except Exception as e:
            logger.error(f"保存文件失败: {e}")
            raise DocumentServiceError(f"保存文件失败: {str(e)}")
    
    async def process_document(self, file_path: str, filename: str) -> List[Dict[str, Any]]:
        """处理文档并返回分块"""
        try:
            file_type = self.get_file_type(filename)
            if not file_type:
                raise DocumentServiceError(f"不支持的文件类型: {filename}")
            
            processor = self.processors.get(file_type)
            if not processor:
                raise DocumentServiceError(f"没有找到对应的处理器: {file_type}")
            
            # 处理文档
            chunks = await processor.process_file(file_path, file_type)
            
            logger.info(f"成功处理文档 {filename}，生成 {len(chunks)} 个分块")
            return chunks
            
        except Exception as e:
            logger.error(f"文档处理失败: {e}")
            raise DocumentServiceError(f"文档处理失败: {str(e)}")
    
    async def delete_file(self, file_path: str):
        """删除文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"成功删除文件: {file_path}")
        except Exception as e:
            logger.error(f"删除文件失败: {e}")

# 全局文档服务实例
document_service = DocumentService()

def get_document_service() -> DocumentService:
    """获取文档服务实例"""
    return document_service
