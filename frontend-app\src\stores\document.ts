import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Document } from '@/api/document'
import { documentAPI } from '@/api/document'

export const useDocumentStore = defineStore('document', () => {
  // 状态
  const documents = ref<Document[]>([])
  const currentDocument = ref<Document | null>(null)
  const loading = ref(false)

  // 获取文档列表
  const fetchDocuments = async (kbId: number, params?: any) => {
    loading.value = true
    try {
      const data = await documentAPI.getList(kbId, params)
      documents.value = data
      return { success: true, data }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '获取文档列表失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 获取单个文档
  const fetchDocument = async (id: number) => {
    loading.value = true
    try {
      const document = await documentAPI.getDetail(id)
      currentDocument.value = document
      return { success: true, data: document }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '获取文档详情失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 上传文档
  const uploadDocuments = async (kbId: number, files: File[]) => {
    loading.value = true
    try {
      console.log('Store: 调用 batchUpload API, kbId:', kbId, 'files:', files.length)
      const result = await documentAPI.batchUpload(kbId, files)
      console.log('Store: API 响应:', result)

      // 添加成功上传的文档到列表
      if (result.uploaded_documents.length > 0) {
        documents.value.unshift(...result.uploaded_documents)
      }

      return { success: true, data: result }
    } catch (error: any) {
      console.error('Store: 上传失败:', error)
      return {
        success: false,
        message: error.response?.data?.message || error.message || '上传文档失败'
      }
    } finally {
      loading.value = false
    }
  }

  // 删除文档
  const deleteDocument = async (id: number) => {
    loading.value = true
    try {
      await documentAPI.delete(id)
      
      // 从列表中移除
      documents.value = documents.value.filter(doc => doc.id !== id)
      
      // 清除当前文档
      if (currentDocument.value?.id === id) {
        currentDocument.value = null
      }
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '删除文档失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 批量删除文档
  const batchDeleteDocuments = async (documentIds: number[]) => {
    loading.value = true
    try {
      const result = await documentAPI.batchDelete({ document_ids: documentIds })
      
      // 从列表中移除已删除的文档
      documents.value = documents.value.filter(doc => !documentIds.includes(doc.id))
      
      return { success: true, data: result }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '批量删除文档失败' 
      }
    } finally {
      loading.value = false
    }
  }

  // 下载文档
  const downloadDocument = async (id: number) => {
    try {
      const blob = await documentAPI.download(id)
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      
      // 获取文档信息设置文件名
      const doc = documents.value.find(d => d.id === id)
      link.download = doc?.filename || `document_${id}`
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      return { success: true }
    } catch (error: any) {
      return { 
        success: false, 
        message: error.response?.data?.message || '下载文档失败' 
      }
    }
  }

  // 清除当前文档
  const clearCurrentDocument = () => {
    currentDocument.value = null
  }

  return {
    // 状态
    documents,
    currentDocument,
    loading,
    
    // 方法
    fetchDocuments,
    fetchDocument,
    uploadDocuments,
    deleteDocument,
    batchDeleteDocuments,
    downloadDocument,
    clearCurrentDocument
  }
})
