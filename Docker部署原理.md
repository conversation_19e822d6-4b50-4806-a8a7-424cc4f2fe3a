# 🐳 Docker部署原理详解

## 🏗️ 容器化架构

### 1. 整体架构
```
┌─────────────────┐    ┌─────────────────┐
│   前端容器       │    │   后端容器       │
│   (Nginx)       │────│   (FastAPI)     │
│   Port: 80      │    │   Port: 8000    │
└─────────────────┘    └─────────────────┘
         │                       │
         └───────────┬───────────┘
                     │
    ┌─────────────────┴─────────────────┐
    │                                   │
┌─────────────────┐    ┌─────────────────┐
│  PostgreSQL     │    │     Redis       │
│  Port: 5432     │    │   Port: 6379    │
│  (数据存储)      │    │  (向量存储)      │
└─────────────────┘    └─────────────────┘
```

### 2. 容器职责分工

#### 前端容器 (Nginx)
- **作用**: 提供静态文件服务和API代理
- **技术**: Vue.js构建 + Nginx服务器
- **优势**: 
  - 高性能静态文件服务
  - 自动gzip压缩
  - 缓存优化
  - API请求代理

#### 后端容器 (FastAPI)
- **作用**: 提供REST API和业务逻辑
- **技术**: Python FastAPI + Uvicorn
- **优势**:
  - 高性能异步处理
  - 自动API文档
  - 类型安全
  - 易于扩展

#### 数据库容器 (PostgreSQL)
- **作用**: 持久化数据存储
- **技术**: PostgreSQL 15
- **优势**:
  - ACID事务支持
  - 强一致性
  - 丰富的数据类型
  - 扩展性好

#### 缓存容器 (Redis)
- **作用**: 向量数据存储和缓存
- **技术**: Redis 7
- **优势**:
  - 高性能内存存储
  - 支持复杂数据结构
  - 持久化选项
  - 集群支持

## 🔄 部署流程详解

### 1. 构建阶段 (Build)

#### 前端构建
```dockerfile
# 多阶段构建
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build  # 生成优化的静态文件
```

**过程**:
1. 安装Node.js依赖
2. 执行Vite构建
3. 自动移除console.log
4. 代码分割和压缩
5. 生成静态文件到dist目录

#### 后端构建
```dockerfile
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
```

**过程**:
1. 安装Python依赖
2. 复制应用代码
3. 设置环境变量
4. 配置启动命令

### 2. 运行阶段 (Runtime)

#### 服务启动顺序
1. **PostgreSQL** 首先启动
2. **Redis** 同时启动
3. **Backend** 等待数据库就绪后启动
4. **Frontend** 最后启动，依赖后端服务

#### 网络通信
- 所有容器在同一个Docker网络中
- 容器间通过服务名通信（如 `backend:8000`）
- 外部只暴露必要端口（80, 443）

### 3. 数据持久化

#### 数据卷 (Volumes)
```yaml
volumes:
  postgres_data:    # 数据库数据
  redis_data:       # Redis数据
  uploads:          # 上传文件
```

**优势**:
- 数据独立于容器生命周期
- 容器重启数据不丢失
- 便于备份和迁移

## 🚀 部署优势

### 1. 环境一致性
- **开发环境** = **测试环境** = **生产环境**
- 消除"在我机器上能跑"问题
- 依赖版本锁定

### 2. 快速部署
- 一键部署：`docker-compose up -d`
- 秒级启动
- 自动依赖管理

### 3. 易于扩展
```yaml
# 水平扩展后端服务
backend:
  scale: 3  # 启动3个后端实例
```

### 4. 资源隔离
- 每个服务独立运行
- 资源限制和监控
- 故障隔离

### 5. 版本管理
- 镜像版本化
- 回滚简单
- A/B测试支持

## 🔧 配置管理

### 1. 环境变量
```yaml
environment:
  DATABASE_URL: postgresql://...
  REDIS_URL: redis://...
  SECRET_KEY: ${SECRET_KEY}
```

### 2. 配置文件挂载
```yaml
volumes:
  - ./nginx.conf:/etc/nginx/nginx.conf
  - ./backend/config:/app/config
```

### 3. 密钥管理
- 使用Docker Secrets
- 环境变量注入
- 外部密钥管理系统

## 📊 监控和日志

### 1. 日志聚合
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务
docker-compose logs -f backend
```

### 2. 健康检查
```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
  interval: 30s
  timeout: 10s
  retries: 3
```

### 3. 资源监控
```bash
# 查看资源使用
docker stats

# 查看容器状态
docker-compose ps
```

## 🔐 安全考虑

### 1. 网络安全
- 内部网络隔离
- 最小权限原则
- 端口限制

### 2. 镜像安全
- 使用官方基础镜像
- 定期更新依赖
- 漏洞扫描

### 3. 数据安全
- 数据加密
- 访问控制
- 备份策略

## 🚀 生产环境优化

### 1. 性能优化
- 多阶段构建减小镜像大小
- 资源限制配置
- 缓存策略

### 2. 高可用性
- 多实例部署
- 负载均衡
- 故障转移

### 3. 自动化
- CI/CD集成
- 自动测试
- 自动部署
