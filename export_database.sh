#!/bin/bash

# 数据库导出脚本
# 在本地机器上运行此脚本

echo "📊 数据库导出工具"
echo "================"

# 获取用户输入
read -p "数据库主机 (默认: localhost): " DB_HOST
DB_HOST=${DB_HOST:-localhost}

read -p "数据库端口 (默认: 5432): " DB_PORT
DB_PORT=${DB_PORT:-5432}

read -p "数据库用户名: " postgres
read -p "数据库名称: " aiknowledgebase
read -s -p "数据库密码: " 111222
echo ""

# 创建导出目录
EXPORT_DIR="database_export_$(date +%Y%m%d_%H%M%S)"
mkdir -p $EXPORT_DIR

echo ""
echo "📁 导出目录: $EXPORT_DIR"
echo ""

# 设置密码环境变量
export PGPASSWORD=$DB_PASSWORD

echo "🔍 测试数据库连接..."
if psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT version();" > /dev/null 2>&1; then
    echo "✅ 数据库连接成功"
else
    echo "❌ 数据库连接失败，请检查连接参数"
    exit 1
fi

echo ""
echo "📊 开始导出数据库..."

# 1. 导出完整数据库
echo "1️⃣ 导出完整数据库..."
pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME > $EXPORT_DIR/full_backup.sql
if [ $? -eq 0 ]; then
    echo "✅ 完整备份导出成功: $EXPORT_DIR/full_backup.sql"
else
    echo "❌ 完整备份导出失败"
fi

# 2. 导出表结构
echo "2️⃣ 导出表结构..."
pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --schema-only > $EXPORT_DIR/schema_only.sql
if [ $? -eq 0 ]; then
    echo "✅ 表结构导出成功: $EXPORT_DIR/schema_only.sql"
else
    echo "❌ 表结构导出失败"
fi

# 3. 导出数据
echo "3️⃣ 导出数据..."
pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --data-only > $EXPORT_DIR/data_only.sql
if [ $? -eq 0 ]; then
    echo "✅ 数据导出成功: $EXPORT_DIR/data_only.sql"
else
    echo "❌ 数据导出失败"
fi

# 4. 导出表列表
echo "4️⃣ 导出表信息..."
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\dt" > $EXPORT_DIR/table_list.txt
psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT schemaname,tablename,n_live_tup FROM pg_stat_user_tables ORDER BY n_live_tup DESC;" > $EXPORT_DIR/table_stats.txt

# 5. 创建导入脚本
echo "5️⃣ 创建导入脚本..."
cat > $EXPORT_DIR/import_to_docker.sh << 'EOF'
#!/bin/bash

# Docker数据库导入脚本
# 在服务器上运行此脚本

CONTAINER_NAME="ai-knowledge-postgres-prod"
DB_NAME="aiknowledgebase"
DB_USER="postgres"

echo "📊 导入数据到Docker数据库"
echo "========================="

# 检查容器状态
if ! docker ps | grep -q $CONTAINER_NAME; then
    echo "❌ PostgreSQL容器未运行"
    echo "启动容器: docker-compose -f docker-compose.prod.yml up -d"
    exit 1
fi

echo "✅ PostgreSQL容器运行正常"

# 选择导入文件
echo ""
echo "可用的备份文件:"
ls -la *.sql

echo ""
read -p "请输入要导入的SQL文件名 (推荐: full_backup.sql): " SQL_FILE

if [ ! -f "$SQL_FILE" ]; then
    echo "❌ 文件不存在: $SQL_FILE"
    exit 1
fi

echo ""
echo "📁 复制文件到容器..."
docker cp "$SQL_FILE" $CONTAINER_NAME:/tmp/import.sql

echo "📊 导入数据..."
docker exec -it $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -f /tmp/import.sql

echo "🧹 清理临时文件..."
docker exec $CONTAINER_NAME rm /tmp/import.sql

echo "✅ 导入完成!"

echo ""
echo "🔍 验证导入结果:"
docker exec $CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -c "\dt"
EOF

chmod +x $EXPORT_DIR/import_to_docker.sh

# 清理密码环境变量
unset PGPASSWORD

echo ""
echo "🎉 导出完成!"
echo "============"
echo "导出目录: $EXPORT_DIR"
echo ""
echo "📁 导出的文件:"
ls -la $EXPORT_DIR/
echo ""
echo "📋 下一步操作:"
echo "1. 将整个 $EXPORT_DIR 目录上传到服务器"
echo "2. 在服务器上运行: cd $EXPORT_DIR && ./import_to_docker.sh"
echo ""
echo "💡 推荐使用 full_backup.sql 进行完整导入"
