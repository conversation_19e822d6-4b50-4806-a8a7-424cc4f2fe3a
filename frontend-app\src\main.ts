import './assets/main.css'
import 'element-plus/dist/index.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'

// 导入Element Plus图标
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

const app = createApp(App)
const pinia = createPinia()

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 确保Pinia在路由之前初始化
app.use(pinia)
app.use(router)

app.mount('#app')

// 在应用挂载后初始化设置
import('./stores/settings').then(({ useSettingsStore }) => {
  const settingsStore = useSettingsStore()
  settingsStore.initializeSettings()
})
