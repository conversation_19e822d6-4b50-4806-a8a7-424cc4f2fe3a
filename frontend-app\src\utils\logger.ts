/**
 * 日志工具
 * 在生产环境中自动禁用控制台输出
 */

const isDev = import.meta.env.DEV

export const logger = {
  log: (...args: any[]) => {
    if (isDev) {
      console.log(...args)
    }
  },
  
  info: (...args: any[]) => {
    if (isDev) {
      console.info(...args)
    }
  },
  
  warn: (...args: any[]) => {
    if (isDev) {
      console.warn(...args)
    }
  },
  
  error: (...args: any[]) => {
    // 错误信息在生产环境也保留，但可以选择性禁用
    if (isDev || import.meta.env.VITE_SHOW_ERRORS !== 'false') {
      console.error(...args)
    }
  },
  
  debug: (...args: any[]) => {
    if (isDev && import.meta.env.VITE_DEBUG === 'true') {
      console.debug(...args)
    }
  },
  
  table: (data: any) => {
    if (isDev) {
      console.table(data)
    }
  },
  
  group: (label: string) => {
    if (isDev) {
      console.group(label)
    }
  },
  
  groupEnd: () => {
    if (isDev) {
      console.groupEnd()
    }
  }
}

// 默认导出
export default logger

// 也可以直接导出方法
export const { log, info, warn, error, debug, table, group, groupEnd } = logger
