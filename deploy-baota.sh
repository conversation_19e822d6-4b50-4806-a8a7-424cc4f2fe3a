#!/bin/bash

# 宝塔面板专用部署脚本
# 适用于域名: aiknowledgebase.csicollege.cn

echo "🏗️ 宝塔面板AI知识库项目部署脚本"
echo "域名: aiknowledgebase.csicollege.cn"
echo "=================================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，正在安装..."
    curl -fsSL https://get.docker.com | bash
    systemctl start docker
    systemctl enable docker
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，正在安装..."
    curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
fi

# 验证安装
echo "🔍 验证Docker安装..."
docker --version
docker-compose --version

# 设置项目目录
PROJECT_DIR="/www/wwwroot/aiknowledgebase"
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ 项目目录不存在: $PROJECT_DIR"
    echo "请确保项目文件已上传到宝塔面板"
    exit 1
fi

cd $PROJECT_DIR

# 设置SSL证书
echo "🔐 设置SSL证书..."
if [ -f "setup-ssl.sh" ]; then
    chmod +x setup-ssl.sh
    ./setup-ssl.sh
else
    echo "⚠️  未找到SSL设置脚本，手动设置SSL..."
    mkdir -p ssl
    if [ -f "aiknowledgebase.csicollege.cn/full_chain.pem" ] && [ -f "aiknowledgebase.csicollege.cn/private.key" ]; then
        cp "aiknowledgebase.csicollege.cn/full_chain.pem" ssl/
        cp "aiknowledgebase.csicollege.cn/private.key" ssl/
        chmod 644 ssl/full_chain.pem
        chmod 600 ssl/private.key
        echo "✅ SSL证书设置完成"
    else
        echo "❌ SSL证书文件未找到，请检查证书路径"
        exit 1
    fi
fi

# 创建环境变量文件
if [ ! -f .env ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down 2>/dev/null || true

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker system prune -f

# 构建并启动生产环境服务
echo "🚀 启动生产环境服务..."
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 检查服务健康状态
echo "🔍 检查服务健康状态..."
sleep 10

# 测试HTTP连接
if curl -f -s http://localhost/health > /dev/null; then
    echo "✅ HTTP服务正常"
else
    echo "⚠️  HTTP服务可能有问题"
fi

# 测试HTTPS连接（如果SSL配置正确）
if curl -f -s -k https://localhost/health > /dev/null; then
    echo "✅ HTTPS服务正常"
else
    echo "⚠️  HTTPS服务可能有问题"
fi

# 显示日志
echo "📋 显示最近日志..."
docker-compose logs --tail=20

echo ""
echo "🎉 宝塔面板部署完成！"
echo "=================================="
echo "🌐 访问地址:"
echo "  - HTTPS: https://aiknowledgebase.csicollege.cn"
echo "  - HTTP: http://aiknowledgebase.csicollege.cn (自动重定向)"
echo ""
echo "🔧 管理命令:"
echo "  查看日志: docker-compose logs -f"
echo "  重启服务: docker-compose restart"
echo "  停止服务: docker-compose down"
echo "  更新服务: docker-compose up --build -d"
echo ""
echo "📝 注意事项:"
echo "1. 确保域名DNS已正确解析到服务器IP"
echo "2. 确保防火墙已开放80和443端口"
echo "3. 如果使用宝塔面板的Nginx，请停止或配置反向代理"
echo "4. 定期备份数据库和上传文件"
