"""
认证相关API
"""
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status
from sqlmodel import Session, select
from app.core.database import SessionDep
from app.core.security import verify_password, get_password_hash, create_access_token
from app.core.auth import get_current_user
from app.models.user import User, UserQuota
from app.schemas.user import UserCreate, UserLogin, Token, UserResponse

router = APIRouter()


@router.post("/register", response_model=UserResponse)
async def register(user_data: UserCreate, session: SessionDep):
    """用户注册"""
    # 检查用户名是否已存在
    statement = select(User).where(User.username == user_data.username)
    existing_user = session.exec(statement).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    statement = select(User).where(User.email == user_data.email)
    existing_email = session.exec(statement).first()
    if existing_email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建新用户
    hashed_password = get_password_hash(user_data.password)
    new_user = User(
        username=user_data.username,
        email=user_data.email,
        password_hash=hashed_password,
        display_name=user_data.display_name,
        bio=user_data.bio
    )
    
    session.add(new_user)
    session.commit()
    session.refresh(new_user)
    
    # 获取系统默认设置
    from app.models.system import SystemSetting
    from app.models.user import UserSettings

    # 获取默认存储配额
    default_storage_setting = session.exec(
        select(SystemSetting).where(SystemSetting.key == "default_storage_quota")
    ).first()
    default_storage_mb = int(default_storage_setting.value) if default_storage_setting else 1024

    # 获取默认模型设置
    default_model_setting = session.exec(
        select(SystemSetting).where(SystemSetting.key == "default_model_id")
    ).first()
    default_model_id = int(default_model_setting.value) if default_model_setting and default_model_setting.value else None

    # 创建用户配额（应用系统默认设置）
    user_quota = UserQuota(
        user_id=new_user.id,
        max_storage_mb=default_storage_mb
    )
    session.add(user_quota)

    # 创建用户设置（应用系统默认设置）
    user_settings = UserSettings(
        user_id=new_user.id,
        default_model_id=default_model_id
    )
    session.add(user_settings)

    session.commit()

    return new_user


@router.post("/login", response_model=Token)
async def login(user_credentials: UserLogin, session: SessionDep):
    """用户登录"""
    # 查找用户
    statement = select(User).where(User.username == user_credentials.username)
    user = session.exec(statement).first()
    
    if not user or not verify_password(user_credentials.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if user.status != "active":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户账户已被禁用"
        )
    
    # 更新最后登录时间
    user.last_login_at = datetime.utcnow()
    session.add(user)
    session.commit()
    
    # 创建访问令牌
    access_token_expires = timedelta(minutes=30)
    access_token = create_access_token(
        data={"sub": str(user.id)}, expires_delta=access_token_expires
    )
    
    return {"access_token": access_token, "token_type": "bearer"}


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: User = Depends(get_current_user)):
    """获取当前用户信息"""
    return current_user


@router.get("/quota")
async def get_user_quota(
    current_user: User = Depends(get_current_user),
    session: SessionDep = SessionDep
):
    """获取当前用户配额信息"""
    from app.models.user import UserQuota
    from app.models.knowledge_base import KnowledgeBase, Document
    from sqlmodel import select, func

    # 获取用户配额
    quota_statement = select(UserQuota).where(UserQuota.user_id == current_user.id)
    user_quota = session.exec(quota_statement).first()

    # 如果没有配额记录，创建默认配额
    if not user_quota:
        user_quota = UserQuota(
            user_id=current_user.id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(user_quota)
        session.commit()
        session.refresh(user_quota)

    # 计算当前使用量
    # 知识库数量
    kb_count_statement = select(func.count(KnowledgeBase.id)).where(
        KnowledgeBase.owner_id == current_user.id
    )
    current_kbs = session.exec(kb_count_statement).one()

    # 存储使用量
    storage_statement = select(func.sum(Document.file_size)).join(KnowledgeBase).where(
        KnowledgeBase.owner_id == current_user.id
    )
    current_storage_bytes = session.exec(storage_statement).one() or 0
    current_storage_mb = current_storage_bytes / (1024 * 1024)

    return {
        "max_kbs": user_quota.max_kbs,
        "max_docs_per_kb": user_quota.max_docs_per_kb,
        "max_storage_mb": user_quota.max_storage_mb,
        "current_kbs": current_kbs,
        "current_storage_mb": round(current_storage_mb, 2)
    }
