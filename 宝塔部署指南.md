# 🏗️ 宝塔面板Docker部署指南

## 📋 前置准备

### 1. 服务器要求
- **CPU**: 2核心以上
- **内存**: 4GB以上（推荐8GB）
- **存储**: 20GB以上可用空间
- **系统**: CentOS 7+/Ubuntu 18+/Debian 9+

### 2. 宝塔面板安装
```bash
# CentOS安装命令
yum install -y wget && wget -O install.sh http://download.bt.cn/install/install_6.0.sh && sh install.sh

# Ubuntu/Debian安装命令
wget -O install.sh http://download.bt.cn/install/install-ubuntu_6.0.sh && sudo bash install.sh
```

## 🐳 Docker环境配置

### 1. 在宝塔面板安装Docker
1. 登录宝塔面板
2. 进入 **软件商店**
3. 搜索并安装 **Docker管理器**
4. 安装 **Docker** 和 **Docker Compose**

### 2. 或者手动安装Docker
```bash
# 安装Docker
curl -fsSL https://get.docker.com | bash -s docker

# 启动Docker服务
systemctl start docker
systemctl enable docker

# 安装Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

## 📁 项目部署步骤

### 1. 上传项目文件
1. 在宝塔面板创建网站目录：`/www/wwwroot/aiknowledgebase`
2. 将项目文件上传到该目录
3. 确保文件结构如下：
```
/www/wwwroot/aiknowledgebase/
├── backend/
├── frontend-app/
├── aiknowledgebase.csicollege.cn/
│   ├── full_chain.pem
│   └── private.key
├── docker-compose.yml
├── docker-compose.prod.yml
├── nginx.prod.conf
├── .env.example
├── init.sql
├── deploy.sh
├── deploy-baota.sh
└── setup-ssl.sh
```

### 2. 一键部署（推荐）
使用专门的宝塔部署脚本：
```bash
cd /www/wwwroot/aiknowledgebase
chmod +x deploy-baota.sh
./deploy-baota.sh
```

### 3. 手动配置（可选）
如果需要手动配置：

1. 复制环境变量文件：
```bash
cd /www/wwwroot/aiknowledgebase
cp .env.example .env
```

2. 配置已使用项目实际配置：
- 数据库名: `aiknowledgebase`
- 数据库密码: `111222`
- JWT密钥: `abcXyz123_4x9KpQvE8jHmN2qRtSvWnZr5t7w-`
- SiliconFlow API密钥: `sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm`
- 域名: `aiknowledgebase.csicollege.cn`

### 3. 配置防火墙和端口
在宝塔面板 **安全** 页面添加端口：
- `80` (HTTP)
- `443` (HTTPS，如需要)
- `8000` (后端API，可选)

### 4. SSL证书配置
项目已包含您的SSL证书配置：
- 证书文件: `aiknowledgebase.csicollege.cn/full_chain.pem`
- 私钥文件: `aiknowledgebase.csicollege.cn/private.key`

部署脚本会自动处理SSL证书设置。

### 5. 执行部署
```bash
cd /www/wwwroot/aiknowledgebase
chmod +x deploy.sh
./deploy.sh
# 选择 "2" 进入生产环境模式
```

## 🌐 域名和SSL配置

### 1. 域名解析
将您的域名A记录指向服务器IP

### 2. 宝塔面板配置
1. 在宝塔面板 **网站** 页面添加站点
2. 域名填入您的域名
3. 根目录设置为：`/www/wwwroot/ai-knowledge`
4. 不创建数据库（Docker已包含）

### 3. Nginx反向代理配置
在宝塔面板网站设置中，配置Nginx：

```nginx
location / {
    proxy_pass http://127.0.0.1:80;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}

location /api/ {
    proxy_pass http://127.0.0.1:8000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    
    # 支持SSE流式响应
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_cache_bypass $http_upgrade;
    proxy_buffering off;
}
```

### 4. SSL证书配置
1. 在宝塔面板网站设置中点击 **SSL**
2. 选择 **Let's Encrypt** 免费证书
3. 点击申请证书

## 🔧 维护和监控

### 1. 查看服务状态
```bash
cd /www/wwwroot/ai-knowledge
docker-compose ps
```

### 2. 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 3. 重启服务
```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart backend
```

### 4. 更新应用
```bash
# 拉取最新代码
git pull

# 重新构建并部署
docker-compose up --build -d
```

### 5. 备份数据
```bash
# 备份数据库
docker exec ai-knowledge-postgres pg_dump -U postgres ai_knowledge_base > backup.sql

# 备份上传文件
tar -czf uploads_backup.tar.gz backend/uploads/
```

## 🚨 故障排除

### 1. 端口冲突
如果80端口被占用：
```bash
# 查看端口占用
netstat -tlnp | grep :80

# 停止占用进程或修改docker-compose.yml中的端口映射
```

### 2. 内存不足
```bash
# 查看内存使用
free -h

# 如果内存不足，考虑添加swap或升级服务器
```

### 3. 权限问题
```bash
# 确保目录权限正确
chown -R www:www /www/wwwroot/ai-knowledge
chmod -R 755 /www/wwwroot/ai-knowledge
```

## 📊 性能优化

### 1. 数据库优化
在`.env`中添加PostgreSQL优化配置

### 2. Redis优化
配置Redis持久化和内存限制

### 3. Nginx优化
启用gzip压缩和静态文件缓存

## 🔐 安全建议

1. **定期更新密码**
2. **启用防火墙**
3. **定期备份数据**
4. **监控系统资源**
5. **及时更新系统和应用**

{
  "type": "bar",
  "data": {
    "labels": ["曾彦", "孙海婷", "覃琴", "黄沁玥", "邓文雨", "胡海霞", "祝绍红", "任曦", "张越航", "汪耀辉", "胥恭丽", "廖斌", "耿莹"],
    "datasets": [{
      "label": "作业数量",
      "data": [4, 3, 3, 3, 2, 2, 2, 1, 1, 1, 1, 1, 1],
      "backgroundColor": [
        "#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF", 
        "#FF9F40", "#C9CBCF", "#E7E9ED", "#8A2BE2", "#7FFF00",
        "#D2691E", "#FF7F50", "#6495ED"
      ],
      "borderColor": "#fff",
      "borderWidth": 1
    }]
  },
  "options": {
    "responsive": true,
    "indexAxis": "y",
    "plugins": {
      "title": {
        "display": true,
        "text": "各老师布置作业数量统计",
        "font": {
          "size": 18
        }
      },
      "legend": {
        "display": false
      }
    },
    "scales": {
      "x": {
        "beginAtZero": true,
        "ticks": {
          "stepSize": 1
        }
      }
    }
  }
}
