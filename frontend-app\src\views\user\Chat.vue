<template>
  <!-- 页面加载状态 -->
  <div v-if="pageLoading" class="h-full w-full flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20">
    <div class="text-center">
      <el-icon :size="48" class="text-blue-500 animate-spin mb-4">
        <Loading />
      </el-icon>
      <p class="text-gray-600 dark:text-gray-400">正在加载聊天数据...</p>
    </div>
  </div>

  <!-- 主要内容 -->
  <div v-else class="h-full w-full flex bg-gradient-to-br from-slate-50 via-blue-50 to-purple-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20 overflow-hidden">
    <!-- 侧边栏 -->
    <div
      v-if="!uiStore.focusMode"
      :class="[
        'flex-shrink-0 bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-r border-gray-200/50 dark:border-gray-700/50 flex flex-col shadow-xl h-full transition-all duration-300',
        sidebarCollapsed ? 'w-16' : 'w-60'
      ]"
    >
      <!-- 侧边栏头部 -->
      <div class="p-4 border-b border-gray-200/50 dark:border-gray-700/50 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
        <div v-if="!sidebarCollapsed" class="flex items-center justify-between mb-4">
          <h2 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            对话历史
          </h2>
          <div class="flex items-center space-x-2">
            <el-button
              type="primary"
              size="small"
              @click="showNewChatDialog = true"
              class="bg-gradient-to-r from-blue-500 to-purple-600 border-0 hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200 rounded-xl"
            >
              <el-icon class="mr-1"><Plus /></el-icon>
              新建
            </el-button>
            <el-button
              @click="sidebarCollapsed = !sidebarCollapsed"
              size="small"
              text
              title="折叠侧边栏"
              class="text-gray-500 hover:text-blue-600 transition-colors"
            >
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
          </div>
        </div>

        <!-- 折叠状态的按钮组 -->
        <div v-else class="flex flex-col items-center space-y-2 py-2">
          <el-button
            type="primary"
            size="small"
            @click="showNewChatDialog = true"
            class="bg-gradient-to-r from-blue-500 to-purple-600 border-0 hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg w-10 h-10 p-0 flex items-center justify-center"
            title="新建对话"
          >
            <el-icon :size="16"><Plus /></el-icon>
          </el-button>
          <el-button
            @click="sidebarCollapsed = !sidebarCollapsed"
            size="small"
            text
            title="展开侧边栏"
            class="text-gray-500 hover:text-blue-600 transition-colors w-10 h-10 p-0 flex items-center justify-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <el-icon :size="16"><ArrowRight /></el-icon>
          </el-button>
        </div>

        <!-- 搜索框 -->
        <el-input
          v-if="!sidebarCollapsed"
          v-model="sessionSearchQuery"
          placeholder="搜索对话..."
          size="small"
          clearable
          class="search-input"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>

      <!-- 会话列表 -->
      <div class="flex-1 overflow-y-auto">
        <div v-if="filteredSessions.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400">
          <el-icon :size="32" class="mb-2"><ChatDotRound /></el-icon>
          <p class="text-sm">{{ sessionSearchQuery ? '未找到匹配的对话' : '还没有对话记录' }}</p>
        </div>

        <div v-else :class="sidebarCollapsed ? 'p-1 space-y-2' : 'p-3 space-y-3'">
          <div
            v-for="session in filteredSessions"
            :key="session.id"
            :class="[
              'cursor-pointer transition-all duration-300 group backdrop-blur-sm border shadow-sm hover:shadow-lg',
              sidebarCollapsed
                ? 'p-2 rounded-lg mx-1'
                : 'p-4 rounded-2xl bg-white/60 dark:bg-gray-800/60 border-gray-200/30 dark:border-gray-700/30',
              currentSession?.id === session.id
                ? 'bg-gradient-to-r from-blue-500/15 to-purple-500/15 border-blue-400/50 shadow-lg ring-2 ring-blue-400/20'
                : 'hover:bg-white/80 dark:hover:bg-gray-800/80 hover:border-gray-300/50 dark:hover:border-gray-600/50'
            ]"
            @click="selectSession(session)"
            :title="sidebarCollapsed ? session.title : ''"
          >
            <div v-if="sidebarCollapsed" class="flex justify-center">
              <el-icon :size="16" class="text-blue-500">
                <ChatDotRound />
              </el-icon>
            </div>
            <div v-else class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <h3 class="font-medium text-sm truncate">
                  {{ session.title }}
                </h3>
                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                  {{ formatRelativeTime(session.updated_at) }}
                </p>
                <div class="flex items-center mt-2 text-xs text-gray-400">
                  <el-icon class="mr-1"><Avatar /></el-icon>
                  AI助手
                  <span v-if="selectedKnowledgeBases.length > 0" class="ml-2 flex items-center">
                    <el-icon class="mr-1"><Collection /></el-icon>
                    {{ selectedKnowledgeBases.length }}
                  </span>
                </div>
              </div>
              <el-dropdown @command="(command: string) => handleSessionAction(command, session)" trigger="click">
                <el-button circle size="small" class="opacity-0 group-hover:opacity-100">
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="rename">
                      <el-icon><Edit /></el-icon>
                      重命名
                    </el-dropdown-item>
                    <el-dropdown-item command="export">
                      <el-icon><Download /></el-icon>
                      导出
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户信息 -->
      <div class="p-4 border-t border-gray-200 dark:border-dark-700">
        <div class="flex items-center space-x-3">
          <el-avatar :size="32" :src="userStore.user?.avatar_url">
            {{ userStore.user?.username?.charAt(0).toUpperCase() }}
          </el-avatar>
          <div class="flex-1 min-w-0">
            <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
              {{ userStore.user?.display_name || userStore.user?.username }}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              在线
            </p>
          </div>
          <el-button circle size="small" @click="$router.push('/user/settings')">
            <el-icon><Setting /></el-icon>
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="flex-1 flex flex-col min-w-0 overflow-hidden transition-all duration-300" style="width: 100% !important; max-width: none !important;">
      <!-- 聊天头部 -->
      <div class="bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-b border-gray-200/50 dark:border-gray-700/50 p-3 shadow-sm flex-shrink-0 transition-all duration-300">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <h1 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {{ currentSession?.title || '选择或创建对话' }}
            </h1>
            <el-select
              v-if="currentSession"
              v-model="currentModel"
              placeholder="选择AI模型"
              size="small"
              style="width: 180px"
              @change="handleModelChange"
              class="model-select"
            >
              <el-option
                v-for="model in availableModels"
                :key="model.id"
                :label="`${model.display_name || model.name} (${model.provider})`"
                :value="model.id.toString()"
              >
                <span>{{ model.display_name || model.name }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{ model.provider }}</span>
              </el-option>
            </el-select>
          </div>

          <div v-if="currentSession" class="flex items-center space-x-2">
            <el-button
              size="small"
              @click="toggleFocusMode"
              :type="uiStore.focusMode ? 'primary' : 'default'"
              class="tech-button"
              :title="uiStore.focusMode ? '退出专注模式' : '进入专注模式'"
            >
              <el-icon class="mr-1"><FullScreen /></el-icon>
              {{ uiStore.focusMode ? '退出专注' : '专注模式' }}
            </el-button>
            <el-button
              size="small"
              @click="showKnowledgeBasePanel = !showKnowledgeBasePanel"
              class="tech-button"
              :type="showKnowledgeBasePanel ? 'primary' : 'default'"
            >
              <el-icon class="mr-1"><Collection /></el-icon>
              知识库 ({{ selectedKnowledgeBases.length }})
            </el-button>
            <el-button size="small" @click="exportChat" class="tech-button">
              <el-icon class="mr-1"><Download /></el-icon>
              导出
            </el-button>
            <el-button size="small" @click="clearContext" class="tech-button-warning">
              <el-icon class="mr-1"><RefreshLeft /></el-icon>
              清空上下文
            </el-button>
          </div>
        </div>
      </div>

      <div class="flex-1 flex overflow-hidden" style="width: 100% !important; max-width: none !important;">
        <!-- 消息区域 -->
        <div class="flex-1 flex flex-col min-w-0 overflow-hidden message-container relative" style="width: 100% !important; max-width: none !important;">
          <!-- 专注模式退出按钮 -->
          <div v-if="uiStore.focusMode" class="absolute top-4 left-4 z-50">
            <el-button
              @click="toggleFocusMode"
              type="primary"
              size="small"
              circle
              class="shadow-lg hover:shadow-xl transition-all duration-200"
              title="退出专注模式"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          <!-- 消息流 -->
          <div ref="messagesContainer" class="flex-1 overflow-y-auto px-4 py-4 space-y-6 min-h-0">
            <div class="w-full">
              <!-- 空状态 -->
              <div v-if="!currentSession" class="h-full flex items-center justify-center">
                <div class="text-center">
                  <el-icon :size="64" class="text-gray-400 mb-4">
                    <ChatDotRound />
                  </el-icon>
                  <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                    开始新的对话
                  </h3>
                  <p class="text-gray-600 dark:text-gray-400 mb-6">
                    选择一个现有对话或创建新的对话来开始与AI助手交流
                  </p>
                  <el-button type="primary" @click="showNewChatDialog = true" class="btn-tech">
                    <el-icon class="mr-2"><Plus /></el-icon>
                    创建新对话
                  </el-button>
                </div>
              </div>

              <!-- 消息列表 -->
              <div v-else-if="currentMessages.length === 0" class="h-full flex items-center justify-center">
                <div class="text-center">
                  <el-icon :size="48" class="text-gray-400 mb-4">
                    <MessageBox />
                  </el-icon>
                  <p class="text-gray-600 dark:text-gray-400">
                    开始您的第一条消息...
                  </p>
                </div>
              </div>

              <div v-else class="space-y-6">
                <div
                  v-for="message in currentMessages"
                  :key="message.id"
                  class="flex w-full group"
                  :class="message.role === 'user' ? 'justify-end' : 'justify-start'"
                >
                  <!-- AI消息 -->
                  <div v-if="message.role === 'assistant'" class="flex space-x-4 w-full">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                      <el-icon :size="18" class="text-white">
                        <ChatDotRound />
                      </el-icon>
                    </div>
                    <div class="flex-1 min-w-0 max-w-[66%]">
                      <div :class="[
                        'backdrop-blur-xl rounded-2xl p-6 shadow-lg border ai-message-content',
                        message.isError
                          ? 'bg-red-50/90 dark:bg-red-900/20 border-red-200/50 dark:border-red-700/50 overflow-hidden'
                          : 'bg-white/90 dark:bg-gray-900/90 border-gray-200/30 dark:border-gray-700/30 overflow-visible'
                      ]">
                        <!-- 思考中状态 -->
                        <div v-if="!message.content.trim() && (isTyping || isSending) && isLastAssistantMessage(message)" class="flex items-center space-x-2 text-gray-600 dark:text-gray-400">
                          <div class="flex space-x-1">
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                          </div>
                          <span class="text-sm">思考中...</span>
                        </div>
                        <!-- 消息内容 -->
                        <div v-else>
                          <!-- 错误消息特殊显示 -->
                          <div v-if="message.isError" class="flex items-start space-x-3">
                            <el-icon class="text-red-500 mt-1 flex-shrink-0" size="18">
                              <WarningFilled />
                            </el-icon>
                            <div class="flex-1">
                              <div class="text-red-700 dark:text-red-300 text-sm leading-relaxed">{{ message.content }}</div>
                              <div class="mt-2 text-xs text-red-600 dark:text-red-400">
                                请检查模型设置或联系技术支持
                              </div>
                            </div>
                          </div>
                          <!-- 正常消息显示 -->
                          <div v-else class="prose dark:prose-invert max-w-none markdown-content break-words overflow-hidden text-xs leading-relaxed" v-html="renderMarkdown(message.content)"></div>

                          <!-- 智能图表和HTML渲染 -->
                          <template v-if="message.id != null">
                            <template v-for="(strategy, strategyIndex) in [getDisplayStrategy(message.id, message.content, streamingMessageIds.has(message.id.toString()))]" :key="`${message.id}-strategy-${strategyIndex}`">
                              <!-- HTML预览 -->
                              <div v-if="strategy.showHtml && strategy.htmlBlocks.length > 0" class="mt-4">
                                <HtmlPreview
                                  v-for="(html, index) in strategy.htmlBlocks"
                                  :key="`${message.id}-html-${index}`"
                                  :html-content="html.content"
                                  :height="'auto'"
                                  :min-height="'400px'"
                                  :max-height="'800px'"
                                />
                              </div>

                              <!-- 图表渲染 -->
                              <div v-if="strategy.showCharts && strategy.chartBlocks.length > 0" class="mt-4">
                                <ChartRenderer
                                  v-for="(chart, index) in strategy.chartBlocks"
                                  :key="`${message.id}-chart-${index}`"
                                  :chart-data="chart"
                                />
                              </div>
                            </template>
                          </template>
                        </div>

                      <!-- 引用来源 -->
                      <div v-if="message.sources && message.sources.length > 0" class="mt-4 pt-4 border-t border-gray-200 dark:border-dark-700">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                          参考来源：
                        </h4>
                        <div class="space-y-2">
                          <div
                            v-for="source in message.sources"
                            :key="source.documentId"
                            class="p-2 bg-gray-50 dark:bg-dark-700 rounded text-sm"
                          >
                            <div class="flex items-center justify-between">
                              <span class="font-medium text-gray-900 dark:text-gray-100">
                                {{ source.documentName }}
                              </span>
                              <span class="text-xs text-gray-500">
                                相关度: {{ Math.round(source.relevance * 100) }}%
                              </span>
                            </div>
                            <p class="text-gray-600 dark:text-gray-400 mt-1">
                              {{ source.excerpt }}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 消息操作 -->
                    <div class="flex items-center space-x-2 mt-2 opacity-100 transition-opacity duration-200">
                      <el-button size="small" text @click="copyMessage(message.content)" title="复制">
                        <el-icon><CopyDocument /></el-icon>
                      </el-button>
                      <el-button size="small" text @click="regenerateMessage(message)" title="重新生成">
                        <el-icon><RefreshLeft /></el-icon>
                      </el-button>
                      <el-button size="small" text @click="deleteMessage(message)" title="删除">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                      <span class="text-xs text-gray-500">
                        {{ formatTime(message.created_at) }}
                      </span>
                    </div>
                  </div>
                </div>

                <!-- 用户消息 -->
                <div v-else class="flex space-x-4 w-full justify-end">
                  <div class="max-w-3xl">
                    <div class="bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-2xl p-4 shadow-lg">
                      <div class="whitespace-pre-wrap break-words text-xs leading-relaxed">{{ message.content }}</div>
                    </div>

                    <!-- 消息操作 -->
                    <div class="flex items-center justify-end space-x-2 mt-2 opacity-100 transition-opacity duration-200">
                      <el-button size="small" text @click="resendMessage(message)" title="重新发送">
                        <el-icon><Promotion /></el-icon>
                      </el-button>
                      <el-button size="small" text @click="editMessage(message)" title="编辑">
                        <el-icon><Edit /></el-icon>
                      </el-button>
                      <el-button size="small" text @click="deleteMessage(message)" title="删除">
                        <el-icon><Delete /></el-icon>
                      </el-button>
                      <span class="text-xs text-gray-500">
                        {{ formatTime(message.created_at) }}
                      </span>
                    </div>
                  </div>
                  <div class="w-10 h-10 bg-gradient-to-br from-green-400 to-blue-500 rounded-xl flex items-center justify-center flex-shrink-0 shadow-lg">
                    <span class="text-white font-bold text-sm">{{ userStore.user?.username?.charAt(0).toUpperCase() }}</span>
                  </div>
                </div>
              </div>



              <!-- 建议问题 -->
              <div v-if="currentMessages.length > 0 && !isTyping && !isSending" class="mt-6">
                <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">💡 您可能还想了解：</div>
                <div class="flex flex-wrap gap-2">
                  <el-button
                    v-for="suggestion in suggestedQuestions"
                    :key="suggestion"
                    size="small"
                    type="primary"
                    plain
                    @click="sendSuggestedQuestion(suggestion)"
                    class="suggestion-button"
                  >
                    {{ suggestion }}
                  </el-button>
                </div>
              </div>
            </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div v-if="currentSession" class="bg-white/90 dark:bg-gray-900/90 backdrop-blur-xl border-t border-gray-200/50 dark:border-gray-700/50 px-4 py-2 shadow-lg flex-shrink-0">
            <div class="w-full">
              <div class="flex items-start space-x-3">
                <div class="flex-1">
                  <el-input
                    v-model="messageInput"
                    type="textarea"
                    placeholder="输入您的消息... (Shift+Enter 换行，Enter 发送)"
                    :rows="Math.min(messageInput.split('\n').length, 3)"
                    :max-rows="3"
                    resize="none"
                    @keydown="handleInputKeydown"
                    class="message-input"
                  />

                  <!-- 输入提示 -->
                  <div class="flex items-center justify-between mt-2 text-xs text-gray-500">
                    <div class="flex items-center space-x-4">
                      <span>Token 使用: {{ tokenCount }}/{{ typeof maxTokens === 'string' ? maxTokens : maxTokens }}</span>
                      <span v-if="selectedKnowledgeBases.length > 0">
                        知识库: {{ selectedKnowledgeBases.length }} 个
                      </span>
                      <span>上下文: {{ contextLength }}/{{ currentSessionMessageCount }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <el-button size="small" text @click="showContextSettings = !showContextSettings" title="上下文设置">
                        <el-icon><Setting /></el-icon>
                      </el-button>

                      <el-button size="small" text @click="attachFile">
                        <el-icon><Paperclip /></el-icon>
                      </el-button>
                    </div>
                  </div>

                  <!-- 上下文长度设置面板 -->
                  <div v-if="showContextSettings" class="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between mb-2">
                      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">AI上下文长度设置</span>
                      <el-button size="small" text @click="showContextSettings = false">
                        <el-icon><Close /></el-icon>
                      </el-button>
                    </div>

                    <div class="space-y-3">
                      <div class="flex items-center space-x-3">
                        <span class="text-xs text-gray-600 dark:text-gray-400 w-20">当前会话:</span>
                        <span class="text-xs font-medium text-blue-600 dark:text-blue-400">{{ currentSessionMessageCount }} 条消息</span>
                      </div>

                      <div class="space-y-2">
                        <div class="flex items-center space-x-3">
                          <span class="text-xs text-gray-600 dark:text-gray-400 w-20">AI获取:</span>
                          <div class="flex-1">
                            <el-slider
                              v-model="contextLengthDisplay"
                              :min="1"
                              :max="Math.max(currentSessionMessageCount, 20)"
                              :step="1"
                              show-input
                              :show-input-controls="false"
                              size="small"
                              class="context-slider"
                              :disabled="isUsingAllMessages"
                              @input="handleContextLengthChange"
                            />
                          </div>
                          <el-input-number
                            v-model="contextLengthDisplay"
                            :min="1"
                            :max="Math.max(currentSessionMessageCount, 20)"
                            size="small"
                            class="w-20"
                            :disabled="isUsingAllMessages"
                            @input="handleContextLengthChange"
                          />
                        </div>

                        <div class="flex items-center space-x-2">
                          <el-button
                            size="small"
                            :type="isUsingAllMessages ? 'primary' : ''"
                            @click="setUseAllMessages"
                            class="text-xs"
                          >
                            全部消息
                          </el-button>
                          <el-button
                            v-if="isUsingAllMessages"
                            size="small"
                            @click="setUseLimitedMessages"
                            class="text-xs"
                          >
                            恢复限制
                          </el-button>
                        </div>
                      </div>

                      <div class="text-xs text-gray-500 dark:text-gray-400">
                        <template v-if="isUsingAllMessages">
                          AI将使用当前会话的全部 {{ currentSessionMessageCount }} 条消息作为上下文。
                        </template>
                        <template v-else>
                          AI将使用最近 {{ contextLength }} 条消息作为上下文。更多上下文可能提供更好的连贯性，但会消耗更多tokens。
                        </template>
                      </div>
                    </div>
                  </div>
                </div>

                <el-button
                  :type="isTyping ? 'danger' : 'primary'"
                  :loading="isSending && !isTyping"
                  :disabled="!isTyping && (!messageInput.trim() || isSending)"
                  @click="handleSendButtonClick"
                  class="send-button"
                  size="large"
                  :title="isTyping ? '停止生成' : '发送消息'"
                >
                  <el-icon v-if="!isTyping"><Promotion /></el-icon>
                  <el-icon v-else><VideoPause /></el-icon>
                </el-button>
              </div>


            </div>
          </div>
        </div>

        <!-- 知识库面板遮罩 -->
        <div
          v-if="showKnowledgeBasePanel && currentSession"
          class="fixed inset-0 bg-black bg-opacity-20 z-[9998] transition-opacity duration-300"
          @click="showKnowledgeBasePanel = false"
        ></div>

        <!-- 知识库面板 - 浮动抽屉模式 -->
        <div
          v-if="showKnowledgeBasePanel && currentSession"
          class="fixed right-0 top-0 w-80 h-screen bg-white dark:bg-dark-800 border-l border-gray-200 dark:border-dark-700 flex flex-col shadow-2xl z-[9999] transform transition-transform duration-300"
        >
          <div class="p-4 border-b border-gray-200 dark:border-dark-700">
            <div class="flex items-center justify-between">
              <h3 class="font-medium text-gray-900 dark:text-gray-100">
                知识库设置
              </h3>
              <el-button circle size="small" @click="showKnowledgeBasePanel = false">
                <el-icon><Close /></el-icon>
              </el-button>
            </div>
          </div>

          <div class="flex-1 overflow-hidden flex flex-col">
            <!-- 知识库选择区域 -->
            <div class="flex-1 overflow-hidden p-4 pb-0">
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                选择知识库
              </h4>
              <!-- 知识库列表容器 - 占用剩余空间，可滚动 -->
              <div class="h-full overflow-y-auto border border-gray-200/30 dark:border-gray-700/30 rounded-xl bg-gray-50/50 dark:bg-gray-800/50 p-2">
                <div class="space-y-2">
                  <div
                    v-for="kb in availableKnowledgeBases"
                    :key="kb.id"
                    class="flex items-center justify-between p-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-200/30 dark:border-gray-700/30 rounded-lg shadow-sm hover:shadow-md transition-all duration-300"
                  >
                    <div class="flex items-center space-x-2">
                      <el-checkbox
                        :model-value="selectedKnowledgeBases.includes(kb.id)"
                        @change="toggleKnowledgeBase(kb.id)"
                      />
                      <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {{ kb.name }}
                        </div>
                        <div class="text-xs text-gray-500">
                          知识库
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 相关度阈值设置 - 固定在底部 -->
            <div class="flex-shrink-0 border-t border-gray-200/30 dark:border-gray-700/30 p-4">
              <h4 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                相关度阈值
                <span v-if="selectedKnowledgeBases.length > 0" class="text-xs text-blue-500 ml-2">
                  (已选择 {{ selectedKnowledgeBases.length }} 个知识库)
                </span>
              </h4>

              <el-slider
                v-model="relevanceThreshold"
                :min="0"
                :max="1"
                :step="0.1"
                show-stops
                show-input
                :disabled="selectedKnowledgeBases.length === 0"
              />

              <div class="text-xs text-gray-500 mt-2 space-y-2">
                <p v-if="selectedKnowledgeBases.length > 0">
                  只有相关度高于此阈值的知识库内容才会被AI引用
                </p>
                <p v-else class="text-orange-500">
                  请先选择知识库，然后调整相关度阈值
                </p>

                <div class="grid grid-cols-2 gap-2 text-xs opacity-75">
                  <div>0.0-0.3: 几乎不相关</div>
                  <div>0.3-0.5: 低相关</div>
                  <div>0.5-0.7: 中等相关</div>
                  <div>0.7-1.0: 高相关</div>
                </div>
                <p class="opacity-75 leading-relaxed">
                  建议值：0.4-0.6，过高可能错过有用信息，过低可能引入噪音
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建对话对话框 -->
    <el-dialog
      v-model="showNewChatDialog"
      title="创建新对话"
      width="600px"
      @close="resetNewChatForm"
      class="custom-dialog"
      :show-close="true"
      center
    >
      <el-form :model="newChatForm" :rules="newChatRules" ref="newChatFormRef" label-width="100px">
        <el-form-item label="对话标题" prop="title">
          <el-input
            v-model="newChatForm.title"
            placeholder="请输入对话标题（可选）"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="AI模型" prop="model">
          <el-select v-model="newChatForm.model" placeholder="选择AI模型" style="width: 100%">
            <el-option
              v-for="model in availableModels"
              :key="model.id"
              :label="`${model.display_name || model.name} (${model.provider})`"
              :value="model.id"
            >
              <span>{{ model.display_name || model.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ model.provider }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="知识库">
          <el-select
            v-model="newChatForm.knowledgeBases"
            multiple
            placeholder="选择要使用的知识库（可选）"
            style="width: 100%"
          >
            <el-option
              v-for="kb in availableKnowledgeBases"
              :key="kb.id"
              :label="kb.name"
              :value="kb.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="上下文长度">
          <el-select v-model="newChatForm.contextLength" style="width: 100%">
            <el-option label="短 (最近5条消息)" :value="5" />
            <el-option label="中 (最近10条消息)" :value="10" />
            <el-option label="长 (最近20条消息)" :value="20" />
            <el-option label="超长 (最近50条消息)" :value="50" />
            <el-option label="全部消息" :value="-1" />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showNewChatDialog = false">取消</el-button>
          <el-button type="primary" @click="createNewChat" :loading="creatingChat">
            创建
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.markdown-content h1 {
  font-size: 1.5em;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.3em;
}

.markdown-content h2 {
  font-size: 1.3em;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0.3em;
}

.markdown-content h3 {
  font-size: 1.1em;
}

.markdown-content p {
  margin-bottom: 1em;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.markdown-content li {
  margin-bottom: 0.25em;
}

.markdown-content blockquote {
  border-left: 4px solid #3b82f6;
  padding-left: 1em;
  margin: 1em 0;
  color: #6b7280;
  font-style: italic;
}

.markdown-content .code-block-container {
  margin: 1em 0;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.dark .markdown-content .code-block-container {
  border-color: #374151;
}

.markdown-content .inline-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.markdown-content pre {
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875em;
}

.markdown-content .table-container table {
  border-collapse: collapse;
}

.markdown-content .table-container th,
.markdown-content .table-container td {
  padding: 0.75rem;
  text-align: left;
  border: 1px solid #e5e7eb;
}

.dark .markdown-content .table-container th,
.dark .markdown-content .table-container td {
  border-color: #374151;
}

.markdown-content .table-container th {
  font-weight: 600;
  background-color: #f9fafb;
}

.dark .markdown-content .table-container th {
  background-color: #1f2937;
}

/* 确保代码块正确显示 */
.markdown-content .code-block-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.dark .markdown-content .code-block-container {
  background: #1a1a1a;
  border: 1px solid #333;
}

.markdown-content .code-block-header {
  border-bottom: 1px solid #e9ecef;
}

.dark .markdown-content .code-block-header {
  border-bottom: 1px solid #333;
}

.markdown-content pre.hljs {
  background: #f8f9fa !important;
  color: #333 !important;
}

.dark .markdown-content pre.hljs {
  background: #1a1a1a !important;
  color: #e6e6e6 !important;
}
</style>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { useKnowledgeBaseStore } from '@/stores/knowledgeBase'
import { useUIStore } from '@/stores/ui'
import { useSettingsStore } from '@/stores/settings'
import { useAvailableModels } from '@/composables/useAvailableModels'
import type { ChatSession, ChatMessage } from '@/api/chat'

interface CreateChatForm {
  title: string
  model: number  // 修改为number类型
  knowledgeBases: number[]
  contextLength?: number
}
import {
  Plus,
  Search,
  ChatDotRound,
  Collection,
  MoreFilled,
  Edit,
  Download,
  Delete,
  Setting,
  RefreshLeft,
  MessageBox,
  CopyDocument,
  Paperclip,
  Promotion,
  Close,
  Avatar,
  VideoPause,
  ArrowLeft,
  ArrowRight,
  FullScreen,
  WarningFilled,
  Loading
} from '@element-plus/icons-vue'
import { renderMarkdown, copyMessage as copyMessageUtil } from '@/utils/markdown'
import { chatAPI } from '@/api/chat'
import ChartRenderer from '@/components/ChartRenderer.vue'
import HtmlPreview from '@/components/HtmlPreview.vue'
import { parseChartCode, extractChartBlocks } from '@/utils/chartParser'
import { extractHtmlBlocks, shouldPrioritizeHtml } from '@/utils/htmlParser'

const route = useRoute()

const userStore = useAuthStore()
const chatStore = useChatStore()
const knowledgeBaseStore = useKnowledgeBaseStore()
const uiStore = useUIStore()
const settingsStore = useSettingsStore()
const { availableModels: computedAvailableModels, initializeData: initializeModelsData } = useAvailableModels()

// 响应式数据
const pageLoading = ref(false) // 页面加载状态
const sessionSearchQuery = ref('')
const currentSession = computed(() => chatStore.currentSession)
// 从localStorage读取上次选择的模型，如果没有则使用默认值
const currentModel = ref(localStorage.getItem('selectedModelId') || '')
const messageInput = ref('')
const isTyping = ref(false)
const isSending = ref(false)
const abortController = ref<AbortController | null>(null)
// 跟踪正在流式输出的消息ID
const streamingMessageIds = ref<Set<string>>(new Set())
const creatingChat = ref(false)
const showKnowledgeBasePanel = ref(false)
const showNewChatDialog = ref(false)
const showContextSettings = ref(false)
const contextLength = ref(parseInt(localStorage.getItem('contextLength') || '10')) // 从localStorage读取，默认10条消息
const contextLengthDisplay = ref(10) // 用于显示的上下文长度
const isUsingAllMessages = ref(false) // 是否使用全部消息
const sidebarCollapsed = ref(false) // 侧边栏折叠状态
const messagesContainer = ref<HTMLElement>()
const newChatFormRef = ref()

// 知识库相关 - 从localStorage读取上次选择的知识库
const selectedKnowledgeBases = ref<number[]>(
  JSON.parse(localStorage.getItem('selectedKnowledgeBases') || '[]')
)
const relevanceThreshold = ref(parseFloat(localStorage.getItem('relevanceThreshold') || '0.4'))

// 建议问题
const suggestedQuestions = ref<string[]>([
  '请详细解释一下',
  '有什么相关的例子吗？',
  '这个有什么优缺点？',
  '如何实际应用？',
  '还有其他方法吗？',
  '请用Markdown表格形式重新输出'
])

// 表单数据
const newChatForm = ref<CreateChatForm>({
  title: '',
  model: 0,  // 初始为0，在初始化时设置
  knowledgeBases: [],
  contextLength: 10
})

const newChatRules = {
  model: [
    { required: true, message: '请选择AI模型', trigger: 'change' }
  ]
}

// 使用store中的数据
const sessions = computed(() => chatStore.sessions)
const messages = computed(() => chatStore.messages)

// 可用模型和知识库数据
const availableModels = computed(() => {
  return computedAvailableModels.value.map(model => ({
    id: model.id,
    name: model.model_name,
    display_name: model.display_name,
    modelName: model.model_name,
    provider: model.provider?.display_name || model.provider?.name || 'Unknown',
    max_tokens: model.max_tokens,
    is_active: model.is_active
  }))
})
const availableKnowledgeBases = computed(() => knowledgeBaseStore.knowledgeBases)

// 计算属性
const filteredSessions = computed(() => {
  if (!sessionSearchQuery.value) {
    return sessions.value
  }

  const query = sessionSearchQuery.value.toLowerCase()
  return sessions.value.filter(session =>
    session.title.toLowerCase().includes(query)
  )
})

const currentMessages = computed(() => {
  if (!currentSession.value) return []
  return messages.value[currentSession.value.id] || []
})

// 当前会话消息数量
const currentSessionMessageCount = computed(() => {
  return currentMessages.value.length
})

const tokenCount = computed(() => {
  // 简单估算token数量
  const messageText = currentMessages.value.map(m => m.content).join(' ')
  return Math.ceil(messageText.length / 4)
})

const maxTokens = computed(() => {
  if (!currentSession.value) return 0

  // 根据当前选择的模型获取max_tokens
  const selectedModel = availableModels.value.find(model => model.id.toString() === currentModel.value)
  if (selectedModel) {
    // 如果max_tokens为null，表示无限制，显示"无限制"
    return selectedModel.max_tokens || '无限制'
  }

  return 8000 // 默认值
})

// 当前模型信息
const currentModelInfo = computed(() => {
  const selectedModel = availableModels.value.find(model => model.id.toString() === currentModel.value)
  if (selectedModel) {
    return {
      name: selectedModel.name,
      provider: selectedModel.provider,
      maxTokens: selectedModel.max_tokens,
      isActive: selectedModel.is_active,
      tokenDisplay: selectedModel.max_tokens ? `${selectedModel.max_tokens} tokens` : '无限制'
    }
  }
  return null
})

// 工具方法
const formatRelativeTime = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes} 分钟前`
  } else if (hours < 24) {
    return `${hours} 小时前`
  } else {
    return `${days} 天前`
  }
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 会话操作方法
const selectSession = async (session: ChatSession) => {
  const result = await chatStore.setCurrentSession(session.id)
  if (result.success) {
    // 保存当前会话ID到localStorage
    localStorage.setItem('currentSessionId', session.id.toString())
    // 滚动到底部
    await nextTick()
    scrollToBottom()
  } else {
    ElMessage.error(result.message || '切换会话失败')
  }
}

const handleSessionAction = async (command: string, session: ChatSession) => {
  switch (command) {
    case 'rename':
      await renameSession(session)
      break
    case 'export':
      await exportSession(session)
      break
    case 'delete':
      await deleteSession(session)
      break
  }
}

const renameSession = async (session: ChatSession) => {
  try {
    const { value: newTitle } = await ElMessageBox.prompt(
      '请输入新的对话标题',
      '重命名对话',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: session.title,
        inputValidator: (value) => {
          if (!value || value.trim().length === 0) {
            return '标题不能为空'
          }
          return true
        }
      }
    )

    const result = await chatStore.updateSessionTitle(session.id, newTitle.trim())

    if (result.success) {
      ElMessage.success('重命名成功')
    } else {
      ElMessage.error(result.message || '重命名失败')
    }
  } catch (error) {
    // 用户取消
  }
}

const exportSession = async (session: ChatSession) => {
  try {
    const sessionMessages = messages.value[session.id] || []
    const exportData = {
      title: session.title,
      createdAt: session.created_at,
      messages: sessionMessages
    }

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${session.title}.json`
    a.click()
    URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const deleteSession = async (session: ChatSession) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除对话"${session.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    const result = await chatStore.deleteSession(session.id)

    if (result.success) {
      ElMessage.success('对话删除成功')

      // 如果删除的是当前保存的会话，清理localStorage
      const savedSessionId = localStorage.getItem('currentSessionId')
      if (savedSessionId === session.id.toString()) {
        localStorage.removeItem('currentSessionId')
      }
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const createNewChat = async () => {
  if (!newChatFormRef.value) return

  try {
    await newChatFormRef.value.validate()
    creatingChat.value = true

    const sessionData = {
      title: newChatForm.value.title || `新对话 ${sessions.value.length + 1}`
    }

    const result = await chatStore.createSession(sessionData)

    if (result.success) {
      showNewChatDialog.value = false
      resetNewChatForm()
      ElMessage.success('新对话创建成功')

      // 保存新创建的会话ID到localStorage
      if (result.data && result.data.id) {
        localStorage.setItem('currentSessionId', result.data.id.toString())
      }
    } else {
      ElMessage.error(result.message || '创建失败')
    }
  } catch (error) {
    ElMessage.error('创建失败')
  } finally {
    creatingChat.value = false
  }
}

const resetNewChatForm = () => {
  // 获取当前可用的模型ID，如果没有则使用第一个可用模型
  let defaultModelId = 0

  if (currentModel.value && parseInt(currentModel.value)) {
    defaultModelId = parseInt(currentModel.value)
  } else if (availableModels.value.length > 0) {
    defaultModelId = availableModels.value[0].id
  }

  newChatForm.value = {
    title: '',
    model: defaultModelId,
    knowledgeBases: [],
    contextLength: 10
  }
  if (newChatFormRef.value) {
    newChatFormRef.value.resetFields()
  }
}

// 上下文长度管理方法
const handleContextLengthChange = (value: number) => {
  if (!isUsingAllMessages.value) {
    contextLength.value = value
  }
}

const setUseAllMessages = () => {
  isUsingAllMessages.value = true
  contextLength.value = -1
  contextLengthDisplay.value = currentSessionMessageCount.value || 10
}

const setUseLimitedMessages = () => {
  isUsingAllMessages.value = false
  contextLength.value = Math.min(currentSessionMessageCount.value || 10, 10)
  contextLengthDisplay.value = contextLength.value
}

// 初始化显示值
const initializeContextDisplay = () => {
  if (contextLength.value === -1) {
    isUsingAllMessages.value = true
    contextLengthDisplay.value = currentSessionMessageCount.value || 10
  } else {
    isUsingAllMessages.value = false
    contextLengthDisplay.value = contextLength.value
  }
}

// 消息操作方法
const sendMessage = async () => {
  if (!messageInput.value.trim() || !currentSession.value || isSending.value) return

  const userInput = messageInput.value.trim()
  messageInput.value = ''

  try {
    // 找到当前选中的模型对象
    const selectedModel = availableModels.value.find(model => model.id.toString() === currentModel.value)

    if (!selectedModel) {
      ElMessage.error('请选择一个AI模型')
      messageInput.value = userInput // 恢复输入内容
      return
    }

    // 检查模型是否被禁用
    if (!selectedModel.is_active) {
      // 自动切换到第一个可用模型
      const firstAvailableModel = availableModels.value.find(m => m.is_active)
      if (firstAvailableModel) {
        handleModelChange(firstAvailableModel.id.toString())
        ElMessage.warning(`当前模型已被禁用，已自动切换到 ${firstAvailableModel.name}`)
        // 继续发送消息
      } else {
        ElMessage.error('没有可用的AI模型，请联系管理员')
        messageInput.value = userInput // 恢复输入内容
        return
      }
    }

    // 检查token限制（如果模型有限制）
    if (selectedModel.max_tokens && typeof selectedModel.max_tokens === 'number') {
      const estimatedTokens = Math.ceil(userInput.length / 4) + tokenCount.value
      if (estimatedTokens > selectedModel.max_tokens) {
        ElMessage.warning(`消息过长，预估token数(${estimatedTokens})超过模型限制(${selectedModel.max_tokens})，请缩短消息内容`)
        messageInput.value = userInput // 恢复输入内容
        return
      }
    }

    // 设置发送状态和思考状态
    isSending.value = true
    isTyping.value = true

    // 创建 AbortController 用于停止生成
    abortController.value = new AbortController()

    const chatData = {
      message: userInput,
      model_id: selectedModel.id,  // 使用模型ID（整数）
      knowledge_base_ids: selectedKnowledgeBases.value,
      history_limit: contextLength.value === -1 ? 999999 : contextLength.value,  // -1表示全部消息
      relevance_threshold: relevanceThreshold.value  // 添加相关度阈值
    }

    // 创建用户消息
    const userMessage: ChatMessage = {
      id: Date.now(),
      session_id: currentSession.value.id,
      role: 'user',
      content: userInput,
      created_at: new Date().toISOString()
    }

    // 添加用户消息到当前会话
    if (!messages.value[currentSession.value.id]) {
      messages.value[currentSession.value.id] = []
    }
    messages.value[currentSession.value.id].push(userMessage)

    // 创建AI消息
    const aiMessage: ChatMessage = {
      id: Date.now() + 1,
      session_id: currentSession.value.id,
      role: 'assistant',
      content: '',
      created_at: new Date().toISOString()
    }
    messages.value[currentSession.value.id].push(aiMessage)

    // 标记AI消息为流式输出中
    streamingMessageIds.value.add(aiMessage.id.toString())

    // 调用流式聊天API
    await chatAPI.streamChat(
      currentSession.value.id,
      chatData,
      (content: string) => {
        // 流式更新消息内容
        if (currentSession.value) {
          const aiMsgIndex = messages.value[currentSession.value.id].findIndex(m => m.id === aiMessage.id)
          if (aiMsgIndex !== -1) {
            messages.value[currentSession.value.id][aiMsgIndex].content += content
          }
        }
      },
      (finalMessage: ChatMessage) => {
        // 完成时更新整个消息对象并移除流式状态
        if (currentSession.value) {
          const aiMsgIndex = messages.value[currentSession.value.id].findIndex(m => m.id === aiMessage.id)
          if (aiMsgIndex !== -1) {
            messages.value[currentSession.value.id][aiMsgIndex] = finalMessage
          }
        }

        // 延迟移除流式状态，确保图表解析在流式完成后进行
        setTimeout(() => {
          streamingMessageIds.value.delete(aiMessage.id.toString())
          console.log(`[流式完成] 消息${aiMessage.id}流式输出完成，开始解析图表`)
        }, 100) // 100ms延迟

        // 清理旧的缓存条目
        const messageIdStr = aiMessage.id.toString()
        for (const key of chartCache.value.keys()) {
          if (key.startsWith(messageIdStr + '-')) {
            chartCache.value.delete(key)
          }
        }
        for (const key of htmlCache.value.keys()) {
          if (key.startsWith(messageIdStr + '-')) {
            htmlCache.value.delete(key)
          }
        }

        // 强制触发重新解析图表和HTML
        nextTick(() => {
          console.log('流式响应完成，开始解析图表和HTML，消息ID:', aiMessage.id)
        })

        // 滚动到底部
        nextTick(() => {
          scrollToBottom()
        })
      },
      (error: string) => {
        // 错误处理
        if (currentSession.value) {
          const aiMsgIndex = messages.value[currentSession.value.id].findIndex(m => m.id === aiMessage.id)
          if (aiMsgIndex !== -1) {
            messages.value[currentSession.value.id][aiMsgIndex] = {
              ...aiMessage,
              content: error,
              role: 'assistant',
              isError: true
            }
          }
        }
        streamingMessageIds.value.delete(aiMessage.id.toString())
        ElMessage.error(error || '发送失败')
        // 恢复输入框内容
        messageInput.value = userInput
      },
      abortController.value
    )
  } catch (error) {
    ElMessage.error('发送失败，请重试')
    // 恢复输入框内容
    messageInput.value = userInput
  } finally {
    // 清除发送状态
    isSending.value = false
    isTyping.value = false
    abortController.value = null
  }
}

const handleInputKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 发送按钮点击处理
const handleSendButtonClick = () => {
  if (isTyping.value) {
    stopGeneration()
  } else {
    sendMessage()
  }
}

const stopGeneration = () => {
  if (abortController.value) {
    abortController.value.abort()
    abortController.value = null
  }

  // 清理所有流式状态
  streamingMessageIds.value.clear()

  // 清理所有缓存（因为可能有未完成的流式输出）
  chartCache.value.clear()
  htmlCache.value.clear()

  isTyping.value = false
  isSending.value = false

  ElMessage.info('已停止生成，保留当前内容')
}

const copyMessage = async (content: string) => {
  try {
    copyMessageUtil(content)
    ElMessage.success('已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

const regenerateMessage = async (message: ChatMessage) => {
  if (!currentSession.value || message.role !== 'assistant') return

  try {
    isTyping.value = true

    // 找到当前选中的模型对象
    const selectedModel = availableModels.value.find(model => model.id.toString() === currentModel.value)

    if (!selectedModel) {
      ElMessage.error('请选择一个AI模型')
      return
    }

    // 创建 AbortController 用于停止生成
    abortController.value = new AbortController()

    // 清空当前消息内容，显示加载状态
    const sessionMessages = messages.value[currentSession.value.id]
    const messageIndex = sessionMessages.findIndex(m => m.id === message.id)

    if (messageIndex !== -1) {
      sessionMessages[messageIndex].content = ''
    }

    // 标记消息为流式输出中
    streamingMessageIds.value.add(message.id.toString())

    // 调用流式重新生成API
    await chatAPI.streamRegenerateMessage(
      {
        message_id: message.id,
        model_id: selectedModel.id,  // 使用模型ID（整数）
        knowledge_base_ids: selectedKnowledgeBases.value,
        history_limit: contextLength.value === -1 ? 999999 : contextLength.value,  // -1表示全部消息
        relevance_threshold: relevanceThreshold.value  // 添加相关度阈值
      },
      (content: string) => {
        // 流式更新消息内容
        if (messageIndex !== -1) {
          sessionMessages[messageIndex].content += content
        }
      },
      (finalMessage: ChatMessage) => {
        // 完成时更新整个消息对象并移除流式状态
        if (messageIndex !== -1) {
          sessionMessages[messageIndex] = finalMessage
        }

        // 延迟移除流式状态，确保图表解析在流式完成后进行
        setTimeout(() => {
          streamingMessageIds.value.delete(message.id.toString())
          console.log(`[重新生成完成] 消息${message.id}重新生成完成，开始解析图表`)
        }, 100) // 100ms延迟

        // 清理旧的缓存条目
        const messageIdStr = message.id.toString()
        for (const key of chartCache.value.keys()) {
          if (key.startsWith(messageIdStr + '-')) {
            chartCache.value.delete(key)
          }
        }
        for (const key of htmlCache.value.keys()) {
          if (key.startsWith(messageIdStr + '-')) {
            htmlCache.value.delete(key)
          }
        }

        // 强制触发重新解析图表和HTML
        nextTick(() => {
          console.log('重新生成完成，开始解析图表和HTML，消息ID:', message.id)
        })

        ElMessage.success('消息已重新生成')
      },
      (error: string) => {
        // 移除流式状态
        streamingMessageIds.value.delete(message.id.toString())
        ElMessage.error(error || '重新生成失败')
      },
      abortController.value
    )

    // 滚动到底部
    await nextTick()
    scrollToBottom()
  } catch (error: any) {
    console.error('重新生成消息失败:', error)
    ElMessage.error(error.response?.data?.message || '重新生成失败')
  } finally {
    isTyping.value = false
    abortController.value = null
  }
}

const resendMessage = async (message: ChatMessage) => {
  if (!currentSession.value || message.role !== 'user') return

  try {
    // 重新发送用户消息
    messageInput.value = message.content
    await sendMessage()
  } catch (error: any) {
    console.error('重新发送消息失败:', error)
    ElMessage.error('重新发送失败')
  }
}

const deleteMessage = async (message: ChatMessage) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这条消息吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger'
      }
    )

    await chatAPI.deleteMessage(message.id)

    if (currentSession.value) {
      const sessionMessages = messages.value[currentSession.value.id]
      const messageIndex = sessionMessages.findIndex(m => m.id === message.id)
      if (messageIndex !== -1) {
        sessionMessages.splice(messageIndex, 1)
        ElMessage.success('消息删除成功')
      }
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除消息失败:', error)
      ElMessage.error(error.response?.data?.message || '删除失败')
    }
  }
}

const editMessage = async (message: ChatMessage) => {
  try {
    const { value: newContent } = await ElMessageBox.prompt(
      '编辑消息内容',
      '编辑消息',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputValue: message.content
      }
    )

    if (currentSession.value) {
      const sessionMessages = messages.value[currentSession.value.id]
      const messageIndex = sessionMessages.findIndex(m => m.id === message.id)

      if (messageIndex !== -1) {
        sessionMessages[messageIndex].content = newContent
        ElMessage.success('消息编辑成功')
      }
    }
  } catch (error) {
    // 用户取消
  }
}

const sendSuggestedQuestion = async (question: string) => {
  messageInput.value = question
  await sendMessage()
}

// 其他操作方法
const handleModelChange = (modelId: string) => {
  // 更新当前模型
  currentModel.value = modelId
  // 保存选择的模型到localStorage
  localStorage.setItem('selectedModelId', modelId)

  // 获取模型信息并显示提示
  const selectedModel = availableModels.value.find(model => model.id.toString() === modelId)
  if (selectedModel) {
    const tokenInfo = selectedModel.max_tokens ? `${selectedModel.max_tokens} tokens` : '无限制'
    const statusInfo = selectedModel.is_active ? '可用' : '已禁用'
    ElMessage.success(`已切换到 ${selectedModel.name}（${tokenInfo}，${statusInfo}）`)
    console.log('模型已切换到:', selectedModel.name, '- Token限制:', tokenInfo)
  }
}

const exportChat = () => {
  if (currentSession.value) {
    exportSession(currentSession.value)
  }
}

const clearContext = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空当前对话的上下文吗？这将删除所有消息记录。',
      '清空上下文',
      {
        confirmButtonText: '清空',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (currentSession.value) {
      try {
        await chatAPI.clearSession(currentSession.value.id)
        messages.value[currentSession.value.id] = []
        ElMessage.success('上下文已清空')
      } catch (error: any) {
        console.error('清空聊天记录失败:', error)
        ElMessage.error(error.response?.data?.message || '清空失败，请重试')
      }
    }
  } catch (error) {
    // 用户取消
  }
}

const toggleKnowledgeBase = (kbId: number) => {
  const index = selectedKnowledgeBases.value.indexOf(kbId)
  if (index !== -1) {
    selectedKnowledgeBases.value.splice(index, 1)
  } else {
    selectedKnowledgeBases.value.push(kbId)
  }

  // 持久化知识库选择到localStorage
  localStorage.setItem('selectedKnowledgeBases', JSON.stringify(selectedKnowledgeBases.value))
}

const attachFile = () => {
  ElMessage.info('文件附加功能开发中...')
}

// 判断是否是最后一条AI消息
const isLastAssistantMessage = (message: ChatMessage) => {
  if (!currentSession.value) return false
  const sessionMessages = messages.value[currentSession.value.id] || []
  const assistantMessages = sessionMessages.filter(m => m.role === 'assistant')
  return assistantMessages.length > 0 && assistantMessages[assistantMessages.length - 1].id === message.id
}

// 从消息中提取图表数据
// 缓存图表和HTML解析结果
const chartCache = ref(new Map<string, any[]>())
const htmlCache = ref(new Map<string, any[]>())

const getChartsFromMessage = (messageId: number, content: string, isStreaming = false): any[] => {
  const cacheKey = `${messageId}-${content.length}`

  console.log(`[图表解析] 消息ID: ${messageId}, 内容长度: ${content.length}, 流式状态: ${isStreaming}`)

  // 如果正在流式输出，返回空数组
  if (isStreaming) {
    console.log(`[图表解析] 消息${messageId}正在流式输出，跳过解析`)
    return []
  }

  // 检查缓存
  if (chartCache.value.has(cacheKey)) {
    const cached = chartCache.value.get(cacheKey) || []
    console.log(`[图表解析] 使用缓存结果，图表数量: ${cached.length}`)
    return cached
  }

  console.log(`[图表解析] 开始解析消息${messageId}的图表内容`)
  const chartBlocks = extractChartBlocks(content)

  const charts = []

  // 调试日志
  if (chartBlocks.length > 0) {
    
  }

  for (const block of chartBlocks) {
    const chartData = parseChartCode(block)
    if (chartData) {
      charts.push(chartData)
    }
  }

  // 缓存结果
  chartCache.value.set(cacheKey, charts)
  console.log(`[图表解析] 消息${messageId}解析完成，成功解析${charts.length}个图表，已缓存`)
  return charts
}

// 从消息中提取HTML内容
const getHtmlFromMessage = (messageId: number, content: string, isStreaming = false): any[] => {
  const cacheKey = `${messageId}-${content.length}`

  // 如果正在流式输出，返回空数组
  if (isStreaming) {
    return []
  }

  // 检查缓存
  if (htmlCache.value.has(cacheKey)) {
    return htmlCache.value.get(cacheKey) || []
  }

  const htmlBlocks = extractHtmlBlocks(content)

  // 调试日志
  if (htmlBlocks.length > 0) {
    console.log('发现HTML代码块:', htmlBlocks)
  }

  // 缓存结果
  htmlCache.value.set(cacheKey, htmlBlocks)
  return htmlBlocks
}

// 智能决定显示策略：如果HTML包含图表，优先显示HTML；否则分别显示
const getDisplayStrategy = (messageId: number, content: string, isStreaming = false) => {
  const htmlBlocks = getHtmlFromMessage(messageId, content, isStreaming)
  const chartBlocks = getChartsFromMessage(messageId, content, isStreaming)

  // 如果有HTML且HTML包含图表代码，优先显示HTML
  if (htmlBlocks.length > 0 && shouldPrioritizeHtml(content)) {
    return {
      showHtml: true,
      showCharts: false,
      htmlBlocks,
      chartBlocks: []
    }
  }

  // 如果有HTML但不包含图表，两者都显示
  if (htmlBlocks.length > 0 && chartBlocks.length > 0) {
    return {
      showHtml: true,
      showCharts: true,
      htmlBlocks,
      chartBlocks
    }
  }

  // 其他情况按原逻辑显示
  return {
    showHtml: htmlBlocks.length > 0,
    showCharts: chartBlocks.length > 0,
    htmlBlocks,
    chartBlocks
  }
}

// 切换专注模式
const toggleFocusMode = () => {
  uiStore.toggleFocusMode()
}



const scrollToBottom = () => {
  if (messagesContainer.value) {
    nextTick(() => {
      const container = messagesContainer.value!
      container.scrollTop = container.scrollHeight

      // 强制滚动，确保在所有情况下都能滚动到底部
      setTimeout(() => {
        container.scrollTop = container.scrollHeight
      }, 50)
    })
  }
}

// 监听路由参数
watch(() => route.query, (newQuery) => {
  if (newQuery.session) {
    const sessionId = Number(newQuery.session)
    const session = sessions.value.find(s => s.id === sessionId)
    if (session) {
      selectSession(session)
    }
  }

  if (newQuery.kb) {
    const kbId = Number(newQuery.kb)
    if (!selectedKnowledgeBases.value.includes(kbId)) {
      selectedKnowledgeBases.value.push(kbId)
    }
  }
}, { immediate: true })

// 监听当前会话的消息变化，自动滚动到底部
watch(() => currentSession.value ? messages.value[currentSession.value.id] : [], async () => {
  if (currentSession.value) {
    await nextTick()
    scrollToBottom()
  }
}, { deep: true })

// 监听路由变化，确保每次进入聊天页面都滚动到底部
watch(() => route.path, async (newPath) => {
  if (newPath === '/user/chat' && currentSession.value) {
    await nextTick()
    setTimeout(() => {
      scrollToBottom()
    }, 200) // 延迟确保页面完全加载
  }
})

// 监听上下文长度变化，保存到localStorage
watch(contextLength, (newValue) => {
  localStorage.setItem('contextLength', newValue.toString())
})

// 监听相关度阈值变化，保存到localStorage
watch(relevanceThreshold, (newValue) => {
  localStorage.setItem('relevanceThreshold', newValue.toString())
})

// 数据加载函数
const loadChatData = async () => {
  try {
    // 初始化AI模型数据（包括用户设置）
    await initializeModelsData()

    // 加载聊天会话
    await chatStore.fetchSessions()

    // 加载知识库
    await knowledgeBaseStore.fetchKnowledgeBases()

    // 等待下一个tick，确保计算属性已更新
    await nextTick()

    // 设置默认模型 - 优先级：localStorage > 用户设置 > 第一个可用模型
    let modelToUse = null

    console.log('可用模型列表:', availableModels.value.map(m => ({ id: m.id, name: m.name, display_name: m.display_name })))
    console.log('用户设置:', settingsStore.userSettings)

    // 1. 优先使用localStorage中保存的模型（用户最后选择的模型）
    const savedModelId = localStorage.getItem('selectedModelId')
    if (savedModelId) {
      modelToUse = availableModels.value.find(m => m.id.toString() === savedModelId)
      if (modelToUse) {
        console.log('使用localStorage中保存的模型:', modelToUse.name)
      }
    }

    // 2. 如果localStorage中没有或模型不存在，使用用户设置中的默认模型
    if (!modelToUse && settingsStore.userSettings.defaultModel) {
      modelToUse = availableModels.value.find(m => m.id === settingsStore.userSettings.defaultModel)
      if (modelToUse) {
        console.log('使用用户设置的默认模型:', modelToUse.name)
      }
    }

    // 3. 如果都没有，使用第一个可用模型
    if (!modelToUse && availableModels.value.length > 0) {
      modelToUse = availableModels.value[0]
      console.log('使用第一个可用模型:', modelToUse.name)
    }

    // 设置当前模型
    if (modelToUse) {
      currentModel.value = modelToUse.id.toString()
      localStorage.setItem('selectedModelId', currentModel.value)
      console.log('设置当前模型:', modelToUse.name, '(ID:', currentModel.value, ')')

      // 同时设置新建对话表单的默认模型
      newChatForm.value.model = parseInt(currentModel.value)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
  }
}

onMounted(async () => {
  // 立即显示页面，后台加载数据
  loadChatData().then(() => {
    // 初始化上下文显示
    initializeContextDisplay()

    // 恢复之前选择的会话，如果没有则选择第一个
    if (sessions.value.length > 0 && !currentSession.value) {
      const savedSessionId = localStorage.getItem('currentSessionId')
      let targetSession = null

      if (savedSessionId) {
        // 尝试找到保存的会话
        targetSession = sessions.value.find(s => s.id.toString() === savedSessionId)
      }

      // 如果没有找到保存的会话，使用第一个会话
      if (!targetSession) {
        targetSession = sessions.value[0]
      }

      if (targetSession) {
        chatStore.setCurrentSession(targetSession.id)
        // 更新localStorage为实际选择的会话
        localStorage.setItem('currentSessionId', targetSession.id.toString())
      }
    }

    // 无论是否有当前会话，都滚动到底部
    nextTick().then(() => {
      setTimeout(() => {
        scrollToBottom()
      }, 100) // 延迟一点确保DOM完全渲染
    })
  }).catch(error => {
    console.error('加载聊天数据失败:', error)
  })
})
</script>

<style scoped>
/* 搜索框样式 */
.search-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.search-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.1);
}

.search-input :deep(.el-input__wrapper.is-focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 模型选择器样式 */
.model-select :deep(.el-input__wrapper) {
  border-radius: 10px;
  border: 1px solid rgba(147, 51, 234, 0.2);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
}

/* 科技风按钮 */
.tech-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 10px;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tech-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.tech-button-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
  border-radius: 10px;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(245, 87, 108, 0.3);
}

.tech-button-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 87, 108, 0.4);
}

/* 消息输入框样式 */
.message-input :deep(.el-textarea__inner) {
  border-radius: 12px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 12px;
  font-size: 13px;
  line-height: 1.4;
  transition: all 0.3s ease;
  resize: none;
  max-height: 90px !important;
  overflow-y: auto;
}

.message-input :deep(.el-textarea__inner:focus) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

/* 发送按钮样式 */
.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 10px;
  width: 40px;
  height: 40px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
  flex-shrink: 0;
  margin-top: 0;
}

.send-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 8px 30px rgba(102, 126, 234, 0.6);
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.send-button:active {
  transform: translateY(0) scale(1);
}

/* AI消息内容自适应宽度 */
.ai-message-content {
  min-width: 200px;
  max-width: 100%;
  width: fit-content;
}

/* 上下文设置滑块样式 */
.context-slider :deep(.el-slider__runway) {
  background-color: #e5e7eb;
  height: 4px;
}

.context-slider :deep(.el-slider__bar) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 4px;
}

.context-slider :deep(.el-slider__button) {
  border: 2px solid #667eea;
  background-color: white;
  width: 16px;
  height: 16px;
}

.context-slider :deep(.el-slider__button:hover) {
  border-color: #764ba2;
}

/* Markdown内容样式 */
.markdown-content {
  line-height: 1.6;
  color: #374151;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 12px;
}

.dark .markdown-content {
  color: #e5e7eb;
}

/* 改善段落间距 */
.markdown-content p {
  margin-bottom: 1rem;
}

.markdown-content h1, .markdown-content h2, .markdown-content h3, .markdown-content h4, .markdown-content h5, .markdown-content h6 {
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
}

.markdown-content ul, .markdown-content ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.markdown-content li {
  margin-bottom: 0.25rem;
}

/* 确保代码块不会溢出 */
.markdown-content pre {
  max-width: 100%;
  overflow-x: auto;
}

.markdown-content .code-block-container {
  max-width: 100%;
  overflow: hidden;
}

.markdown-content table {
  max-width: 100%;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
  margin: 1.5em 0 0.5em 0;
}

.markdown-content p {
  margin: 1em 0;
}

.markdown-content ul,
.markdown-content ol {
  margin: 1em 0;
  padding-left: 1.5em;
}

.markdown-content li {
  margin: 0.5em 0;
}

.markdown-content blockquote {
  border-left: 4px solid #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  padding: 1em 1.5em;
  margin: 1.5em 0;
  border-radius: 0 12px 12px 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .w-80 {
    width: 100%;
    position: absolute;
    z-index: 50;
    height: 100%;
  }
}

/* 确保容器不会超出屏幕宽度 */
.chat-container {
  max-width: 100vw;
  overflow-x: hidden;
}

/* 消息容器宽度限制 */
.message-container {
  max-width: calc(100vw - 240px); /* 减去侧边栏宽度 */
  overflow: visible; /* 允许HTML预览突破容器 */
}

@media (max-width: 768px) {
  .message-container {
    max-width: 100vw;
    overflow: visible;
  }
}

/* AI消息内容容器 */
.ai-message-content {
  position: relative;
  z-index: 1;
}

/* 确保HTML预览可以突破消息容器 */
.ai-message-content .html-preview-container.full-width {
  z-index: 10;
}

/* 对话框美化 */
.custom-dialog .el-dialog {
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.custom-dialog .el-dialog__header {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-radius: 16px 16px 0 0;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.custom-dialog .el-dialog__title {
  font-weight: 600;
  font-size: 18px;
  background: linear-gradient(135deg, #3b82f6, #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.custom-dialog .el-dialog__body {
  padding: 24px;
}

.custom-dialog .el-form-item__label {
  font-weight: 500;
  color: #374151;
}

.custom-dialog .el-input__wrapper {
  border-radius: 12px;
  border: 1px solid rgba(209, 213, 219, 0.5);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.custom-dialog .el-input__wrapper:hover {
  border-color: rgba(59, 130, 246, 0.5);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.custom-dialog .el-select .el-input__wrapper {
  border-radius: 12px;
}

/* 建议问题按钮样式 */
.suggestion-button {
  border-radius: 20px;
  font-size: 12px;
  padding: 6px 12px;
  transition: all 0.3s ease;
  border: 1px solid rgba(59, 130, 246, 0.3);
  background: rgba(59, 130, 246, 0.05);
}

.suggestion-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
  background: rgba(59, 130, 246, 0.1);
}
</style>
