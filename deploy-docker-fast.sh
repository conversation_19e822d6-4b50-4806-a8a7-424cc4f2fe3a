#!/bin/bash

# Docker快速部署脚本 - 使用国内镜像源优化
# 适用于网络环境较差的情况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker环境
check_docker() {
    log_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 配置Docker镜像源
configure_mirrors() {
    log_info "检查Docker镜像源配置..."
    
    # 检查是否已配置镜像源
    if docker info 2>/dev/null | grep -q "Registry Mirrors"; then
        log_success "Docker镜像源已配置"
    else
        log_warn "Docker镜像源未配置，建议先运行: ./configure-docker-mirrors.sh"
        read -p "是否继续部署？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 清理旧容器和镜像
cleanup_old() {
    log_info "清理旧容器和镜像..."
    
    # 停止并删除旧容器
    docker-compose -f docker-compose.prod.yml down --remove-orphans 2>/dev/null || true
    
    # 清理悬空镜像
    docker image prune -f
    
    log_success "清理完成"
}

# 预拉取基础镜像
pull_base_images() {
    log_info "预拉取基础镜像..."
    
    local images=(
        "python:3.11-slim"
        "node:20-alpine"
        "nginx:alpine"
        "postgres:15-alpine"
        "redis:7-alpine"
    )
    
    for image in "${images[@]}"; do
        log_info "拉取镜像: $image"
        if ! docker pull "$image"; then
            log_warn "拉取 $image 失败，将在构建时重试"
        fi
    done
    
    log_success "基础镜像拉取完成"
}

# 构建镜像（分步骤）
build_images() {
    log_info "开始构建Docker镜像..."
    
    # 设置构建环境变量
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    # 分别构建各个服务，避免并行构建导致的资源竞争
    log_info "构建后端镜像..."
    if ! docker-compose -f docker-compose.prod.yml build backend; then
        log_error "后端镜像构建失败"
        exit 1
    fi
    
    log_info "构建前端镜像..."
    if ! docker-compose -f docker-compose.prod.yml build frontend; then
        log_error "前端镜像构建失败"
        exit 1
    fi
    
    log_success "所有镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动Docker服务..."
    
    # 分步启动服务，确保依赖关系
    log_info "启动数据库服务..."
    docker-compose -f docker-compose.prod.yml up -d postgres redis
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 10
    
    # 检查数据库连接
    local retries=30
    while [ $retries -gt 0 ]; do
        if docker-compose -f docker-compose.prod.yml exec -T postgres pg_isready -U postgres; then
            log_success "数据库连接成功"
            break
        fi
        log_info "等待数据库连接... ($retries)"
        sleep 2
        ((retries--))
    done
    
    if [ $retries -eq 0 ]; then
        log_error "数据库连接超时"
        exit 1
    fi
    
    # 启动后端服务
    log_info "启动后端服务..."
    docker-compose -f docker-compose.prod.yml up -d backend
    
    # 等待后端启动
    sleep 5
    
    # 启动前端服务
    log_info "启动前端服务..."
    docker-compose -f docker-compose.prod.yml up -d frontend
    
    log_success "所有服务启动完成"
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 显示容器状态
    docker-compose -f docker-compose.prod.yml ps
    
    # 检查服务健康状态
    local services=("postgres" "redis" "backend" "frontend")
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.prod.yml ps "$service" | grep -q "Up"; then
            log_success "$service 服务运行正常"
        else
            log_error "$service 服务异常"
        fi
    done
    
    # 测试前端访问
    sleep 5
    if curl -s http://localhost:8080 > /dev/null; then
        log_success "前端服务可访问: http://localhost:8080"
    else
        log_warn "前端服务暂时无法访问，请稍后重试"
    fi
}

# 显示日志
show_logs() {
    log_info "显示服务日志..."
    echo "按 Ctrl+C 退出日志查看"
    sleep 2
    docker-compose -f docker-compose.prod.yml logs -f
}

# 停止服务
stop_services() {
    log_info "停止Docker服务..."
    docker-compose -f docker-compose.prod.yml down
    log_success "服务已停止"
}

# 显示帮助信息
show_help() {
    cat << EOF
Docker快速部署脚本

用法: $0 [选项]

选项:
  start     启动所有服务（默认）
  stop      停止所有服务
  restart   重启所有服务
  build     仅构建镜像
  logs      查看服务日志
  status    查看服务状态
  clean     清理容器和镜像
  help      显示此帮助信息

示例:
  $0 start    # 启动服务
  $0 logs     # 查看日志
  $0 restart  # 重启服务

EOF
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            log_info "开始Docker快速部署..."
            check_docker
            configure_mirrors
            cleanup_old
            pull_base_images
            build_images
            start_services
            check_services
            log_success "部署完成！访问地址: http://localhost:8080"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            sleep 2
            main start
            ;;
        "build")
            check_docker
            configure_mirrors
            pull_base_images
            build_images
            ;;
        "logs")
            show_logs
            ;;
        "status")
            check_services
            ;;
        "clean")
            cleanup_old
            docker system prune -f
            log_success "清理完成"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
