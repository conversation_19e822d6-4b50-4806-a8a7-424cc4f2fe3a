"""
应用配置模块
"""
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """应用设置"""
    
    # 应用基础配置
    app_name: str = "AI Knowledge Base"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 数据库配置
    database_url: str = "postgresql+asyncpg://postgres:111222@localhost:5432/aiknowledgebase"
    database_host: str = "localhost"
    database_port: int = 5432
    database_user: str = "postgres"
    database_password: str = "111222"
    database_name: str = "aiknowledgebase"
    
    # JWT配置
    secret_key: str = "abcXyz123_4x9KpQvE8jHmN2qRtSvWnZr5t7w-"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 480  # 8小时
    
    # Redis配置
    redis_host: str = "redis"  
    redis_port: int = 6379
    redis_db: int = 0

    # 文件上传配置
    upload_dir: str = "./uploads"
    max_file_size: int = 50  # MB

    # AI模型配置
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    siliconflow_api_key: str = "sk-ltvyukqqyhwhedkxyqhsqnrtqehlfdzpflskkfkqwetoiixm"
    deepseek_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 全局设置实例
settings = Settings()
