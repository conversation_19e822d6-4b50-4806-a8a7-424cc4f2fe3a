#!/bin/bash

# AI知识库项目Docker部署脚本

echo "🚀 开始部署AI知识库项目..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "⚠️  未找到.env文件，正在复制.env.example..."
    cp .env.example .env
    echo "📝 .env文件已创建，使用项目实际配置"
fi

# 检查部署模式
read -p "选择部署模式 (1: 开发环境, 2: 生产环境): " -n 1 -r
echo
if [[ $REPLY == "2" ]]; then
    DEPLOY_MODE="production"
    echo "🚀 生产环境部署模式"

    # 设置SSL证书
    if [ -f "setup-ssl.sh" ]; then
        chmod +x setup-ssl.sh
        ./setup-ssl.sh
    else
        echo "⚠️  未找到SSL设置脚本，请手动设置SSL证书"
    fi
else
    DEPLOY_MODE="development"
    echo "🔧 开发环境部署模式"
fi

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down

# 清理旧镜像（可选）
read -p "是否清理旧的Docker镜像？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 清理旧镜像..."
    docker system prune -f
fi

# 构建并启动服务
echo "🔨 构建并启动服务..."
if [[ $DEPLOY_MODE == "production" ]]; then
    echo "🌐 启动生产环境服务..."
    docker-compose -f docker-compose.yml -f docker-compose.prod.yml up --build -d
else
    echo "🔧 启动开发环境服务..."
    docker-compose up --build -d
fi

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 检查服务状态
echo "📊 检查服务状态..."
docker-compose ps

# 显示日志
echo "📋 显示服务日志..."
docker-compose logs --tail=50

echo "✅ 部署完成！"
if [[ $DEPLOY_MODE == "production" ]]; then
    echo "🌐 生产环境访问地址:"
    echo "  - HTTPS: https://aiknowledgebase.csicollege.cn"
    echo "  - HTTP: http://aiknowledgebase.csicollege.cn (自动重定向到HTTPS)"
    echo "🔧 后端API地址: https://aiknowledgebase.csicollege.cn/api"
    echo "📚 API文档地址: https://aiknowledgebase.csicollege.cn/api/docs"
else
    echo "🌐 开发环境访问地址:"
    echo "  - 前端: http://localhost"
    echo "🔧 后端API地址: http://localhost:8000"
    echo "📚 API文档地址: http://localhost:8000/docs"
fi

echo ""
echo "📝 常用命令："
echo "查看日志: docker-compose logs -f [service_name]"
echo "重启服务: docker-compose restart [service_name]"
echo "停止服务: docker-compose down"
echo "更新服务: docker-compose up --build -d"
