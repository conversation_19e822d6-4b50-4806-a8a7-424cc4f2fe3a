"""
AI相关模型
"""
from typing import Optional
from sqlmodel import SQLModel, Field, Relationship
from .base import BaseModel


class AIProvider(BaseModel, table=True):
    """AI供应商表"""
    __tablename__ = "ai_providers"

    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(max_length=50, unique=True)  # OpenAI, Anthropic, SiliconFlow等
    display_name: str = Field(max_length=100)  # 显示名称，如"硅基流动"
    base_url: Optional[str] = Field(default=None, max_length=255)  # API基础URL
    is_active: bool = Field(default=True)
    description: Optional[str] = Field(default=None, max_length=500)  # 供应商描述

    # 关联的模型
    models: list["AIModel"] = Relationship(back_populates="provider")


class AIModel(BaseModel, table=True):
    """AI模型表"""
    __tablename__ = "ai_models"

    id: Optional[int] = Field(default=None, primary_key=True)
    provider_id: int = Field(foreign_key="ai_providers.id")
    model_name: str = Field(max_length=100)  # gpt-4, claude-3-opus等
    display_name: str = Field(max_length=100)  # 显示名称
    is_active: bool = Field(default=True)
    system_api_key: Optional[str] = Field(default=None, max_length=255)  # 加密存储
    allow_system_key_use: bool = Field(default=False)
    max_tokens: Optional[int] = Field(default=4000)  # 最大token数
    supports_streaming: bool = Field(default=True)  # 是否支持流式输出
    cost_per_1k_tokens: Optional[float] = Field(default=None)  # 每1k token成本

    # 关联的供应商
    provider: AIProvider = Relationship(back_populates="models")


class UserAPIKey(BaseModel, table=True):
    """用户个人密钥表"""
    __tablename__ = "user_api_keys"

    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    provider_id: int = Field(foreign_key="ai_providers.id")  # 改为关联供应商
    api_key: str = Field(max_length=255)  # 加密存储
    description: Optional[str] = Field(default=None, max_length=200)  # 密钥描述

    # 关联的供应商
    provider: Optional[AIProvider] = Relationship()


class ChatSession(BaseModel, table=True):
    """聊天会话表"""
    __tablename__ = "chat_sessions"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: int = Field(foreign_key="users.id")
    title: Optional[str] = Field(default=None, max_length=255)


class ChatMessage(BaseModel, table=True):
    """聊天消息表"""
    __tablename__ = "chat_messages"

    id: Optional[int] = Field(default=None, primary_key=True)
    session_id: int = Field(foreign_key="chat_sessions.id")
    role: str = Field(max_length=20)  # user 或 assistant
    content: str = Field()
    model_id_used: Optional[int] = Field(default=None, foreign_key="ai_models.id")  # 改为关联模型表
    referenced_kbs: Optional[str] = Field(default=None)  # JSON字符串存储引用的知识库ID列表
