"""
文档管理API
"""
import time
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from pydantic import BaseModel
from sqlmodel import Session, select
from app.core.database import SessionDep
from app.core.auth import get_current_active_user
from app.models.user import User, UserQuota
from app.models.knowledge_base import KnowledgeBase, Document
from app.schemas.knowledge_base import DocumentResponse
# from app.utils.file_handler import save_upload_file, get_file_type, delete_file  # 不再需要
from app.config import settings
from app.services.document_service import get_document_service
from app.services.vector_service import get_vector_service
from app.services.cache_service import get_cache_service
import uuid
import os

router = APIRouter()


class BatchDeleteRequest(BaseModel):
    """批量删除请求"""
    document_ids: List[int]


@router.get("/", response_model=List[DocumentResponse])
async def get_documents(
    kb_id: int = Query(None, description="知识库ID"),
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """获取文档列表"""
    if kb_id:
        # 验证知识库是否属于当前用户
        kb_statement = select(KnowledgeBase).where(
            KnowledgeBase.id == kb_id,
            KnowledgeBase.owner_id == current_user.id
        )
        kb = session.exec(kb_statement).first()
        if not kb:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="知识库不存在"
            )

        # 获取指定知识库的文档
        statement = select(Document).where(Document.kb_id == kb_id)
    else:
        # 获取用户所有文档
        statement = select(Document).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id
        )

    documents = session.exec(statement).all()
    return documents


@router.post("/upload/{kb_id}", response_model=DocumentResponse)
async def upload_document(
    kb_id: int,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """上传文档到知识库"""
    # 验证知识库是否存在且属于当前用户
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )

    # 获取用户配额并检查限制
    quota_statement = select(UserQuota).where(UserQuota.user_id == current_user.id)
    user_quota = session.exec(quota_statement).first()

    # 如果没有配额记录，创建默认配额
    if not user_quota:
        user_quota = UserQuota(
            user_id=current_user.id,
            max_kbs=5,
            max_docs_per_kb=100,
            max_storage_mb=1024
        )
        session.add(user_quota)
        session.commit()
        session.refresh(user_quota)

    # 检查该知识库的文档数量
    doc_count_statement = select(Document).where(Document.kb_id == kb_id)
    existing_docs = session.exec(doc_count_statement).all()

    if len(existing_docs) >= user_quota.max_docs_per_kb:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"该知识库已达到文档数量限制，最多可上传 {user_quota.max_docs_per_kb} 个文档"
        )

    # 检查文件大小
    if file.size and file.size > settings.max_file_size * 1024 * 1024:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"文件大小超过限制 ({settings.max_file_size}MB)"
        )

    # 检查用户存储配额
    if file.size:
        file_size_mb = file.size / (1024 * 1024)

        # 计算用户当前存储使用量
        user_docs_statement = select(Document).join(KnowledgeBase).where(
            KnowledgeBase.owner_id == current_user.id
        )
        user_docs = session.exec(user_docs_statement).all()
        current_storage_mb = sum(doc.file_size or 0 for doc in user_docs) / (1024 * 1024)

        if current_storage_mb + file_size_mb > user_quota.max_storage_mb:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"存储空间不足。当前使用: {current_storage_mb:.1f}MB，配额: {user_quota.max_storage_mb}MB"
            )
    
    # 检查文件类型
    document_service = get_document_service()
    if not document_service.is_supported_file(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="不支持的文件类型"
        )
    
    try:
        document_service = get_document_service()
        vector_service = get_vector_service()

        # 检查文件类型是否支持
        if not document_service.is_supported_file(file.filename):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件类型"
            )

        # 生成唯一文件名
        file_extension = os.path.splitext(file.filename)[1]
        unique_filename = f"{uuid.uuid4()}{file_extension}"

        # 保存文件
        file_content = await file.read()
        file_path = await document_service.save_uploaded_file(file_content, unique_filename)

        # 创建文档记录
        new_document = Document(
            kb_id=kb_id,
            uploader_id=current_user.id,
            filename=file.filename,
            storage_path=file_path,
            file_type=document_service.get_file_type(file.filename),
            file_size=file.size or 0,
            status="pending"
        )

        session.add(new_document)
        session.commit()
        session.refresh(new_document)

        # 异步处理文档
        try:
            # 处理文档并生成分块
            chunks = await document_service.process_document(file_path, file.filename)

            # 添加到向量索引
            success = await vector_service.add_document_chunks(
                kb_id=kb_id,
                document_id=new_document.id,
                chunks=chunks
            )

            if success:
                new_document.status = "completed"
            else:
                new_document.status = "failed"

        except Exception as process_error:
            new_document.status = "failed"
            print(f"文档处理失败: {process_error}")

        session.add(new_document)
        session.commit()

        return new_document

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )


@router.get("/kb/{kb_id}", response_model=List[DocumentResponse])
async def get_documents(
    kb_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep,
    offset: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100)
):
    """获取知识库的文档列表"""
    # 验证知识库权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )
    
    # 获取文档列表
    statement = (
        select(Document)
        .where(Document.kb_id == kb_id)
        .offset(offset)
        .limit(limit)
    )
    documents = session.exec(statement).all()
    
    return documents


@router.get("/{doc_id}", response_model=DocumentResponse)
async def get_document(
    doc_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """获取文档详情"""
    statement = select(Document).where(Document.id == doc_id)
    document = session.exec(statement).first()
    
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )
    
    # 验证权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == document.kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()
    
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权访问此文档"
        )
    
    return document


@router.delete("/batch-delete")
async def batch_delete_documents(
    request: BatchDeleteRequest,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """批量删除文档"""
    document_service = get_document_service()
    vector_service = get_vector_service()

    deleted_count = 0
    failed_deletes = []

    for doc_id in request.document_ids:
        try:
            statement = select(Document).where(Document.id == doc_id)
            document = session.exec(statement).first()

            if not document:
                failed_deletes.append({
                    "document_id": doc_id,
                    "error": "文档不存在"
                })
                continue

            # 验证权限
            kb_statement = select(KnowledgeBase).where(
                KnowledgeBase.id == document.kb_id,
                KnowledgeBase.owner_id == current_user.id
            )
            kb = session.exec(kb_statement).first()

            if not kb:
                failed_deletes.append({
                    "document_id": doc_id,
                    "error": "无权删除此文档"
                })
                continue

            # 从向量索引中删除
            await vector_service.remove_document_chunks(document.kb_id, doc_id)

            # 删除文件
            await document_service.delete_file(document.storage_path)

            # 删除数据库记录
            session.delete(document)
            session.commit()

            deleted_count += 1

        except Exception as e:
            failed_deletes.append({
                "document_id": doc_id,
                "error": str(e)
            })

    return {
        "message": f"批量删除完成，成功: {deleted_count}, 失败: {len(failed_deletes)}",
        "deleted_count": deleted_count,
        "failed_deletes": failed_deletes
    }


@router.delete("/{doc_id}")
async def delete_document(
    doc_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """删除文档"""
    statement = select(Document).where(Document.id == doc_id)
    document = session.exec(statement).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )

    # 验证权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == document.kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()

    if not kb:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权删除此文档"
        )

    # 删除文件
    document_service = get_document_service()
    await document_service.delete_file(document.storage_path)

    # 删除数据库记录
    session.delete(document)
    session.commit()

    return {"message": "文档删除成功"}


@router.post("/{doc_id}/vectorize")
async def vectorize_document(
    doc_id: int,
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """对文档进行向量化处理"""
    document_service = get_document_service()
    vector_service = get_vector_service()
    cache_service = get_cache_service()

    statement = select(Document).where(Document.id == doc_id)
    document = session.exec(statement).first()

    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )

    # 验证权限
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == document.kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()

    if not kb:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权处理此文档"
        )

    # 检查是否已经处理过
    if document.status == "completed":
        return {
            "message": "文档已经处理完成",
            "document_id": doc_id,
            "status": "completed"
        }

    # 更新文档状态为处理中
    document.status = "processing"
    session.add(document)
    session.commit()

    try:
        # 检查缓存中是否有分块数据
        cached_chunks = await cache_service.get_document_chunks(doc_id)

        if cached_chunks:
            chunks = cached_chunks
        else:
            # 处理文档并生成分块
            chunks = await document_service.process_document(
                document.storage_path,
                document.filename
            )

            # 缓存分块数据
            await cache_service.cache_document_chunks(doc_id, chunks)

        # 添加到向量索引
        success = await vector_service.add_document_chunks(
            kb_id=document.kb_id,
            document_id=doc_id,
            chunks=chunks
        )

        if success:
            document.status = "completed"
            session.add(document)
            session.commit()

            return {
                "message": "文档向量化处理完成",
                "document_id": doc_id,
                "status": "completed",
                "chunks_count": len(chunks)
            }
        else:
            raise Exception("向量化处理失败")

    except Exception as e:
        # 处理失败，更新状态
        document.status = "failed"
        session.add(document)
        session.commit()

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文档处理失败: {str(e)}"
        )


@router.post("/batch-upload/{kb_id}")
async def batch_upload_documents(
    kb_id: int,
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_active_user),
    session: SessionDep = SessionDep
):
    """批量上传文档到知识库"""
    # 验证知识库是否存在且属于当前用户
    kb_statement = select(KnowledgeBase).where(
        KnowledgeBase.id == kb_id,
        KnowledgeBase.owner_id == current_user.id
    )
    kb = session.exec(kb_statement).first()

    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )

    document_service = get_document_service()
    vector_service = get_vector_service()

    uploaded_documents = []
    failed_uploads = []

    for file in files:
        # 为每个文件创建独立的数据库会话，避免事务回滚影响其他文件
        from app.core.database import get_session
        file_session = next(get_session())

        try:
            # 检查文件类型
            if not document_service.is_supported_file(file.filename):
                failed_uploads.append({
                    "filename": file.filename,
                    "error": "不支持的文件类型"
                })
                continue

            # 检查文件名长度（数据库限制）
            if len(file.filename) > 255:
                failed_uploads.append({
                    "filename": file.filename,
                    "error": "文件名过长，请重命名后重试"
                })
                continue

            # 读取文件内容（只读取一次）
            file_content = await file.read()

            # 检查文件大小
            actual_size = len(file_content)
            if actual_size > settings.max_file_size * 1024 * 1024:
                failed_uploads.append({
                    "filename": file.filename,
                    "error": f"文件大小超过限制 ({settings.max_file_size}MB)"
                })
                continue

            # 检查是否存在同名文件
            existing_doc_statement = select(Document).where(
                Document.kb_id == kb_id,
                Document.filename == file.filename
            )
            existing_doc = file_session.exec(existing_doc_statement).first()

            # 生成唯一文件名
            file_extension = os.path.splitext(file.filename)[1]
            unique_filename = f"{int(time.time())}_{uuid.uuid4().hex[:8]}{file_extension}"

            # 保存文件
            file_path = await document_service.save_uploaded_file(file_content, unique_filename)

            # 如果存在同名文件，删除旧文件和相关数据
            if existing_doc:
                try:
                    # 删除旧文件
                    await document_service.delete_file(existing_doc.storage_path)

                    # 删除向量索引中的相关数据
                    await vector_service.remove_document_chunks(kb_id, existing_doc.id)

                    # 更新现有文档记录
                    existing_doc.storage_path = file_path
                    existing_doc.file_type = document_service.get_file_type(file.filename)
                    existing_doc.file_size = actual_size
                    existing_doc.status = "pending"
                    existing_doc.error_message = None

                    file_session.add(existing_doc)
                    file_session.commit()
                    file_session.refresh(existing_doc)
                    new_document = existing_doc

                except Exception as delete_error:
                    print(f"删除旧文件失败: {delete_error}")
                    file_session.rollback()
                    # 如果删除失败，仍然创建新文档记录
                    new_document = Document(
                        kb_id=kb_id,
                        uploader_id=current_user.id,
                        filename=file.filename,
                        storage_path=file_path,
                        file_type=document_service.get_file_type(file.filename),
                        file_size=actual_size,
                        status="pending"
                    )
                    file_session.add(new_document)
                    file_session.commit()
                    file_session.refresh(new_document)
            else:
                # 创建新文档记录
                new_document = Document(
                    kb_id=kb_id,
                    uploader_id=current_user.id,
                    filename=file.filename,
                    storage_path=file_path,
                    file_type=document_service.get_file_type(file.filename),
                    file_size=actual_size,
                    status="pending"
                )
                file_session.add(new_document)
                file_session.commit()
                file_session.refresh(new_document)

            # 异步处理文档
            try:
                # 处理文档并生成分块
                chunks = await document_service.process_document(file_path, file.filename)

                # 添加到向量索引
                success = await vector_service.add_document_chunks(
                    kb_id=kb_id,
                    document_id=new_document.id,
                    chunks=chunks
                )

                if success:
                    new_document.status = "completed"
                else:
                    new_document.status = "failed"

            except Exception as process_error:
                new_document.status = "failed"
                print(f"文档处理失败: {process_error}")

            file_session.add(new_document)
            file_session.commit()

            uploaded_documents.append(new_document)

        except Exception as e:
            print(f"文件 {file.filename} 上传失败: {str(e)}")
            import traceback
            traceback.print_exc()
            file_session.rollback()
            failed_uploads.append({
                "filename": file.filename,
                "error": str(e)
            })
        finally:
            file_session.close()

    return {
        "message": f"批量上传完成，成功: {len(uploaded_documents)}, 失败: {len(failed_uploads)}",
        "uploaded_documents": uploaded_documents,
        "failed_uploads": failed_uploads
    }



