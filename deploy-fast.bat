@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: AI知识库完整部署脚本（Windows版本，集成镜像源加速）
:: 结合原quick-fix.sh和镜像源加速配置

echo 🚀 AI知识库完整部署脚本（加速版）
echo ==================================
echo.

:: 检查必要文件
echo [INFO] 检查必要文件...
if not exist "docker-compose.prod.yml" (
    echo [ERROR] docker-compose.prod.yml 文件不存在
    pause
    exit /b 1
)

if not exist "nginx.prod.conf" (
    echo [ERROR] nginx.prod.conf 文件不存在
    pause
    exit /b 1
)

if not exist "ssl" (
    echo [WARNING] ssl 目录不存在，将创建临时目录
    mkdir ssl
)

echo [SUCCESS] 必要文件检查通过
echo.

:: 检查Docker是否安装
echo [INFO] 检查Docker环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        echo [ERROR] Docker Compose未安装，请先安装Docker Compose
        pause
        exit /b 1
    )
)

echo [SUCCESS] Docker环境检查通过
echo.

:: 停止现有服务并清理
echo [INFO] 停止现有服务...
docker compose down >nul 2>&1
docker compose -f docker-compose.prod.yml down >nul 2>&1

echo [INFO] 清理Docker资源...
docker system prune -f >nul 2>&1
docker builder prune -f >nul 2>&1

echo [SUCCESS] 服务清理完成
echo.

:: 检查前端构建
echo [INFO] 检查前端构建...
if exist "frontend-app\dist" (
    echo [SUCCESS] 发现已有前端构建产物
) else (
    echo [INFO] 使用Docker构建前端...
    docker run --rm -v "%cd%\frontend-app:/app" -w /app node:20-alpine sh -c "npm config set registry https://registry.npmmirror.com && npm ci && npm run build-only"

    if not exist "frontend-app\dist" (
        echo [ERROR] 前端构建失败
        pause
        exit /b 1
    )
    echo [SUCCESS] 前端构建完成
)
echo.

:: 设置环境变量启用BuildKit
echo [INFO] 启用Docker BuildKit加速构建...
set DOCKER_BUILDKIT=1
set COMPOSE_DOCKER_CLI_BUILD=1

:: 构建镜像
echo [INFO] 开始构建Docker镜像...
echo 提示：首次构建可能需要较长时间，请耐心等待...
echo.

docker compose -f docker-compose.prod.yml build --no-cache
if errorlevel 1 (
    echo [ERROR] Docker镜像构建失败
    pause
    exit /b 1
)

echo [SUCCESS] Docker镜像构建完成
echo.

:: 启动服务
echo [INFO] 启动服务...
docker compose -f docker-compose.prod.yml up -d
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    pause
    exit /b 1
)

echo [SUCCESS] 服务启动完成
echo.

:: 等待服务启动
echo [INFO] 等待服务启动...
timeout /t 90 /nobreak >nul

:: 检查容器状态
echo [INFO] 检查服务状态...
docker compose -f docker-compose.prod.yml ps

:: 检查服务健康状态
echo [INFO] 检查服务健康状态...
for /l %%i in (1,1,5) do (
    echo [INFO] 尝试 %%i/5: 检查服务响应...

    :: 检查HTTP
    for /f %%j in ('curl -s -o nul -w "%%{http_code}" http://localhost 2^>nul') do (
        if "%%j"=="200" goto :service_ok
        if "%%j"=="301" goto :service_ok
        if "%%j"=="302" goto :service_ok
    )
    echo [INFO] HTTP服务还未就绪，等待10秒...
    timeout /t 10 /nobreak >nul
)

:service_ok
echo [SUCCESS] HTTP服务正常
echo.

:: 显示服务日志
echo [INFO] 显示最近日志...
echo === 前端日志 ===
docker compose -f docker-compose.prod.yml logs frontend --tail=5
echo.
echo === 后端日志 ===
docker compose -f docker-compose.prod.yml logs backend --tail=5
echo.
echo === 数据库日志 ===
docker compose -f docker-compose.prod.yml logs postgres --tail=3
echo.

:: 显示容器状态
echo [INFO] 容器详细状态:
docker compose -f docker-compose.prod.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
echo.

:: 显示部署信息
echo [SUCCESS] 🎉 部署完成！
echo ==================================
echo 🌐 访问地址:
echo   - HTTPS: https://aiknowledgebase.csicollege.cn
echo   - HTTP: http://aiknowledgebase.csicollege.cn
echo   - 本地: http://localhost
echo.
echo 📝 默认账户:
echo   - 管理员: admin / testpass123
echo   - 测试用户: testuser / testpass123
echo.
echo 🔧 管理命令:
echo   查看日志: docker compose -f docker-compose.prod.yml logs -f
echo   重启服务: docker compose -f docker-compose.prod.yml restart
echo   停止服务: docker compose -f docker-compose.prod.yml down
echo.
echo 🔍 故障排除:
echo   1. 如果无法访问，检查防火墙端口80和443
echo   2. 如果SSL错误，检查证书文件在ssl/目录下
echo   3. 查看详细日志: docker compose -f docker-compose.prod.yml logs -f [service_name]
echo.
echo 🌐 请在浏览器中访问相应地址开始使用！
echo.

pause
