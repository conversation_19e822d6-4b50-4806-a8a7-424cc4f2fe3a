#!/usr/bin/env python3
"""
添加用户设置表
"""
import asyncio
from sqlmodel import SQLModel, create_engine
from app.config import settings
from app.models.user import UserSettings

def create_user_settings_table():
    """创建用户设置表"""
    # 创建数据库引擎
    engine = create_engine(settings.DATABASE_URL, echo=True)
    
    # 创建表
    SQLModel.metadata.create_all(engine, tables=[UserSettings.__table__])
    print("用户设置表创建成功！")

if __name__ == "__main__":
    create_user_settings_table()
