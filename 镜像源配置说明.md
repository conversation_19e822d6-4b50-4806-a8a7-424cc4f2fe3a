# Docker部署加速配置说明

## 🚀 加速原理

为了解决Docker部署慢的问题，我们配置了以下国内镜像源：

### 1. Docker镜像源
- 中科大镜像：`https://docker.mirrors.ustc.edu.cn`
- 网易镜像：`https://hub-mirror.c.163.com`
- 百度镜像：`https://mirror.baidubce.com`
- 腾讯云镜像：`https://ccr.ccs.tencentyun.com`

### 2. Python包镜像源
- 清华大学镜像：`https://pypi.tuna.tsinghua.edu.cn/simple`

### 3. npm包镜像源
- 淘宝镜像：`https://registry.npmmirror.com`

### 4. 系统包镜像源
- 阿里云Debian镜像：`https://mirrors.aliyun.com/debian/`

## 📋 配置方法

### 方法一：使用快速部署脚本（推荐）

#### Linux/macOS:
```bash
chmod +x deploy-fast.sh
./deploy-fast.sh
```

#### Windows:
```cmd
deploy-fast.bat
```

### 方法二：手动配置

#### 1. 配置Docker镜像源

**Linux系统：**
```bash
sudo mkdir -p /etc/docker
sudo cp docker-daemon.json /etc/docker/daemon.json
sudo systemctl restart docker
```

**Windows/macOS（Docker Desktop）：**
1. 打开Docker Desktop设置
2. 进入"Docker Engine"选项卡
3. 将`docker-daemon.json`的内容添加到配置中
4. 点击"Apply & Restart"

#### 2. 验证配置
```bash
docker info | grep -A 10 "Registry Mirrors"
```

## 🔧 性能优化

### 1. 启用BuildKit
```bash
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1
```

### 2. 并行构建
```bash
docker-compose build --parallel
```

### 3. 使用.dockerignore
确保每个服务都有`.dockerignore`文件，排除不必要的文件。

## 📊 预期效果

配置镜像源后，预期可以获得以下性能提升：

| 组件 | 原始速度 | 加速后速度 | 提升倍数 |
|------|----------|------------|----------|
| Docker镜像拉取 | 100KB/s | 1-5MB/s | 10-50x |
| Python包安装 | 50KB/s | 500KB/s-2MB/s | 10-40x |
| npm包安装 | 100KB/s | 1-3MB/s | 10-30x |
| 系统包更新 | 50KB/s | 500KB/s-1MB/s | 10-20x |

## 🛠️ 故障排除

### 1. 镜像源不可用
如果某个镜像源不可用，Docker会自动尝试下一个镜像源。

### 2. 网络问题
```bash
# 测试镜像源连通性
curl -I https://docker.mirrors.ustc.edu.cn
curl -I https://pypi.tuna.tsinghua.edu.cn
curl -I https://registry.npmmirror.com
```

### 3. 权限问题
```bash
# Linux系统确保Docker服务有权限访问配置文件
sudo chown root:root /etc/docker/daemon.json
sudo chmod 644 /etc/docker/daemon.json
```

### 4. 清理缓存
```bash
# 清理Docker缓存
docker system prune -a

# 清理npm缓存
npm cache clean --force

# 清理pip缓存
pip cache purge
```

## 📝 注意事项

1. **首次构建**：即使配置了镜像源，首次构建仍需要下载大量依赖，请耐心等待。

2. **网络环境**：镜像源的效果取决于您的网络环境，不同地区可能有差异。

3. **镜像源更新**：镜像源地址可能会变化，如遇问题请检查最新的镜像源地址。

4. **企业网络**：如果在企业网络环境中，可能需要配置代理或联系网络管理员。

## 🔄 更新镜像源

如需更新镜像源配置，请：

1. 修改`docker-daemon.json`文件
2. 重新运行部署脚本
3. 或手动重启Docker服务

## 📞 技术支持

如果在配置过程中遇到问题，请：

1. 检查网络连接
2. 验证Docker版本兼容性
3. 查看Docker日志：`docker-compose logs`
4. 检查系统防火墙设置
