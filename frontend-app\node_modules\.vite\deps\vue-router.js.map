{"version": 3, "file": "vue-router.js", "names": ["parse<PERSON><PERSON>y", "location", "stringifyQuery", "NavigationType", "NavigationDirection", "history", "replace", "NavigationFailureType", "re", "value", "route", "matchedRoute", "href", "failure", "resolve", "router"], "sources": ["../../vue-router/node_modules/@vue/devtools-api/lib/esm/env.js", "../../vue-router/node_modules/@vue/devtools-api/lib/esm/const.js", "../../vue-router/node_modules/@vue/devtools-api/lib/esm/time.js", "../../vue-router/node_modules/@vue/devtools-api/lib/esm/proxy.js", "../../vue-router/node_modules/@vue/devtools-api/lib/esm/index.js", "../../vue-router/dist/vue-router.mjs"], "sourcesContent": ["export function getDevtoolsGlobalHook() {\n    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;\n}\nexport function getTarget() {\n    // @ts-expect-error navigator and windows are not available in all environments\n    return (typeof navigator !== 'undefined' && typeof window !== 'undefined')\n        ? window\n        : typeof globalThis !== 'undefined'\n            ? globalThis\n            : {};\n}\nexport const isProxyAvailable = typeof Proxy === 'function';\n", "export const HOOK_SETUP = 'devtools-plugin:setup';\nexport const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';\n", "let supported;\nlet perf;\nexport function isPerformanceSupported() {\n    var _a;\n    if (supported !== undefined) {\n        return supported;\n    }\n    if (typeof window !== 'undefined' && window.performance) {\n        supported = true;\n        perf = window.performance;\n    }\n    else if (typeof globalThis !== 'undefined' && ((_a = globalThis.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {\n        supported = true;\n        perf = globalThis.perf_hooks.performance;\n    }\n    else {\n        supported = false;\n    }\n    return supported;\n}\nexport function now() {\n    return isPerformanceSupported() ? perf.now() : Date.now();\n}\n", "import { HOOK_PLUGIN_SETTINGS_SET } from './const.js';\nimport { now } from './time.js';\nexport class ApiProxy {\n    constructor(plugin, hook) {\n        this.target = null;\n        this.targetQueue = [];\n        this.onQueue = [];\n        this.plugin = plugin;\n        this.hook = hook;\n        const defaultSettings = {};\n        if (plugin.settings) {\n            for (const id in plugin.settings) {\n                const item = plugin.settings[id];\n                defaultSettings[id] = item.defaultValue;\n            }\n        }\n        const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;\n        let currentSettings = Object.assign({}, defaultSettings);\n        try {\n            const raw = localStorage.getItem(localSettingsSaveId);\n            const data = JSON.parse(raw);\n            Object.assign(currentSettings, data);\n        }\n        catch (e) {\n            // noop\n        }\n        this.fallbacks = {\n            getSettings() {\n                return currentSettings;\n            },\n            setSettings(value) {\n                try {\n                    localStorage.setItem(localSettingsSaveId, JSON.stringify(value));\n                }\n                catch (e) {\n                    // noop\n                }\n                currentSettings = value;\n            },\n            now() {\n                return now();\n            },\n        };\n        if (hook) {\n            hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {\n                if (pluginId === this.plugin.id) {\n                    this.fallbacks.setSettings(value);\n                }\n            });\n        }\n        this.proxiedOn = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target.on[prop];\n                }\n                else {\n                    return (...args) => {\n                        this.onQueue.push({\n                            method: prop,\n                            args,\n                        });\n                    };\n                }\n            },\n        });\n        this.proxiedTarget = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target[prop];\n                }\n                else if (prop === 'on') {\n                    return this.proxiedOn;\n                }\n                else if (Object.keys(this.fallbacks).includes(prop)) {\n                    return (...args) => {\n                        this.targetQueue.push({\n                            method: prop,\n                            args,\n                            resolve: () => { },\n                        });\n                        return this.fallbacks[prop](...args);\n                    };\n                }\n                else {\n                    return (...args) => {\n                        return new Promise((resolve) => {\n                            this.targetQueue.push({\n                                method: prop,\n                                args,\n                                resolve,\n                            });\n                        });\n                    };\n                }\n            },\n        });\n    }\n    async setRealTarget(target) {\n        this.target = target;\n        for (const item of this.onQueue) {\n            this.target.on[item.method](...item.args);\n        }\n        for (const item of this.targetQueue) {\n            item.resolve(await this.target[item.method](...item.args));\n        }\n    }\n}\n", "import { getDevtoolsGlobalHook, getTarget, isProxyAvailable } from './env.js';\nimport { HOOK_SETUP } from './const.js';\nimport { ApiProxy } from './proxy.js';\nexport * from './api/index.js';\nexport * from './plugin.js';\nexport * from './time.js';\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\n    const descriptor = pluginDescriptor;\n    const target = getTarget();\n    const hook = getDevtoolsGlobalHook();\n    const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\n    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\n        hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\n    }\n    else {\n        const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\n        const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\n        list.push({\n            pluginDescriptor: descriptor,\n            setupFn,\n            proxy,\n        });\n        if (proxy) {\n            setupFn(proxy.proxiedTarget);\n        }\n    }\n}\n", "/*!\n  * vue-router v4.5.1\n  * (c) 2025 <PERSON>\n  * @license MIT\n  */\nimport { getCurrentInstance, inject, onUnmounted, onDeactivated, onActivated, computed, unref, watchEffect, defineComponent, reactive, h, provide, ref, watch, shallowRef, shallowReactive, nextTick } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\nconst isBrowser = typeof document !== 'undefined';\n\n/**\n * Allows differentiating lazy components from functional components and vue-class-component\n * @internal\n *\n * @param component\n */\nfunction isRouteComponent(component) {\n    return (typeof component === 'object' ||\n        'displayName' in component ||\n        'props' in component ||\n        '__vccOpts' in component);\n}\nfunction isESModule(obj) {\n    return (obj.__esModule ||\n        obj[Symbol.toStringTag] === 'Module' ||\n        // support CF with dynamic imports that do not\n        // add the Module string tag\n        (obj.default && isRouteComponent(obj.default)));\n}\nconst assign = Object.assign;\nfunction applyToParams(fn, params) {\n    const newParams = {};\n    for (const key in params) {\n        const value = params[key];\n        newParams[key] = isArray(value)\n            ? value.map(fn)\n            : fn(value);\n    }\n    return newParams;\n}\nconst noop = () => { };\n/**\n * Typesafe alternative to Array.isArray\n * https://github.com/microsoft/TypeScript/pull/48228\n */\nconst isArray = Array.isArray;\n\nfunction warn(msg) {\n    // avoid using ...args as it breaks in older Edge builds\n    const args = Array.from(arguments).slice(1);\n    console.warn.apply(console, ['[Vue Router warn]: ' + msg].concat(args));\n}\n\n/**\n * Encoding Rules (␣ = Space)\n * - Path: ␣ \" < > # ? { }\n * - Query: ␣ \" < > # & =\n * - Hash: ␣ \" < > `\n *\n * On top of that, the RFC3986 (https://tools.ietf.org/html/rfc3986#section-2.2)\n * defines some extra characters to be encoded. Most browsers do not encode them\n * in encodeURI https://github.com/whatwg/url/issues/369, so it may be safer to\n * also encode `!'()*`. Leaving un-encoded only ASCII alphanumeric(`a-zA-Z0-9`)\n * plus `-._~`. This extra safety should be applied to query by patching the\n * string returned by encodeURIComponent encodeURI also encodes `[\\]^`. `\\`\n * should be encoded to avoid ambiguity. Browsers (IE, FF, C) transform a `\\`\n * into a `/` if directly typed in. The _backtick_ (`````) should also be\n * encoded everywhere because some browsers like FF encode it when directly\n * written while others don't. Safari and IE don't encode ``\"<>{}``` in hash.\n */\n// const EXTRA_RESERVED_RE = /[!'()*]/g\n// const encodeReservedReplacer = (c: string) => '%' + c.charCodeAt(0).toString(16)\nconst HASH_RE = /#/g; // %23\nconst AMPERSAND_RE = /&/g; // %26\nconst SLASH_RE = /\\//g; // %2F\nconst EQUAL_RE = /=/g; // %3D\nconst IM_RE = /\\?/g; // %3F\nconst PLUS_RE = /\\+/g; // %2B\n/**\n * NOTE: It's not clear to me if we should encode the + symbol in queries, it\n * seems to be less flexible than not doing so and I can't find out the legacy\n * systems requiring this for regular requests like text/html. In the standard,\n * the encoding of the plus character is only mentioned for\n * application/x-www-form-urlencoded\n * (https://url.spec.whatwg.org/#urlencoded-parsing) and most browsers seems lo\n * leave the plus character as is in queries. To be more flexible, we allow the\n * plus character on the query, but it can also be manually encoded by the user.\n *\n * Resources:\n * - https://url.spec.whatwg.org/#urlencoded-parsing\n * - https://stackoverflow.com/questions/1634271/url-encoding-the-space-character-or-20\n */\nconst ENC_BRACKET_OPEN_RE = /%5B/g; // [\nconst ENC_BRACKET_CLOSE_RE = /%5D/g; // ]\nconst ENC_CARET_RE = /%5E/g; // ^\nconst ENC_BACKTICK_RE = /%60/g; // `\nconst ENC_CURLY_OPEN_RE = /%7B/g; // {\nconst ENC_PIPE_RE = /%7C/g; // |\nconst ENC_CURLY_CLOSE_RE = /%7D/g; // }\nconst ENC_SPACE_RE = /%20/g; // }\n/**\n * Encode characters that need to be encoded on the path, search and hash\n * sections of the URL.\n *\n * @internal\n * @param text - string to encode\n * @returns encoded string\n */\nfunction commonEncode(text) {\n    return encodeURI('' + text)\n        .replace(ENC_PIPE_RE, '|')\n        .replace(ENC_BRACKET_OPEN_RE, '[')\n        .replace(ENC_BRACKET_CLOSE_RE, ']');\n}\n/**\n * Encode characters that need to be encoded on the hash section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeHash(text) {\n    return commonEncode(text)\n        .replace(ENC_CURLY_OPEN_RE, '{')\n        .replace(ENC_CURLY_CLOSE_RE, '}')\n        .replace(ENC_CARET_RE, '^');\n}\n/**\n * Encode characters that need to be encoded query values on the query\n * section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeQueryValue(text) {\n    return (commonEncode(text)\n        // Encode the space as +, encode the + to differentiate it from the space\n        .replace(PLUS_RE, '%2B')\n        .replace(ENC_SPACE_RE, '+')\n        .replace(HASH_RE, '%23')\n        .replace(AMPERSAND_RE, '%26')\n        .replace(ENC_BACKTICK_RE, '`')\n        .replace(ENC_CURLY_OPEN_RE, '{')\n        .replace(ENC_CURLY_CLOSE_RE, '}')\n        .replace(ENC_CARET_RE, '^'));\n}\n/**\n * Like `encodeQueryValue` but also encodes the `=` character.\n *\n * @param text - string to encode\n */\nfunction encodeQueryKey(text) {\n    return encodeQueryValue(text).replace(EQUAL_RE, '%3D');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodePath(text) {\n    return commonEncode(text).replace(HASH_RE, '%23').replace(IM_RE, '%3F');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL as a\n * param. This function encodes everything {@link encodePath} does plus the\n * slash (`/`) character. If `text` is `null` or `undefined`, returns an empty\n * string instead.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeParam(text) {\n    return text == null ? '' : encodePath(text).replace(SLASH_RE, '%2F');\n}\n/**\n * Decode text using `decodeURIComponent`. Returns the original text if it\n * fails.\n *\n * @param text - string to decode\n * @returns decoded string\n */\nfunction decode(text) {\n    try {\n        return decodeURIComponent('' + text);\n    }\n    catch (err) {\n        (process.env.NODE_ENV !== 'production') && warn(`Error decoding \"${text}\". Using original value`);\n    }\n    return '' + text;\n}\n\nconst TRAILING_SLASH_RE = /\\/$/;\nconst removeTrailingSlash = (path) => path.replace(TRAILING_SLASH_RE, '');\n/**\n * Transforms a URI into a normalized history location\n *\n * @param parseQuery\n * @param location - URI to normalize\n * @param currentLocation - current absolute location. Allows resolving relative\n * paths. Must start with `/`. Defaults to `/`\n * @returns a normalized history location\n */\nfunction parseURL(parseQuery, location, currentLocation = '/') {\n    let path, query = {}, searchString = '', hash = '';\n    // Could use URL and URLSearchParams but IE 11 doesn't support it\n    // TODO: move to new URL()\n    const hashPos = location.indexOf('#');\n    let searchPos = location.indexOf('?');\n    // the hash appears before the search, so it's not part of the search string\n    if (hashPos < searchPos && hashPos >= 0) {\n        searchPos = -1;\n    }\n    if (searchPos > -1) {\n        path = location.slice(0, searchPos);\n        searchString = location.slice(searchPos + 1, hashPos > -1 ? hashPos : location.length);\n        query = parseQuery(searchString);\n    }\n    if (hashPos > -1) {\n        path = path || location.slice(0, hashPos);\n        // keep the # character\n        hash = location.slice(hashPos, location.length);\n    }\n    // no search and no query\n    path = resolveRelativePath(path != null ? path : location, currentLocation);\n    // empty path means a relative query or hash `?foo=f`, `#thing`\n    return {\n        fullPath: path + (searchString && '?') + searchString + hash,\n        path,\n        query,\n        hash: decode(hash),\n    };\n}\n/**\n * Stringifies a URL object\n *\n * @param stringifyQuery\n * @param location\n */\nfunction stringifyURL(stringifyQuery, location) {\n    const query = location.query ? stringifyQuery(location.query) : '';\n    return location.path + (query && '?') + query + (location.hash || '');\n}\n/**\n * Strips off the base from the beginning of a location.pathname in a non-case-sensitive way.\n *\n * @param pathname - location.pathname\n * @param base - base to strip off\n */\nfunction stripBase(pathname, base) {\n    // no base or base is not found at the beginning\n    if (!base || !pathname.toLowerCase().startsWith(base.toLowerCase()))\n        return pathname;\n    return pathname.slice(base.length) || '/';\n}\n/**\n * Checks if two RouteLocation are equal. This means that both locations are\n * pointing towards the same {@link RouteRecord} and that all `params`, `query`\n * parameters and `hash` are the same\n *\n * @param stringifyQuery - A function that takes a query object of type LocationQueryRaw and returns a string representation of it.\n * @param a - first {@link RouteLocation}\n * @param b - second {@link RouteLocation}\n */\nfunction isSameRouteLocation(stringifyQuery, a, b) {\n    const aLastIndex = a.matched.length - 1;\n    const bLastIndex = b.matched.length - 1;\n    return (aLastIndex > -1 &&\n        aLastIndex === bLastIndex &&\n        isSameRouteRecord(a.matched[aLastIndex], b.matched[bLastIndex]) &&\n        isSameRouteLocationParams(a.params, b.params) &&\n        stringifyQuery(a.query) === stringifyQuery(b.query) &&\n        a.hash === b.hash);\n}\n/**\n * Check if two `RouteRecords` are equal. Takes into account aliases: they are\n * considered equal to the `RouteRecord` they are aliasing.\n *\n * @param a - first {@link RouteRecord}\n * @param b - second {@link RouteRecord}\n */\nfunction isSameRouteRecord(a, b) {\n    // since the original record has an undefined value for aliasOf\n    // but all aliases point to the original record, this will always compare\n    // the original record\n    return (a.aliasOf || a) === (b.aliasOf || b);\n}\nfunction isSameRouteLocationParams(a, b) {\n    if (Object.keys(a).length !== Object.keys(b).length)\n        return false;\n    for (const key in a) {\n        if (!isSameRouteLocationParamsValue(a[key], b[key]))\n            return false;\n    }\n    return true;\n}\nfunction isSameRouteLocationParamsValue(a, b) {\n    return isArray(a)\n        ? isEquivalentArray(a, b)\n        : isArray(b)\n            ? isEquivalentArray(b, a)\n            : a === b;\n}\n/**\n * Check if two arrays are the same or if an array with one single entry is the\n * same as another primitive value. Used to check query and parameters\n *\n * @param a - array of values\n * @param b - array of values or a single value\n */\nfunction isEquivalentArray(a, b) {\n    return isArray(b)\n        ? a.length === b.length && a.every((value, i) => value === b[i])\n        : a.length === 1 && a[0] === b;\n}\n/**\n * Resolves a relative path that starts with `.`.\n *\n * @param to - path location we are resolving\n * @param from - currentLocation.path, should start with `/`\n */\nfunction resolveRelativePath(to, from) {\n    if (to.startsWith('/'))\n        return to;\n    if ((process.env.NODE_ENV !== 'production') && !from.startsWith('/')) {\n        warn(`Cannot resolve a relative location without an absolute path. Trying to resolve \"${to}\" from \"${from}\". It should look like \"/${from}\".`);\n        return to;\n    }\n    if (!to)\n        return from;\n    const fromSegments = from.split('/');\n    const toSegments = to.split('/');\n    const lastToSegment = toSegments[toSegments.length - 1];\n    // make . and ./ the same (../ === .., ../../ === ../..)\n    // this is the same behavior as new URL()\n    if (lastToSegment === '..' || lastToSegment === '.') {\n        toSegments.push('');\n    }\n    let position = fromSegments.length - 1;\n    let toPosition;\n    let segment;\n    for (toPosition = 0; toPosition < toSegments.length; toPosition++) {\n        segment = toSegments[toPosition];\n        // we stay on the same position\n        if (segment === '.')\n            continue;\n        // go up in the from array\n        if (segment === '..') {\n            // we can't go below zero, but we still need to increment toPosition\n            if (position > 1)\n                position--;\n            // continue\n        }\n        // we reached a non-relative path, we stop here\n        else\n            break;\n    }\n    return (fromSegments.slice(0, position).join('/') +\n        '/' +\n        toSegments.slice(toPosition).join('/'));\n}\n/**\n * Initial route location where the router is. Can be used in navigation guards\n * to differentiate the initial navigation.\n *\n * @example\n * ```js\n * import { START_LOCATION } from 'vue-router'\n *\n * router.beforeEach((to, from) => {\n *   if (from === START_LOCATION) {\n *     // initial navigation\n *   }\n * })\n * ```\n */\nconst START_LOCATION_NORMALIZED = {\n    path: '/',\n    // TODO: could we use a symbol in the future?\n    name: undefined,\n    params: {},\n    query: {},\n    hash: '',\n    fullPath: '/',\n    matched: [],\n    meta: {},\n    redirectedFrom: undefined,\n};\n\nvar NavigationType;\n(function (NavigationType) {\n    NavigationType[\"pop\"] = \"pop\";\n    NavigationType[\"push\"] = \"push\";\n})(NavigationType || (NavigationType = {}));\nvar NavigationDirection;\n(function (NavigationDirection) {\n    NavigationDirection[\"back\"] = \"back\";\n    NavigationDirection[\"forward\"] = \"forward\";\n    NavigationDirection[\"unknown\"] = \"\";\n})(NavigationDirection || (NavigationDirection = {}));\n/**\n * Starting location for Histories\n */\nconst START = '';\n// Generic utils\n/**\n * Normalizes a base by removing any trailing slash and reading the base tag if\n * present.\n *\n * @param base - base to normalize\n */\nfunction normalizeBase(base) {\n    if (!base) {\n        if (isBrowser) {\n            // respect <base> tag\n            const baseEl = document.querySelector('base');\n            base = (baseEl && baseEl.getAttribute('href')) || '/';\n            // strip full URL origin\n            base = base.replace(/^\\w+:\\/\\/[^\\/]+/, '');\n        }\n        else {\n            base = '/';\n        }\n    }\n    // ensure leading slash when it was removed by the regex above avoid leading\n    // slash with hash because the file could be read from the disk like file://\n    // and the leading slash would cause problems\n    if (base[0] !== '/' && base[0] !== '#')\n        base = '/' + base;\n    // remove the trailing slash so all other method can just do `base + fullPath`\n    // to build an href\n    return removeTrailingSlash(base);\n}\n// remove any character before the hash\nconst BEFORE_HASH_RE = /^[^#]+#/;\nfunction createHref(base, location) {\n    return base.replace(BEFORE_HASH_RE, '#') + location;\n}\n\nfunction getElementPosition(el, offset) {\n    const docRect = document.documentElement.getBoundingClientRect();\n    const elRect = el.getBoundingClientRect();\n    return {\n        behavior: offset.behavior,\n        left: elRect.left - docRect.left - (offset.left || 0),\n        top: elRect.top - docRect.top - (offset.top || 0),\n    };\n}\nconst computeScrollPosition = () => ({\n    left: window.scrollX,\n    top: window.scrollY,\n});\nfunction scrollToPosition(position) {\n    let scrollToOptions;\n    if ('el' in position) {\n        const positionEl = position.el;\n        const isIdSelector = typeof positionEl === 'string' && positionEl.startsWith('#');\n        /**\n         * `id`s can accept pretty much any characters, including CSS combinators\n         * like `>` or `~`. It's still possible to retrieve elements using\n         * `document.getElementById('~')` but it needs to be escaped when using\n         * `document.querySelector('#\\\\~')` for it to be valid. The only\n         * requirements for `id`s are them to be unique on the page and to not be\n         * empty (`id=\"\"`). Because of that, when passing an id selector, it should\n         * be properly escaped for it to work with `querySelector`. We could check\n         * for the id selector to be simple (no CSS combinators `+ >~`) but that\n         * would make things inconsistent since they are valid characters for an\n         * `id` but would need to be escaped when using `querySelector`, breaking\n         * their usage and ending up in no selector returned. Selectors need to be\n         * escaped:\n         *\n         * - `#1-thing` becomes `#\\31 -thing`\n         * - `#with~symbols` becomes `#with\\\\~symbols`\n         *\n         * - More information about  the topic can be found at\n         *   https://mathiasbynens.be/notes/html5-id-class.\n         * - Practical example: https://mathiasbynens.be/demo/html5-id\n         */\n        if ((process.env.NODE_ENV !== 'production') && typeof position.el === 'string') {\n            if (!isIdSelector || !document.getElementById(position.el.slice(1))) {\n                try {\n                    const foundEl = document.querySelector(position.el);\n                    if (isIdSelector && foundEl) {\n                        warn(`The selector \"${position.el}\" should be passed as \"el: document.querySelector('${position.el}')\" because it starts with \"#\".`);\n                        // return to avoid other warnings\n                        return;\n                    }\n                }\n                catch (err) {\n                    warn(`The selector \"${position.el}\" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);\n                    // return to avoid other warnings\n                    return;\n                }\n            }\n        }\n        const el = typeof positionEl === 'string'\n            ? isIdSelector\n                ? document.getElementById(positionEl.slice(1))\n                : document.querySelector(positionEl)\n            : positionEl;\n        if (!el) {\n            (process.env.NODE_ENV !== 'production') &&\n                warn(`Couldn't find element using selector \"${position.el}\" returned by scrollBehavior.`);\n            return;\n        }\n        scrollToOptions = getElementPosition(el, position);\n    }\n    else {\n        scrollToOptions = position;\n    }\n    if ('scrollBehavior' in document.documentElement.style)\n        window.scrollTo(scrollToOptions);\n    else {\n        window.scrollTo(scrollToOptions.left != null ? scrollToOptions.left : window.scrollX, scrollToOptions.top != null ? scrollToOptions.top : window.scrollY);\n    }\n}\nfunction getScrollKey(path, delta) {\n    const position = history.state ? history.state.position - delta : -1;\n    return position + path;\n}\nconst scrollPositions = new Map();\nfunction saveScrollPosition(key, scrollPosition) {\n    scrollPositions.set(key, scrollPosition);\n}\nfunction getSavedScrollPosition(key) {\n    const scroll = scrollPositions.get(key);\n    // consume it so it's not used again\n    scrollPositions.delete(key);\n    return scroll;\n}\n// TODO: RFC about how to save scroll position\n/**\n * ScrollBehavior instance used by the router to compute and restore the scroll\n * position when navigating.\n */\n// export interface ScrollHandler<ScrollPositionEntry extends HistoryStateValue, ScrollPosition extends ScrollPositionEntry> {\n//   // returns a scroll position that can be saved in history\n//   compute(): ScrollPositionEntry\n//   // can take an extended ScrollPositionEntry\n//   scroll(position: ScrollPosition): void\n// }\n// export const scrollHandler: ScrollHandler<ScrollPosition> = {\n//   compute: computeScroll,\n//   scroll: scrollToPosition,\n// }\n\nlet createBaseLocation = () => location.protocol + '//' + location.host;\n/**\n * Creates a normalized history location from a window.location object\n * @param base - The base path\n * @param location - The window.location object\n */\nfunction createCurrentLocation(base, location) {\n    const { pathname, search, hash } = location;\n    // allows hash bases like #, /#, #/, #!, #!/, /#!/, or even /folder#end\n    const hashPos = base.indexOf('#');\n    if (hashPos > -1) {\n        let slicePos = hash.includes(base.slice(hashPos))\n            ? base.slice(hashPos).length\n            : 1;\n        let pathFromHash = hash.slice(slicePos);\n        // prepend the starting slash to hash so the url starts with /#\n        if (pathFromHash[0] !== '/')\n            pathFromHash = '/' + pathFromHash;\n        return stripBase(pathFromHash, '');\n    }\n    const path = stripBase(pathname, base);\n    return path + search + hash;\n}\nfunction useHistoryListeners(base, historyState, currentLocation, replace) {\n    let listeners = [];\n    let teardowns = [];\n    // TODO: should it be a stack? a Dict. Check if the popstate listener\n    // can trigger twice\n    let pauseState = null;\n    const popStateHandler = ({ state, }) => {\n        const to = createCurrentLocation(base, location);\n        const from = currentLocation.value;\n        const fromState = historyState.value;\n        let delta = 0;\n        if (state) {\n            currentLocation.value = to;\n            historyState.value = state;\n            // ignore the popstate and reset the pauseState\n            if (pauseState && pauseState === from) {\n                pauseState = null;\n                return;\n            }\n            delta = fromState ? state.position - fromState.position : 0;\n        }\n        else {\n            replace(to);\n        }\n        // Here we could also revert the navigation by calling history.go(-delta)\n        // this listener will have to be adapted to not trigger again and to wait for the url\n        // to be updated before triggering the listeners. Some kind of validation function would also\n        // need to be passed to the listeners so the navigation can be accepted\n        // call all listeners\n        listeners.forEach(listener => {\n            listener(currentLocation.value, from, {\n                delta,\n                type: NavigationType.pop,\n                direction: delta\n                    ? delta > 0\n                        ? NavigationDirection.forward\n                        : NavigationDirection.back\n                    : NavigationDirection.unknown,\n            });\n        });\n    };\n    function pauseListeners() {\n        pauseState = currentLocation.value;\n    }\n    function listen(callback) {\n        // set up the listener and prepare teardown callbacks\n        listeners.push(callback);\n        const teardown = () => {\n            const index = listeners.indexOf(callback);\n            if (index > -1)\n                listeners.splice(index, 1);\n        };\n        teardowns.push(teardown);\n        return teardown;\n    }\n    function beforeUnloadListener() {\n        const { history } = window;\n        if (!history.state)\n            return;\n        history.replaceState(assign({}, history.state, { scroll: computeScrollPosition() }), '');\n    }\n    function destroy() {\n        for (const teardown of teardowns)\n            teardown();\n        teardowns = [];\n        window.removeEventListener('popstate', popStateHandler);\n        window.removeEventListener('beforeunload', beforeUnloadListener);\n    }\n    // set up the listeners and prepare teardown callbacks\n    window.addEventListener('popstate', popStateHandler);\n    // TODO: could we use 'pagehide' or 'visibilitychange' instead?\n    // https://developer.chrome.com/blog/page-lifecycle-api/\n    window.addEventListener('beforeunload', beforeUnloadListener, {\n        passive: true,\n    });\n    return {\n        pauseListeners,\n        listen,\n        destroy,\n    };\n}\n/**\n * Creates a state object\n */\nfunction buildState(back, current, forward, replaced = false, computeScroll = false) {\n    return {\n        back,\n        current,\n        forward,\n        replaced,\n        position: window.history.length,\n        scroll: computeScroll ? computeScrollPosition() : null,\n    };\n}\nfunction useHistoryStateNavigation(base) {\n    const { history, location } = window;\n    // private variables\n    const currentLocation = {\n        value: createCurrentLocation(base, location),\n    };\n    const historyState = { value: history.state };\n    // build current history entry as this is a fresh navigation\n    if (!historyState.value) {\n        changeLocation(currentLocation.value, {\n            back: null,\n            current: currentLocation.value,\n            forward: null,\n            // the length is off by one, we need to decrease it\n            position: history.length - 1,\n            replaced: true,\n            // don't add a scroll as the user may have an anchor, and we want\n            // scrollBehavior to be triggered without a saved position\n            scroll: null,\n        }, true);\n    }\n    function changeLocation(to, state, replace) {\n        /**\n         * if a base tag is provided, and we are on a normal domain, we have to\n         * respect the provided `base` attribute because pushState() will use it and\n         * potentially erase anything before the `#` like at\n         * https://github.com/vuejs/router/issues/685 where a base of\n         * `/folder/#` but a base of `/` would erase the `/folder/` section. If\n         * there is no host, the `<base>` tag makes no sense and if there isn't a\n         * base tag we can just use everything after the `#`.\n         */\n        const hashIndex = base.indexOf('#');\n        const url = hashIndex > -1\n            ? (location.host && document.querySelector('base')\n                ? base\n                : base.slice(hashIndex)) + to\n            : createBaseLocation() + base + to;\n        try {\n            // BROWSER QUIRK\n            // NOTE: Safari throws a SecurityError when calling this function 100 times in 30 seconds\n            history[replace ? 'replaceState' : 'pushState'](state, '', url);\n            historyState.value = state;\n        }\n        catch (err) {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn('Error with push/replace State', err);\n            }\n            else {\n                console.error(err);\n            }\n            // Force the navigation, this also resets the call count\n            location[replace ? 'replace' : 'assign'](url);\n        }\n    }\n    function replace(to, data) {\n        const state = assign({}, history.state, buildState(historyState.value.back, \n        // keep back and forward entries but override current position\n        to, historyState.value.forward, true), data, { position: historyState.value.position });\n        changeLocation(to, state, true);\n        currentLocation.value = to;\n    }\n    function push(to, data) {\n        // Add to current entry the information of where we are going\n        // as well as saving the current position\n        const currentState = assign({}, \n        // use current history state to gracefully handle a wrong call to\n        // history.replaceState\n        // https://github.com/vuejs/router/issues/366\n        historyState.value, history.state, {\n            forward: to,\n            scroll: computeScrollPosition(),\n        });\n        if ((process.env.NODE_ENV !== 'production') && !history.state) {\n            warn(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:\\n\\n` +\n                `history.replaceState(history.state, '', url)\\n\\n` +\n                `You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`);\n        }\n        changeLocation(currentState.current, currentState, true);\n        const state = assign({}, buildState(currentLocation.value, to, null), { position: currentState.position + 1 }, data);\n        changeLocation(to, state, false);\n        currentLocation.value = to;\n    }\n    return {\n        location: currentLocation,\n        state: historyState,\n        push,\n        replace,\n    };\n}\n/**\n * Creates an HTML5 history. Most common history for single page applications.\n *\n * @param base -\n */\nfunction createWebHistory(base) {\n    base = normalizeBase(base);\n    const historyNavigation = useHistoryStateNavigation(base);\n    const historyListeners = useHistoryListeners(base, historyNavigation.state, historyNavigation.location, historyNavigation.replace);\n    function go(delta, triggerListeners = true) {\n        if (!triggerListeners)\n            historyListeners.pauseListeners();\n        history.go(delta);\n    }\n    const routerHistory = assign({\n        // it's overridden right after\n        location: '',\n        base,\n        go,\n        createHref: createHref.bind(null, base),\n    }, historyNavigation, historyListeners);\n    Object.defineProperty(routerHistory, 'location', {\n        enumerable: true,\n        get: () => historyNavigation.location.value,\n    });\n    Object.defineProperty(routerHistory, 'state', {\n        enumerable: true,\n        get: () => historyNavigation.state.value,\n    });\n    return routerHistory;\n}\n\n/**\n * Creates an in-memory based history. The main purpose of this history is to handle SSR. It starts in a special location that is nowhere.\n * It's up to the user to replace that location with the starter location by either calling `router.push` or `router.replace`.\n *\n * @param base - Base applied to all urls, defaults to '/'\n * @returns a history object that can be passed to the router constructor\n */\nfunction createMemoryHistory(base = '') {\n    let listeners = [];\n    let queue = [[START, {}]];\n    let position = 0;\n    base = normalizeBase(base);\n    function setLocation(location, state = {}) {\n        position++;\n        if (position !== queue.length) {\n            // we are in the middle, we remove everything from here in the queue\n            queue.splice(position);\n        }\n        queue.push([location, state]);\n    }\n    function triggerListeners(to, from, { direction, delta }) {\n        const info = {\n            direction,\n            delta,\n            type: NavigationType.pop,\n        };\n        for (const callback of listeners) {\n            callback(to, from, info);\n        }\n    }\n    const routerHistory = {\n        // rewritten by Object.defineProperty\n        location: START,\n        // rewritten by Object.defineProperty\n        state: {},\n        base,\n        createHref: createHref.bind(null, base),\n        replace(to, state) {\n            // remove current entry and decrement position\n            queue.splice(position--, 1);\n            setLocation(to, state);\n        },\n        push(to, state) {\n            setLocation(to, state);\n        },\n        listen(callback) {\n            listeners.push(callback);\n            return () => {\n                const index = listeners.indexOf(callback);\n                if (index > -1)\n                    listeners.splice(index, 1);\n            };\n        },\n        destroy() {\n            listeners = [];\n            queue = [[START, {}]];\n            position = 0;\n        },\n        go(delta, shouldTrigger = true) {\n            const from = this.location;\n            const direction = \n            // we are considering delta === 0 going forward, but in abstract mode\n            // using 0 for the delta doesn't make sense like it does in html5 where\n            // it reloads the page\n            delta < 0 ? NavigationDirection.back : NavigationDirection.forward;\n            position = Math.max(0, Math.min(position + delta, queue.length - 1));\n            if (shouldTrigger) {\n                triggerListeners(this.location, from, {\n                    direction,\n                    delta,\n                });\n            }\n        },\n    };\n    Object.defineProperty(routerHistory, 'location', {\n        enumerable: true,\n        get: () => queue[position][0],\n    });\n    Object.defineProperty(routerHistory, 'state', {\n        enumerable: true,\n        get: () => queue[position][1],\n    });\n    return routerHistory;\n}\n\n/**\n * Creates a hash history. Useful for web applications with no host (e.g. `file://`) or when configuring a server to\n * handle any URL is not possible.\n *\n * @param base - optional base to provide. Defaults to `location.pathname + location.search` If there is a `<base>` tag\n * in the `head`, its value will be ignored in favor of this parameter **but note it affects all the history.pushState()\n * calls**, meaning that if you use a `<base>` tag, it's `href` value **has to match this parameter** (ignoring anything\n * after the `#`).\n *\n * @example\n * ```js\n * // at https://example.com/folder\n * createWebHashHistory() // gives a url of `https://example.com/folder#`\n * createWebHashHistory('/folder/') // gives a url of `https://example.com/folder/#`\n * // if the `#` is provided in the base, it won't be added by `createWebHashHistory`\n * createWebHashHistory('/folder/#/app/') // gives a url of `https://example.com/folder/#/app/`\n * // you should avoid doing this because it changes the original url and breaks copying urls\n * createWebHashHistory('/other-folder/') // gives a url of `https://example.com/other-folder/#`\n *\n * // at file:///usr/etc/folder/index.html\n * // for locations with no `host`, the base is ignored\n * createWebHashHistory('/iAmIgnored') // gives a url of `file:///usr/etc/folder/index.html#`\n * ```\n */\nfunction createWebHashHistory(base) {\n    // Make sure this implementation is fine in terms of encoding, specially for IE11\n    // for `file://`, directly use the pathname and ignore the base\n    // location.pathname contains an initial `/` even at the root: `https://example.com`\n    base = location.host ? base || location.pathname + location.search : '';\n    // allow the user to provide a `#` in the middle: `/base/#/app`\n    if (!base.includes('#'))\n        base += '#';\n    if ((process.env.NODE_ENV !== 'production') && !base.endsWith('#/') && !base.endsWith('#')) {\n        warn(`A hash base must end with a \"#\":\\n\"${base}\" should be \"${base.replace(/#.*$/, '#')}\".`);\n    }\n    return createWebHistory(base);\n}\n\nfunction isRouteLocation(route) {\n    return typeof route === 'string' || (route && typeof route === 'object');\n}\nfunction isRouteName(name) {\n    return typeof name === 'string' || typeof name === 'symbol';\n}\n\nconst NavigationFailureSymbol = Symbol((process.env.NODE_ENV !== 'production') ? 'navigation failure' : '');\n/**\n * Enumeration with all possible types for navigation failures. Can be passed to\n * {@link isNavigationFailure} to check for specific failures.\n */\nvar NavigationFailureType;\n(function (NavigationFailureType) {\n    /**\n     * An aborted navigation is a navigation that failed because a navigation\n     * guard returned `false` or called `next(false)`\n     */\n    NavigationFailureType[NavigationFailureType[\"aborted\"] = 4] = \"aborted\";\n    /**\n     * A cancelled navigation is a navigation that failed because a more recent\n     * navigation finished started (not necessarily finished).\n     */\n    NavigationFailureType[NavigationFailureType[\"cancelled\"] = 8] = \"cancelled\";\n    /**\n     * A duplicated navigation is a navigation that failed because it was\n     * initiated while already being at the exact same location.\n     */\n    NavigationFailureType[NavigationFailureType[\"duplicated\"] = 16] = \"duplicated\";\n})(NavigationFailureType || (NavigationFailureType = {}));\n// DEV only debug messages\nconst ErrorTypeMessages = {\n    [1 /* ErrorTypes.MATCHER_NOT_FOUND */]({ location, currentLocation }) {\n        return `No match for\\n ${JSON.stringify(location)}${currentLocation\n            ? '\\nwhile being at\\n' + JSON.stringify(currentLocation)\n            : ''}`;\n    },\n    [2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */]({ from, to, }) {\n        return `Redirected from \"${from.fullPath}\" to \"${stringifyRoute(to)}\" via a navigation guard.`;\n    },\n    [4 /* ErrorTypes.NAVIGATION_ABORTED */]({ from, to }) {\n        return `Navigation aborted from \"${from.fullPath}\" to \"${to.fullPath}\" via a navigation guard.`;\n    },\n    [8 /* ErrorTypes.NAVIGATION_CANCELLED */]({ from, to }) {\n        return `Navigation cancelled from \"${from.fullPath}\" to \"${to.fullPath}\" with a new navigation.`;\n    },\n    [16 /* ErrorTypes.NAVIGATION_DUPLICATED */]({ from, to }) {\n        return `Avoided redundant navigation to current location: \"${from.fullPath}\".`;\n    },\n};\n/**\n * Creates a typed NavigationFailure object.\n * @internal\n * @param type - NavigationFailureType\n * @param params - { from, to }\n */\nfunction createRouterError(type, params) {\n    // keep full error messages in cjs versions\n    if ((process.env.NODE_ENV !== 'production') || !true) {\n        return assign(new Error(ErrorTypeMessages[type](params)), {\n            type,\n            [NavigationFailureSymbol]: true,\n        }, params);\n    }\n    else {\n        return assign(new Error(), {\n            type,\n            [NavigationFailureSymbol]: true,\n        }, params);\n    }\n}\nfunction isNavigationFailure(error, type) {\n    return (error instanceof Error &&\n        NavigationFailureSymbol in error &&\n        (type == null || !!(error.type & type)));\n}\nconst propertiesToLog = ['params', 'query', 'hash'];\nfunction stringifyRoute(to) {\n    if (typeof to === 'string')\n        return to;\n    if (to.path != null)\n        return to.path;\n    const location = {};\n    for (const key of propertiesToLog) {\n        if (key in to)\n            location[key] = to[key];\n    }\n    return JSON.stringify(location, null, 2);\n}\n\n// default pattern for a param: non-greedy everything but /\nconst BASE_PARAM_PATTERN = '[^/]+?';\nconst BASE_PATH_PARSER_OPTIONS = {\n    sensitive: false,\n    strict: false,\n    start: true,\n    end: true,\n};\n// Special Regex characters that must be escaped in static tokens\nconst REGEX_CHARS_RE = /[.+*?^${}()[\\]/\\\\]/g;\n/**\n * Creates a path parser from an array of Segments (a segment is an array of Tokens)\n *\n * @param segments - array of segments returned by tokenizePath\n * @param extraOptions - optional options for the regexp\n * @returns a PathParser\n */\nfunction tokensToParser(segments, extraOptions) {\n    const options = assign({}, BASE_PATH_PARSER_OPTIONS, extraOptions);\n    // the amount of scores is the same as the length of segments except for the root segment \"/\"\n    const score = [];\n    // the regexp as a string\n    let pattern = options.start ? '^' : '';\n    // extracted keys\n    const keys = [];\n    for (const segment of segments) {\n        // the root segment needs special treatment\n        const segmentScores = segment.length ? [] : [90 /* PathScore.Root */];\n        // allow trailing slash\n        if (options.strict && !segment.length)\n            pattern += '/';\n        for (let tokenIndex = 0; tokenIndex < segment.length; tokenIndex++) {\n            const token = segment[tokenIndex];\n            // resets the score if we are inside a sub-segment /:a-other-:b\n            let subSegmentScore = 40 /* PathScore.Segment */ +\n                (options.sensitive ? 0.25 /* PathScore.BonusCaseSensitive */ : 0);\n            if (token.type === 0 /* TokenType.Static */) {\n                // prepend the slash if we are starting a new segment\n                if (!tokenIndex)\n                    pattern += '/';\n                pattern += token.value.replace(REGEX_CHARS_RE, '\\\\$&');\n                subSegmentScore += 40 /* PathScore.Static */;\n            }\n            else if (token.type === 1 /* TokenType.Param */) {\n                const { value, repeatable, optional, regexp } = token;\n                keys.push({\n                    name: value,\n                    repeatable,\n                    optional,\n                });\n                const re = regexp ? regexp : BASE_PARAM_PATTERN;\n                // the user provided a custom regexp /:id(\\\\d+)\n                if (re !== BASE_PARAM_PATTERN) {\n                    subSegmentScore += 10 /* PathScore.BonusCustomRegExp */;\n                    // make sure the regexp is valid before using it\n                    try {\n                        new RegExp(`(${re})`);\n                    }\n                    catch (err) {\n                        throw new Error(`Invalid custom RegExp for param \"${value}\" (${re}): ` +\n                            err.message);\n                    }\n                }\n                // when we repeat we must take care of the repeating leading slash\n                let subPattern = repeatable ? `((?:${re})(?:/(?:${re}))*)` : `(${re})`;\n                // prepend the slash if we are starting a new segment\n                if (!tokenIndex)\n                    subPattern =\n                        // avoid an optional / if there are more segments e.g. /:p?-static\n                        // or /:p?-:p2\n                        optional && segment.length < 2\n                            ? `(?:/${subPattern})`\n                            : '/' + subPattern;\n                if (optional)\n                    subPattern += '?';\n                pattern += subPattern;\n                subSegmentScore += 20 /* PathScore.Dynamic */;\n                if (optional)\n                    subSegmentScore += -8 /* PathScore.BonusOptional */;\n                if (repeatable)\n                    subSegmentScore += -20 /* PathScore.BonusRepeatable */;\n                if (re === '.*')\n                    subSegmentScore += -50 /* PathScore.BonusWildcard */;\n            }\n            segmentScores.push(subSegmentScore);\n        }\n        // an empty array like /home/<USER>\n        // if (!segment.length) pattern += '/'\n        score.push(segmentScores);\n    }\n    // only apply the strict bonus to the last score\n    if (options.strict && options.end) {\n        const i = score.length - 1;\n        score[i][score[i].length - 1] += 0.7000000000000001 /* PathScore.BonusStrict */;\n    }\n    // TODO: dev only warn double trailing slash\n    if (!options.strict)\n        pattern += '/?';\n    if (options.end)\n        pattern += '$';\n    // allow paths like /dynamic to only match dynamic or dynamic/... but not dynamic_something_else\n    else if (options.strict && !pattern.endsWith('/'))\n        pattern += '(?:/|$)';\n    const re = new RegExp(pattern, options.sensitive ? '' : 'i');\n    function parse(path) {\n        const match = path.match(re);\n        const params = {};\n        if (!match)\n            return null;\n        for (let i = 1; i < match.length; i++) {\n            const value = match[i] || '';\n            const key = keys[i - 1];\n            params[key.name] = value && key.repeatable ? value.split('/') : value;\n        }\n        return params;\n    }\n    function stringify(params) {\n        let path = '';\n        // for optional parameters to allow to be empty\n        let avoidDuplicatedSlash = false;\n        for (const segment of segments) {\n            if (!avoidDuplicatedSlash || !path.endsWith('/'))\n                path += '/';\n            avoidDuplicatedSlash = false;\n            for (const token of segment) {\n                if (token.type === 0 /* TokenType.Static */) {\n                    path += token.value;\n                }\n                else if (token.type === 1 /* TokenType.Param */) {\n                    const { value, repeatable, optional } = token;\n                    const param = value in params ? params[value] : '';\n                    if (isArray(param) && !repeatable) {\n                        throw new Error(`Provided param \"${value}\" is an array but it is not repeatable (* or + modifiers)`);\n                    }\n                    const text = isArray(param)\n                        ? param.join('/')\n                        : param;\n                    if (!text) {\n                        if (optional) {\n                            // if we have more than one optional param like /:a?-static we don't need to care about the optional param\n                            if (segment.length < 2) {\n                                // remove the last slash as we could be at the end\n                                if (path.endsWith('/'))\n                                    path = path.slice(0, -1);\n                                // do not append a slash on the next iteration\n                                else\n                                    avoidDuplicatedSlash = true;\n                            }\n                        }\n                        else\n                            throw new Error(`Missing required param \"${value}\"`);\n                    }\n                    path += text;\n                }\n            }\n        }\n        // avoid empty path when we have multiple optional params\n        return path || '/';\n    }\n    return {\n        re,\n        score,\n        keys,\n        parse,\n        stringify,\n    };\n}\n/**\n * Compares an array of numbers as used in PathParser.score and returns a\n * number. This function can be used to `sort` an array\n *\n * @param a - first array of numbers\n * @param b - second array of numbers\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n * should be sorted first\n */\nfunction compareScoreArray(a, b) {\n    let i = 0;\n    while (i < a.length && i < b.length) {\n        const diff = b[i] - a[i];\n        // only keep going if diff === 0\n        if (diff)\n            return diff;\n        i++;\n    }\n    // if the last subsegment was Static, the shorter segments should be sorted first\n    // otherwise sort the longest segment first\n    if (a.length < b.length) {\n        return a.length === 1 && a[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\n            ? -1\n            : 1;\n    }\n    else if (a.length > b.length) {\n        return b.length === 1 && b[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\n            ? 1\n            : -1;\n    }\n    return 0;\n}\n/**\n * Compare function that can be used with `sort` to sort an array of PathParser\n *\n * @param a - first PathParser\n * @param b - second PathParser\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n */\nfunction comparePathParserScore(a, b) {\n    let i = 0;\n    const aScore = a.score;\n    const bScore = b.score;\n    while (i < aScore.length && i < bScore.length) {\n        const comp = compareScoreArray(aScore[i], bScore[i]);\n        // do not return if both are equal\n        if (comp)\n            return comp;\n        i++;\n    }\n    if (Math.abs(bScore.length - aScore.length) === 1) {\n        if (isLastScoreNegative(aScore))\n            return 1;\n        if (isLastScoreNegative(bScore))\n            return -1;\n    }\n    // if a and b share the same score entries but b has more, sort b first\n    return bScore.length - aScore.length;\n    // this is the ternary version\n    // return aScore.length < bScore.length\n    //   ? 1\n    //   : aScore.length > bScore.length\n    //   ? -1\n    //   : 0\n}\n/**\n * This allows detecting splats at the end of a path: /home/<USER>\n *\n * @param score - score to check\n * @returns true if the last entry is negative\n */\nfunction isLastScoreNegative(score) {\n    const last = score[score.length - 1];\n    return score.length > 0 && last[last.length - 1] < 0;\n}\n\nconst ROOT_TOKEN = {\n    type: 0 /* TokenType.Static */,\n    value: '',\n};\nconst VALID_PARAM_RE = /[a-zA-Z0-9_]/;\n// After some profiling, the cache seems to be unnecessary because tokenizePath\n// (the slowest part of adding a route) is very fast\n// const tokenCache = new Map<string, Token[][]>()\nfunction tokenizePath(path) {\n    if (!path)\n        return [[]];\n    if (path === '/')\n        return [[ROOT_TOKEN]];\n    if (!path.startsWith('/')) {\n        throw new Error((process.env.NODE_ENV !== 'production')\n            ? `Route paths should start with a \"/\": \"${path}\" should be \"/${path}\".`\n            : `Invalid path \"${path}\"`);\n    }\n    // if (tokenCache.has(path)) return tokenCache.get(path)!\n    function crash(message) {\n        throw new Error(`ERR (${state})/\"${buffer}\": ${message}`);\n    }\n    let state = 0 /* TokenizerState.Static */;\n    let previousState = state;\n    const tokens = [];\n    // the segment will always be valid because we get into the initial state\n    // with the leading /\n    let segment;\n    function finalizeSegment() {\n        if (segment)\n            tokens.push(segment);\n        segment = [];\n    }\n    // index on the path\n    let i = 0;\n    // char at index\n    let char;\n    // buffer of the value read\n    let buffer = '';\n    // custom regexp for a param\n    let customRe = '';\n    function consumeBuffer() {\n        if (!buffer)\n            return;\n        if (state === 0 /* TokenizerState.Static */) {\n            segment.push({\n                type: 0 /* TokenType.Static */,\n                value: buffer,\n            });\n        }\n        else if (state === 1 /* TokenizerState.Param */ ||\n            state === 2 /* TokenizerState.ParamRegExp */ ||\n            state === 3 /* TokenizerState.ParamRegExpEnd */) {\n            if (segment.length > 1 && (char === '*' || char === '+'))\n                crash(`A repeatable param (${buffer}) must be alone in its segment. eg: '/:ids+.`);\n            segment.push({\n                type: 1 /* TokenType.Param */,\n                value: buffer,\n                regexp: customRe,\n                repeatable: char === '*' || char === '+',\n                optional: char === '*' || char === '?',\n            });\n        }\n        else {\n            crash('Invalid state to consume buffer');\n        }\n        buffer = '';\n    }\n    function addCharToBuffer() {\n        buffer += char;\n    }\n    while (i < path.length) {\n        char = path[i++];\n        if (char === '\\\\' && state !== 2 /* TokenizerState.ParamRegExp */) {\n            previousState = state;\n            state = 4 /* TokenizerState.EscapeNext */;\n            continue;\n        }\n        switch (state) {\n            case 0 /* TokenizerState.Static */:\n                if (char === '/') {\n                    if (buffer) {\n                        consumeBuffer();\n                    }\n                    finalizeSegment();\n                }\n                else if (char === ':') {\n                    consumeBuffer();\n                    state = 1 /* TokenizerState.Param */;\n                }\n                else {\n                    addCharToBuffer();\n                }\n                break;\n            case 4 /* TokenizerState.EscapeNext */:\n                addCharToBuffer();\n                state = previousState;\n                break;\n            case 1 /* TokenizerState.Param */:\n                if (char === '(') {\n                    state = 2 /* TokenizerState.ParamRegExp */;\n                }\n                else if (VALID_PARAM_RE.test(char)) {\n                    addCharToBuffer();\n                }\n                else {\n                    consumeBuffer();\n                    state = 0 /* TokenizerState.Static */;\n                    // go back one character if we were not modifying\n                    if (char !== '*' && char !== '?' && char !== '+')\n                        i--;\n                }\n                break;\n            case 2 /* TokenizerState.ParamRegExp */:\n                // TODO: is it worth handling nested regexp? like :p(?:prefix_([^/]+)_suffix)\n                // it already works by escaping the closing )\n                // https://paths.esm.dev/?p=AAMeJbiAwQEcDKbAoAAkP60PG2R6QAvgNaA6AFACM2ABuQBB#\n                // is this really something people need since you can also write\n                // /prefix_:p()_suffix\n                if (char === ')') {\n                    // handle the escaped )\n                    if (customRe[customRe.length - 1] == '\\\\')\n                        customRe = customRe.slice(0, -1) + char;\n                    else\n                        state = 3 /* TokenizerState.ParamRegExpEnd */;\n                }\n                else {\n                    customRe += char;\n                }\n                break;\n            case 3 /* TokenizerState.ParamRegExpEnd */:\n                // same as finalizing a param\n                consumeBuffer();\n                state = 0 /* TokenizerState.Static */;\n                // go back one character if we were not modifying\n                if (char !== '*' && char !== '?' && char !== '+')\n                    i--;\n                customRe = '';\n                break;\n            default:\n                crash('Unknown state');\n                break;\n        }\n    }\n    if (state === 2 /* TokenizerState.ParamRegExp */)\n        crash(`Unfinished custom RegExp for param \"${buffer}\"`);\n    consumeBuffer();\n    finalizeSegment();\n    // tokenCache.set(path, tokens)\n    return tokens;\n}\n\nfunction createRouteRecordMatcher(record, parent, options) {\n    const parser = tokensToParser(tokenizePath(record.path), options);\n    // warn against params with the same name\n    if ((process.env.NODE_ENV !== 'production')) {\n        const existingKeys = new Set();\n        for (const key of parser.keys) {\n            if (existingKeys.has(key.name))\n                warn(`Found duplicated params with name \"${key.name}\" for path \"${record.path}\". Only the last one will be available on \"$route.params\".`);\n            existingKeys.add(key.name);\n        }\n    }\n    const matcher = assign(parser, {\n        record,\n        parent,\n        // these needs to be populated by the parent\n        children: [],\n        alias: [],\n    });\n    if (parent) {\n        // both are aliases or both are not aliases\n        // we don't want to mix them because the order is used when\n        // passing originalRecord in Matcher.addRoute\n        if (!matcher.record.aliasOf === !parent.record.aliasOf)\n            parent.children.push(matcher);\n    }\n    return matcher;\n}\n\n/**\n * Creates a Router Matcher.\n *\n * @internal\n * @param routes - array of initial routes\n * @param globalOptions - global route options\n */\nfunction createRouterMatcher(routes, globalOptions) {\n    // normalized ordered array of matchers\n    const matchers = [];\n    const matcherMap = new Map();\n    globalOptions = mergeOptions({ strict: false, end: true, sensitive: false }, globalOptions);\n    function getRecordMatcher(name) {\n        return matcherMap.get(name);\n    }\n    function addRoute(record, parent, originalRecord) {\n        // used later on to remove by name\n        const isRootAdd = !originalRecord;\n        const mainNormalizedRecord = normalizeRouteRecord(record);\n        if ((process.env.NODE_ENV !== 'production')) {\n            checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent);\n        }\n        // we might be the child of an alias\n        mainNormalizedRecord.aliasOf = originalRecord && originalRecord.record;\n        const options = mergeOptions(globalOptions, record);\n        // generate an array of records to correctly handle aliases\n        const normalizedRecords = [mainNormalizedRecord];\n        if ('alias' in record) {\n            const aliases = typeof record.alias === 'string' ? [record.alias] : record.alias;\n            for (const alias of aliases) {\n                normalizedRecords.push(\n                // we need to normalize again to ensure the `mods` property\n                // being non enumerable\n                normalizeRouteRecord(assign({}, mainNormalizedRecord, {\n                    // this allows us to hold a copy of the `components` option\n                    // so that async components cache is hold on the original record\n                    components: originalRecord\n                        ? originalRecord.record.components\n                        : mainNormalizedRecord.components,\n                    path: alias,\n                    // we might be the child of an alias\n                    aliasOf: originalRecord\n                        ? originalRecord.record\n                        : mainNormalizedRecord,\n                    // the aliases are always of the same kind as the original since they\n                    // are defined on the same record\n                })));\n            }\n        }\n        let matcher;\n        let originalMatcher;\n        for (const normalizedRecord of normalizedRecords) {\n            const { path } = normalizedRecord;\n            // Build up the path for nested routes if the child isn't an absolute\n            // route. Only add the / delimiter if the child path isn't empty and if the\n            // parent path doesn't have a trailing slash\n            if (parent && path[0] !== '/') {\n                const parentPath = parent.record.path;\n                const connectingSlash = parentPath[parentPath.length - 1] === '/' ? '' : '/';\n                normalizedRecord.path =\n                    parent.record.path + (path && connectingSlash + path);\n            }\n            if ((process.env.NODE_ENV !== 'production') && normalizedRecord.path === '*') {\n                throw new Error('Catch all routes (\"*\") must now be defined using a param with a custom regexp.\\n' +\n                    'See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.');\n            }\n            // create the object beforehand, so it can be passed to children\n            matcher = createRouteRecordMatcher(normalizedRecord, parent, options);\n            if ((process.env.NODE_ENV !== 'production') && parent && path[0] === '/')\n                checkMissingParamsInAbsolutePath(matcher, parent);\n            // if we are an alias we must tell the original record that we exist,\n            // so we can be removed\n            if (originalRecord) {\n                originalRecord.alias.push(matcher);\n                if ((process.env.NODE_ENV !== 'production')) {\n                    checkSameParams(originalRecord, matcher);\n                }\n            }\n            else {\n                // otherwise, the first record is the original and others are aliases\n                originalMatcher = originalMatcher || matcher;\n                if (originalMatcher !== matcher)\n                    originalMatcher.alias.push(matcher);\n                // remove the route if named and only for the top record (avoid in nested calls)\n                // this works because the original record is the first one\n                if (isRootAdd && record.name && !isAliasRecord(matcher)) {\n                    if ((process.env.NODE_ENV !== 'production')) {\n                        checkSameNameAsAncestor(record, parent);\n                    }\n                    removeRoute(record.name);\n                }\n            }\n            // Avoid adding a record that doesn't display anything. This allows passing through records without a component to\n            // not be reached and pass through the catch all route\n            if (isMatchable(matcher)) {\n                insertMatcher(matcher);\n            }\n            if (mainNormalizedRecord.children) {\n                const children = mainNormalizedRecord.children;\n                for (let i = 0; i < children.length; i++) {\n                    addRoute(children[i], matcher, originalRecord && originalRecord.children[i]);\n                }\n            }\n            // if there was no original record, then the first one was not an alias and all\n            // other aliases (if any) need to reference this record when adding children\n            originalRecord = originalRecord || matcher;\n            // TODO: add normalized records for more flexibility\n            // if (parent && isAliasRecord(originalRecord)) {\n            //   parent.children.push(originalRecord)\n            // }\n        }\n        return originalMatcher\n            ? () => {\n                // since other matchers are aliases, they should be removed by the original matcher\n                removeRoute(originalMatcher);\n            }\n            : noop;\n    }\n    function removeRoute(matcherRef) {\n        if (isRouteName(matcherRef)) {\n            const matcher = matcherMap.get(matcherRef);\n            if (matcher) {\n                matcherMap.delete(matcherRef);\n                matchers.splice(matchers.indexOf(matcher), 1);\n                matcher.children.forEach(removeRoute);\n                matcher.alias.forEach(removeRoute);\n            }\n        }\n        else {\n            const index = matchers.indexOf(matcherRef);\n            if (index > -1) {\n                matchers.splice(index, 1);\n                if (matcherRef.record.name)\n                    matcherMap.delete(matcherRef.record.name);\n                matcherRef.children.forEach(removeRoute);\n                matcherRef.alias.forEach(removeRoute);\n            }\n        }\n    }\n    function getRoutes() {\n        return matchers;\n    }\n    function insertMatcher(matcher) {\n        const index = findInsertionIndex(matcher, matchers);\n        matchers.splice(index, 0, matcher);\n        // only add the original record to the name map\n        if (matcher.record.name && !isAliasRecord(matcher))\n            matcherMap.set(matcher.record.name, matcher);\n    }\n    function resolve(location, currentLocation) {\n        let matcher;\n        let params = {};\n        let path;\n        let name;\n        if ('name' in location && location.name) {\n            matcher = matcherMap.get(location.name);\n            if (!matcher)\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n                    location,\n                });\n            // warn if the user is passing invalid params so they can debug it better when they get removed\n            if ((process.env.NODE_ENV !== 'production')) {\n                const invalidParams = Object.keys(location.params || {}).filter(paramName => !matcher.keys.find(k => k.name === paramName));\n                if (invalidParams.length) {\n                    warn(`Discarded invalid param(s) \"${invalidParams.join('\", \"')}\" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`);\n                }\n            }\n            name = matcher.record.name;\n            params = assign(\n            // paramsFromLocation is a new object\n            paramsFromLocation(currentLocation.params, \n            // only keep params that exist in the resolved location\n            // only keep optional params coming from a parent record\n            matcher.keys\n                .filter(k => !k.optional)\n                .concat(matcher.parent ? matcher.parent.keys.filter(k => k.optional) : [])\n                .map(k => k.name)), \n            // discard any existing params in the current location that do not exist here\n            // #1497 this ensures better active/exact matching\n            location.params &&\n                paramsFromLocation(location.params, matcher.keys.map(k => k.name)));\n            // throws if cannot be stringified\n            path = matcher.stringify(params);\n        }\n        else if (location.path != null) {\n            // no need to resolve the path with the matcher as it was provided\n            // this also allows the user to control the encoding\n            path = location.path;\n            if ((process.env.NODE_ENV !== 'production') && !path.startsWith('/')) {\n                warn(`The Matcher cannot resolve relative paths but received \"${path}\". Unless you directly called \\`matcher.resolve(\"${path}\")\\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`);\n            }\n            matcher = matchers.find(m => m.re.test(path));\n            // matcher should have a value after the loop\n            if (matcher) {\n                // we know the matcher works because we tested the regexp\n                params = matcher.parse(path);\n                name = matcher.record.name;\n            }\n            // location is a relative path\n        }\n        else {\n            // match by name or path of current route\n            matcher = currentLocation.name\n                ? matcherMap.get(currentLocation.name)\n                : matchers.find(m => m.re.test(currentLocation.path));\n            if (!matcher)\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n                    location,\n                    currentLocation,\n                });\n            name = matcher.record.name;\n            // since we are navigating to the same location, we don't need to pick the\n            // params like when `name` is provided\n            params = assign({}, currentLocation.params, location.params);\n            path = matcher.stringify(params);\n        }\n        const matched = [];\n        let parentMatcher = matcher;\n        while (parentMatcher) {\n            // reversed order so parents are at the beginning\n            matched.unshift(parentMatcher.record);\n            parentMatcher = parentMatcher.parent;\n        }\n        return {\n            name,\n            path,\n            params,\n            matched,\n            meta: mergeMetaFields(matched),\n        };\n    }\n    // add initial routes\n    routes.forEach(route => addRoute(route));\n    function clearRoutes() {\n        matchers.length = 0;\n        matcherMap.clear();\n    }\n    return {\n        addRoute,\n        resolve,\n        removeRoute,\n        clearRoutes,\n        getRoutes,\n        getRecordMatcher,\n    };\n}\nfunction paramsFromLocation(params, keys) {\n    const newParams = {};\n    for (const key of keys) {\n        if (key in params)\n            newParams[key] = params[key];\n    }\n    return newParams;\n}\n/**\n * Normalizes a RouteRecordRaw. Creates a copy\n *\n * @param record\n * @returns the normalized version\n */\nfunction normalizeRouteRecord(record) {\n    const normalized = {\n        path: record.path,\n        redirect: record.redirect,\n        name: record.name,\n        meta: record.meta || {},\n        aliasOf: record.aliasOf,\n        beforeEnter: record.beforeEnter,\n        props: normalizeRecordProps(record),\n        children: record.children || [],\n        instances: {},\n        leaveGuards: new Set(),\n        updateGuards: new Set(),\n        enterCallbacks: {},\n        // must be declared afterwards\n        // mods: {},\n        components: 'components' in record\n            ? record.components || null\n            : record.component && { default: record.component },\n    };\n    // mods contain modules and shouldn't be copied,\n    // logged or anything. It's just used for internal\n    // advanced use cases like data loaders\n    Object.defineProperty(normalized, 'mods', {\n        value: {},\n    });\n    return normalized;\n}\n/**\n * Normalize the optional `props` in a record to always be an object similar to\n * components. Also accept a boolean for components.\n * @param record\n */\nfunction normalizeRecordProps(record) {\n    const propsObject = {};\n    // props does not exist on redirect records, but we can set false directly\n    const props = record.props || false;\n    if ('component' in record) {\n        propsObject.default = props;\n    }\n    else {\n        // NOTE: we could also allow a function to be applied to every component.\n        // Would need user feedback for use cases\n        for (const name in record.components)\n            propsObject[name] = typeof props === 'object' ? props[name] : props;\n    }\n    return propsObject;\n}\n/**\n * Checks if a record or any of its parent is an alias\n * @param record\n */\nfunction isAliasRecord(record) {\n    while (record) {\n        if (record.record.aliasOf)\n            return true;\n        record = record.parent;\n    }\n    return false;\n}\n/**\n * Merge meta fields of an array of records\n *\n * @param matched - array of matched records\n */\nfunction mergeMetaFields(matched) {\n    return matched.reduce((meta, record) => assign(meta, record.meta), {});\n}\nfunction mergeOptions(defaults, partialOptions) {\n    const options = {};\n    for (const key in defaults) {\n        options[key] = key in partialOptions ? partialOptions[key] : defaults[key];\n    }\n    return options;\n}\nfunction isSameParam(a, b) {\n    return (a.name === b.name &&\n        a.optional === b.optional &&\n        a.repeatable === b.repeatable);\n}\n/**\n * Check if a path and its alias have the same required params\n *\n * @param a - original record\n * @param b - alias record\n */\nfunction checkSameParams(a, b) {\n    for (const key of a.keys) {\n        if (!key.optional && !b.keys.find(isSameParam.bind(null, key)))\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n    }\n    for (const key of b.keys) {\n        if (!key.optional && !a.keys.find(isSameParam.bind(null, key)))\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n    }\n}\n/**\n * A route with a name and a child with an empty path without a name should warn when adding the route\n *\n * @param mainNormalizedRecord - RouteRecordNormalized\n * @param parent - RouteRecordMatcher\n */\nfunction checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent) {\n    if (parent &&\n        parent.record.name &&\n        !mainNormalizedRecord.name &&\n        !mainNormalizedRecord.path) {\n        warn(`The route named \"${String(parent.record.name)}\" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`);\n    }\n}\nfunction checkSameNameAsAncestor(record, parent) {\n    for (let ancestor = parent; ancestor; ancestor = ancestor.parent) {\n        if (ancestor.record.name === record.name) {\n            throw new Error(`A route named \"${String(record.name)}\" has been added as a ${parent === ancestor ? 'child' : 'descendant'} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`);\n        }\n    }\n}\nfunction checkMissingParamsInAbsolutePath(record, parent) {\n    for (const key of parent.keys) {\n        if (!record.keys.find(isSameParam.bind(null, key)))\n            return warn(`Absolute path \"${record.record.path}\" must have the exact same param named \"${key.name}\" as its parent \"${parent.record.path}\".`);\n    }\n}\n/**\n * Performs a binary search to find the correct insertion index for a new matcher.\n *\n * Matchers are primarily sorted by their score. If scores are tied then we also consider parent/child relationships,\n * with descendants coming before ancestors. If there's still a tie, new routes are inserted after existing routes.\n *\n * @param matcher - new matcher to be inserted\n * @param matchers - existing matchers\n */\nfunction findInsertionIndex(matcher, matchers) {\n    // First phase: binary search based on score\n    let lower = 0;\n    let upper = matchers.length;\n    while (lower !== upper) {\n        const mid = (lower + upper) >> 1;\n        const sortOrder = comparePathParserScore(matcher, matchers[mid]);\n        if (sortOrder < 0) {\n            upper = mid;\n        }\n        else {\n            lower = mid + 1;\n        }\n    }\n    // Second phase: check for an ancestor with the same score\n    const insertionAncestor = getInsertionAncestor(matcher);\n    if (insertionAncestor) {\n        upper = matchers.lastIndexOf(insertionAncestor, upper - 1);\n        if ((process.env.NODE_ENV !== 'production') && upper < 0) {\n            // This should never happen\n            warn(`Finding ancestor route \"${insertionAncestor.record.path}\" failed for \"${matcher.record.path}\"`);\n        }\n    }\n    return upper;\n}\nfunction getInsertionAncestor(matcher) {\n    let ancestor = matcher;\n    while ((ancestor = ancestor.parent)) {\n        if (isMatchable(ancestor) &&\n            comparePathParserScore(matcher, ancestor) === 0) {\n            return ancestor;\n        }\n    }\n    return;\n}\n/**\n * Checks if a matcher can be reachable. This means if it's possible to reach it as a route. For example, routes without\n * a component, or name, or redirect, are just used to group other routes.\n * @param matcher\n * @param matcher.record record of the matcher\n * @returns\n */\nfunction isMatchable({ record }) {\n    return !!(record.name ||\n        (record.components && Object.keys(record.components).length) ||\n        record.redirect);\n}\n\n/**\n * Transforms a queryString into a {@link LocationQuery} object. Accept both, a\n * version with the leading `?` and without Should work as URLSearchParams\n\n * @internal\n *\n * @param search - search string to parse\n * @returns a query object\n */\nfunction parseQuery(search) {\n    const query = {};\n    // avoid creating an object with an empty key and empty value\n    // because of split('&')\n    if (search === '' || search === '?')\n        return query;\n    const hasLeadingIM = search[0] === '?';\n    const searchParams = (hasLeadingIM ? search.slice(1) : search).split('&');\n    for (let i = 0; i < searchParams.length; ++i) {\n        // pre decode the + into space\n        const searchParam = searchParams[i].replace(PLUS_RE, ' ');\n        // allow the = character\n        const eqPos = searchParam.indexOf('=');\n        const key = decode(eqPos < 0 ? searchParam : searchParam.slice(0, eqPos));\n        const value = eqPos < 0 ? null : decode(searchParam.slice(eqPos + 1));\n        if (key in query) {\n            // an extra variable for ts types\n            let currentValue = query[key];\n            if (!isArray(currentValue)) {\n                currentValue = query[key] = [currentValue];\n            }\n            currentValue.push(value);\n        }\n        else {\n            query[key] = value;\n        }\n    }\n    return query;\n}\n/**\n * Stringifies a {@link LocationQueryRaw} object. Like `URLSearchParams`, it\n * doesn't prepend a `?`\n *\n * @internal\n *\n * @param query - query object to stringify\n * @returns string version of the query without the leading `?`\n */\nfunction stringifyQuery(query) {\n    let search = '';\n    for (let key in query) {\n        const value = query[key];\n        key = encodeQueryKey(key);\n        if (value == null) {\n            // only null adds the value\n            if (value !== undefined) {\n                search += (search.length ? '&' : '') + key;\n            }\n            continue;\n        }\n        // keep null values\n        const values = isArray(value)\n            ? value.map(v => v && encodeQueryValue(v))\n            : [value && encodeQueryValue(value)];\n        values.forEach(value => {\n            // skip undefined values in arrays as if they were not present\n            // smaller code than using filter\n            if (value !== undefined) {\n                // only append & with non-empty search\n                search += (search.length ? '&' : '') + key;\n                if (value != null)\n                    search += '=' + value;\n            }\n        });\n    }\n    return search;\n}\n/**\n * Transforms a {@link LocationQueryRaw} into a {@link LocationQuery} by casting\n * numbers into strings, removing keys with an undefined value and replacing\n * undefined with null in arrays\n *\n * @param query - query object to normalize\n * @returns a normalized query object\n */\nfunction normalizeQuery(query) {\n    const normalizedQuery = {};\n    for (const key in query) {\n        const value = query[key];\n        if (value !== undefined) {\n            normalizedQuery[key] = isArray(value)\n                ? value.map(v => (v == null ? null : '' + v))\n                : value == null\n                    ? value\n                    : '' + value;\n        }\n    }\n    return normalizedQuery;\n}\n\n/**\n * RouteRecord being rendered by the closest ancestor Router View. Used for\n * `onBeforeRouteUpdate` and `onBeforeRouteLeave`. rvlm stands for Router View\n * Location Matched\n *\n * @internal\n */\nconst matchedRouteKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location matched' : '');\n/**\n * Allows overriding the router view depth to control which component in\n * `matched` is rendered. rvd stands for Router View Depth\n *\n * @internal\n */\nconst viewDepthKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view depth' : '');\n/**\n * Allows overriding the router instance returned by `useRouter` in tests. r\n * stands for router\n *\n * @internal\n */\nconst routerKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router' : '');\n/**\n * Allows overriding the current route returned by `useRoute` in tests. rl\n * stands for route location\n *\n * @internal\n */\nconst routeLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'route location' : '');\n/**\n * Allows overriding the current route used by router-view. Internally this is\n * used when the `route` prop is passed.\n *\n * @internal\n */\nconst routerViewLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location' : '');\n\n/**\n * Create a list of callbacks that can be reset. Used to create before and after navigation guards list\n */\nfunction useCallbacks() {\n    let handlers = [];\n    function add(handler) {\n        handlers.push(handler);\n        return () => {\n            const i = handlers.indexOf(handler);\n            if (i > -1)\n                handlers.splice(i, 1);\n        };\n    }\n    function reset() {\n        handlers = [];\n    }\n    return {\n        add,\n        list: () => handlers.slice(),\n        reset,\n    };\n}\n\nfunction registerGuard(record, name, guard) {\n    const removeFromList = () => {\n        record[name].delete(guard);\n    };\n    onUnmounted(removeFromList);\n    onDeactivated(removeFromList);\n    onActivated(() => {\n        record[name].add(guard);\n    });\n    record[name].add(guard);\n}\n/**\n * Add a navigation guard that triggers whenever the component for the current\n * location is about to be left. Similar to {@link beforeRouteLeave} but can be\n * used in any component. The guard is removed when the component is unmounted.\n *\n * @param leaveGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteLeave(leaveGuard) {\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\n        warn('getCurrentInstance() returned null. onBeforeRouteLeave() must be called at the top of a setup function');\n        return;\n    }\n    const activeRecord = inject(matchedRouteKey, \n    // to avoid warning\n    {}).value;\n    if (!activeRecord) {\n        (process.env.NODE_ENV !== 'production') &&\n            warn('No active route record was found when calling `onBeforeRouteLeave()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n        return;\n    }\n    registerGuard(activeRecord, 'leaveGuards', leaveGuard);\n}\n/**\n * Add a navigation guard that triggers whenever the current location is about\n * to be updated. Similar to {@link beforeRouteUpdate} but can be used in any\n * component. The guard is removed when the component is unmounted.\n *\n * @param updateGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteUpdate(updateGuard) {\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\n        warn('getCurrentInstance() returned null. onBeforeRouteUpdate() must be called at the top of a setup function');\n        return;\n    }\n    const activeRecord = inject(matchedRouteKey, \n    // to avoid warning\n    {}).value;\n    if (!activeRecord) {\n        (process.env.NODE_ENV !== 'production') &&\n            warn('No active route record was found when calling `onBeforeRouteUpdate()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n        return;\n    }\n    registerGuard(activeRecord, 'updateGuards', updateGuard);\n}\nfunction guardToPromiseFn(guard, to, from, record, name, runWithContext = fn => fn()) {\n    // keep a reference to the enterCallbackArray to prevent pushing callbacks if a new navigation took place\n    const enterCallbackArray = record &&\n        // name is defined if record is because of the function overload\n        (record.enterCallbacks[name] = record.enterCallbacks[name] || []);\n    return () => new Promise((resolve, reject) => {\n        const next = (valid) => {\n            if (valid === false) {\n                reject(createRouterError(4 /* ErrorTypes.NAVIGATION_ABORTED */, {\n                    from,\n                    to,\n                }));\n            }\n            else if (valid instanceof Error) {\n                reject(valid);\n            }\n            else if (isRouteLocation(valid)) {\n                reject(createRouterError(2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */, {\n                    from: to,\n                    to: valid,\n                }));\n            }\n            else {\n                if (enterCallbackArray &&\n                    // since enterCallbackArray is truthy, both record and name also are\n                    record.enterCallbacks[name] === enterCallbackArray &&\n                    typeof valid === 'function') {\n                    enterCallbackArray.push(valid);\n                }\n                resolve();\n            }\n        };\n        // wrapping with Promise.resolve allows it to work with both async and sync guards\n        const guardReturn = runWithContext(() => guard.call(record && record.instances[name], to, from, (process.env.NODE_ENV !== 'production') ? canOnlyBeCalledOnce(next, to, from) : next));\n        let guardCall = Promise.resolve(guardReturn);\n        if (guard.length < 3)\n            guardCall = guardCall.then(next);\n        if ((process.env.NODE_ENV !== 'production') && guard.length > 2) {\n            const message = `The \"next\" callback was never called inside of ${guard.name ? '\"' + guard.name + '\"' : ''}:\\n${guard.toString()}\\n. If you are returning a value instead of calling \"next\", make sure to remove the \"next\" parameter from your function.`;\n            if (typeof guardReturn === 'object' && 'then' in guardReturn) {\n                guardCall = guardCall.then(resolvedValue => {\n                    // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n                    if (!next._called) {\n                        warn(message);\n                        return Promise.reject(new Error('Invalid navigation guard'));\n                    }\n                    return resolvedValue;\n                });\n            }\n            else if (guardReturn !== undefined) {\n                // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n                if (!next._called) {\n                    warn(message);\n                    reject(new Error('Invalid navigation guard'));\n                    return;\n                }\n            }\n        }\n        guardCall.catch(err => reject(err));\n    });\n}\nfunction canOnlyBeCalledOnce(next, to, from) {\n    let called = 0;\n    return function () {\n        if (called++ === 1)\n            warn(`The \"next\" callback was called more than once in one navigation guard when going from \"${from.fullPath}\" to \"${to.fullPath}\". It should be called exactly one time in each navigation guard. This will fail in production.`);\n        // @ts-expect-error: we put it in the original one because it's easier to check\n        next._called = true;\n        if (called === 1)\n            next.apply(null, arguments);\n    };\n}\nfunction extractComponentsGuards(matched, guardType, to, from, runWithContext = fn => fn()) {\n    const guards = [];\n    for (const record of matched) {\n        if ((process.env.NODE_ENV !== 'production') && !record.components && !record.children.length) {\n            warn(`Record with path \"${record.path}\" is either missing a \"component(s)\"` +\n                ` or \"children\" property.`);\n        }\n        for (const name in record.components) {\n            let rawComponent = record.components[name];\n            if ((process.env.NODE_ENV !== 'production')) {\n                if (!rawComponent ||\n                    (typeof rawComponent !== 'object' &&\n                        typeof rawComponent !== 'function')) {\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is not` +\n                        ` a valid component. Received \"${String(rawComponent)}\".`);\n                    // throw to ensure we stop here but warn to ensure the message isn't\n                    // missed by the user\n                    throw new Error('Invalid route component');\n                }\n                else if ('then' in rawComponent) {\n                    // warn if user wrote import('/component.vue') instead of () =>\n                    // import('./component.vue')\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a ` +\n                        `Promise instead of a function that returns a Promise. Did you ` +\n                        `write \"import('./MyPage.vue')\" instead of ` +\n                        `\"() => import('./MyPage.vue')\" ? This will break in ` +\n                        `production if not fixed.`);\n                    const promise = rawComponent;\n                    rawComponent = () => promise;\n                }\n                else if (rawComponent.__asyncLoader &&\n                    // warn only once per component\n                    !rawComponent.__warnedDefineAsync) {\n                    rawComponent.__warnedDefineAsync = true;\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is defined ` +\n                        `using \"defineAsyncComponent()\". ` +\n                        `Write \"() => import('./MyPage.vue')\" instead of ` +\n                        `\"defineAsyncComponent(() => import('./MyPage.vue'))\".`);\n                }\n            }\n            // skip update and leave guards if the route component is not mounted\n            if (guardType !== 'beforeRouteEnter' && !record.instances[name])\n                continue;\n            if (isRouteComponent(rawComponent)) {\n                // __vccOpts is added by vue-class-component and contain the regular options\n                const options = rawComponent.__vccOpts || rawComponent;\n                const guard = options[guardType];\n                guard &&\n                    guards.push(guardToPromiseFn(guard, to, from, record, name, runWithContext));\n            }\n            else {\n                // start requesting the chunk already\n                let componentPromise = rawComponent();\n                if ((process.env.NODE_ENV !== 'production') && !('catch' in componentPromise)) {\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a function that does not return a Promise. If you were passing a functional component, make sure to add a \"displayName\" to the component. This will break in production if not fixed.`);\n                    componentPromise = Promise.resolve(componentPromise);\n                }\n                guards.push(() => componentPromise.then(resolved => {\n                    if (!resolved)\n                        throw new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\"`);\n                    const resolvedComponent = isESModule(resolved)\n                        ? resolved.default\n                        : resolved;\n                    // keep the resolved module for plugins like data loaders\n                    record.mods[name] = resolved;\n                    // replace the function with the resolved component\n                    // cannot be null or undefined because we went into the for loop\n                    record.components[name] = resolvedComponent;\n                    // __vccOpts is added by vue-class-component and contain the regular options\n                    const options = resolvedComponent.__vccOpts || resolvedComponent;\n                    const guard = options[guardType];\n                    return (guard &&\n                        guardToPromiseFn(guard, to, from, record, name, runWithContext)());\n                }));\n            }\n        }\n    }\n    return guards;\n}\n/**\n * Ensures a route is loaded, so it can be passed as o prop to `<RouterView>`.\n *\n * @param route - resolved route to load\n */\nfunction loadRouteLocation(route) {\n    return route.matched.every(record => record.redirect)\n        ? Promise.reject(new Error('Cannot load a route that redirects.'))\n        : Promise.all(route.matched.map(record => record.components &&\n            Promise.all(Object.keys(record.components).reduce((promises, name) => {\n                const rawComponent = record.components[name];\n                if (typeof rawComponent === 'function' &&\n                    !('displayName' in rawComponent)) {\n                    promises.push(rawComponent().then(resolved => {\n                        if (!resolved)\n                            return Promise.reject(new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\". Ensure you passed a function that returns a promise.`));\n                        const resolvedComponent = isESModule(resolved)\n                            ? resolved.default\n                            : resolved;\n                        // keep the resolved module for plugins like data loaders\n                        record.mods[name] = resolved;\n                        // replace the function with the resolved component\n                        // cannot be null or undefined because we went into the for loop\n                        record.components[name] = resolvedComponent;\n                        return;\n                    }));\n                }\n                return promises;\n            }, [])))).then(() => route);\n}\n\n// TODO: we could allow currentRoute as a prop to expose `isActive` and\n// `isExactActive` behavior should go through an RFC\n/**\n * Returns the internal behavior of a {@link RouterLink} without the rendering part.\n *\n * @param props - a `to` location and an optional `replace` flag\n */\nfunction useLink(props) {\n    const router = inject(routerKey);\n    const currentRoute = inject(routeLocationKey);\n    let hasPrevious = false;\n    let previousTo = null;\n    const route = computed(() => {\n        const to = unref(props.to);\n        if ((process.env.NODE_ENV !== 'production') && (!hasPrevious || to !== previousTo)) {\n            if (!isRouteLocation(to)) {\n                if (hasPrevious) {\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- previous to:`, previousTo, `\\n- props:`, props);\n                }\n                else {\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- props:`, props);\n                }\n            }\n            previousTo = to;\n            hasPrevious = true;\n        }\n        return router.resolve(to);\n    });\n    const activeRecordIndex = computed(() => {\n        const { matched } = route.value;\n        const { length } = matched;\n        const routeMatched = matched[length - 1];\n        const currentMatched = currentRoute.matched;\n        if (!routeMatched || !currentMatched.length)\n            return -1;\n        const index = currentMatched.findIndex(isSameRouteRecord.bind(null, routeMatched));\n        if (index > -1)\n            return index;\n        // possible parent record\n        const parentRecordPath = getOriginalPath(matched[length - 2]);\n        return (\n        // we are dealing with nested routes\n        length > 1 &&\n            // if the parent and matched route have the same path, this link is\n            // referring to the empty child. Or we currently are on a different\n            // child of the same parent\n            getOriginalPath(routeMatched) === parentRecordPath &&\n            // avoid comparing the child with its parent\n            currentMatched[currentMatched.length - 1].path !== parentRecordPath\n            ? currentMatched.findIndex(isSameRouteRecord.bind(null, matched[length - 2]))\n            : index);\n    });\n    const isActive = computed(() => activeRecordIndex.value > -1 &&\n        includesParams(currentRoute.params, route.value.params));\n    const isExactActive = computed(() => activeRecordIndex.value > -1 &&\n        activeRecordIndex.value === currentRoute.matched.length - 1 &&\n        isSameRouteLocationParams(currentRoute.params, route.value.params));\n    function navigate(e = {}) {\n        if (guardEvent(e)) {\n            const p = router[unref(props.replace) ? 'replace' : 'push'](unref(props.to)\n            // avoid uncaught errors are they are logged anyway\n            ).catch(noop);\n            if (props.viewTransition &&\n                typeof document !== 'undefined' &&\n                'startViewTransition' in document) {\n                document.startViewTransition(() => p);\n            }\n            return p;\n        }\n        return Promise.resolve();\n    }\n    // devtools only\n    if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n        const instance = getCurrentInstance();\n        if (instance) {\n            const linkContextDevtools = {\n                route: route.value,\n                isActive: isActive.value,\n                isExactActive: isExactActive.value,\n                error: null,\n            };\n            // @ts-expect-error: this is internal\n            instance.__vrl_devtools = instance.__vrl_devtools || [];\n            // @ts-expect-error: this is internal\n            instance.__vrl_devtools.push(linkContextDevtools);\n            watchEffect(() => {\n                linkContextDevtools.route = route.value;\n                linkContextDevtools.isActive = isActive.value;\n                linkContextDevtools.isExactActive = isExactActive.value;\n                linkContextDevtools.error = isRouteLocation(unref(props.to))\n                    ? null\n                    : 'Invalid \"to\" value';\n            }, { flush: 'post' });\n        }\n    }\n    /**\n     * NOTE: update {@link _RouterLinkI}'s `$slots` type when updating this\n     */\n    return {\n        route,\n        href: computed(() => route.value.href),\n        isActive,\n        isExactActive,\n        navigate,\n    };\n}\nfunction preferSingleVNode(vnodes) {\n    return vnodes.length === 1 ? vnodes[0] : vnodes;\n}\nconst RouterLinkImpl = /*#__PURE__*/ defineComponent({\n    name: 'RouterLink',\n    compatConfig: { MODE: 3 },\n    props: {\n        to: {\n            type: [String, Object],\n            required: true,\n        },\n        replace: Boolean,\n        activeClass: String,\n        // inactiveClass: String,\n        exactActiveClass: String,\n        custom: Boolean,\n        ariaCurrentValue: {\n            type: String,\n            default: 'page',\n        },\n        viewTransition: Boolean,\n    },\n    useLink,\n    setup(props, { slots }) {\n        const link = reactive(useLink(props));\n        const { options } = inject(routerKey);\n        const elClass = computed(() => ({\n            [getLinkClass(props.activeClass, options.linkActiveClass, 'router-link-active')]: link.isActive,\n            // [getLinkClass(\n            //   props.inactiveClass,\n            //   options.linkInactiveClass,\n            //   'router-link-inactive'\n            // )]: !link.isExactActive,\n            [getLinkClass(props.exactActiveClass, options.linkExactActiveClass, 'router-link-exact-active')]: link.isExactActive,\n        }));\n        return () => {\n            const children = slots.default && preferSingleVNode(slots.default(link));\n            return props.custom\n                ? children\n                : h('a', {\n                    'aria-current': link.isExactActive\n                        ? props.ariaCurrentValue\n                        : null,\n                    href: link.href,\n                    // this would override user added attrs but Vue will still add\n                    // the listener, so we end up triggering both\n                    onClick: link.navigate,\n                    class: elClass.value,\n                }, children);\n        };\n    },\n});\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to render a link that triggers a navigation on click.\n */\nconst RouterLink = RouterLinkImpl;\nfunction guardEvent(e) {\n    // don't redirect with control keys\n    if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\n        return;\n    // don't redirect when preventDefault called\n    if (e.defaultPrevented)\n        return;\n    // don't redirect on right click\n    if (e.button !== undefined && e.button !== 0)\n        return;\n    // don't redirect if `target=\"_blank\"`\n    // @ts-expect-error getAttribute does exist\n    if (e.currentTarget && e.currentTarget.getAttribute) {\n        // @ts-expect-error getAttribute exists\n        const target = e.currentTarget.getAttribute('target');\n        if (/\\b_blank\\b/i.test(target))\n            return;\n    }\n    // this may be a Weex event which doesn't have this method\n    if (e.preventDefault)\n        e.preventDefault();\n    return true;\n}\nfunction includesParams(outer, inner) {\n    for (const key in inner) {\n        const innerValue = inner[key];\n        const outerValue = outer[key];\n        if (typeof innerValue === 'string') {\n            if (innerValue !== outerValue)\n                return false;\n        }\n        else {\n            if (!isArray(outerValue) ||\n                outerValue.length !== innerValue.length ||\n                innerValue.some((value, i) => value !== outerValue[i]))\n                return false;\n        }\n    }\n    return true;\n}\n/**\n * Get the original path value of a record by following its aliasOf\n * @param record\n */\nfunction getOriginalPath(record) {\n    return record ? (record.aliasOf ? record.aliasOf.path : record.path) : '';\n}\n/**\n * Utility class to get the active class based on defaults.\n * @param propClass\n * @param globalClass\n * @param defaultClass\n */\nconst getLinkClass = (propClass, globalClass, defaultClass) => propClass != null\n    ? propClass\n    : globalClass != null\n        ? globalClass\n        : defaultClass;\n\nconst RouterViewImpl = /*#__PURE__*/ defineComponent({\n    name: 'RouterView',\n    // #674 we manually inherit them\n    inheritAttrs: false,\n    props: {\n        name: {\n            type: String,\n            default: 'default',\n        },\n        route: Object,\n    },\n    // Better compat for @vue/compat users\n    // https://github.com/vuejs/router/issues/1315\n    compatConfig: { MODE: 3 },\n    setup(props, { attrs, slots }) {\n        (process.env.NODE_ENV !== 'production') && warnDeprecatedUsage();\n        const injectedRoute = inject(routerViewLocationKey);\n        const routeToDisplay = computed(() => props.route || injectedRoute.value);\n        const injectedDepth = inject(viewDepthKey, 0);\n        // The depth changes based on empty components option, which allows passthrough routes e.g. routes with children\n        // that are used to reuse the `path` property\n        const depth = computed(() => {\n            let initialDepth = unref(injectedDepth);\n            const { matched } = routeToDisplay.value;\n            let matchedRoute;\n            while ((matchedRoute = matched[initialDepth]) &&\n                !matchedRoute.components) {\n                initialDepth++;\n            }\n            return initialDepth;\n        });\n        const matchedRouteRef = computed(() => routeToDisplay.value.matched[depth.value]);\n        provide(viewDepthKey, computed(() => depth.value + 1));\n        provide(matchedRouteKey, matchedRouteRef);\n        provide(routerViewLocationKey, routeToDisplay);\n        const viewRef = ref();\n        // watch at the same time the component instance, the route record we are\n        // rendering, and the name\n        watch(() => [viewRef.value, matchedRouteRef.value, props.name], ([instance, to, name], [oldInstance, from, oldName]) => {\n            // copy reused instances\n            if (to) {\n                // this will update the instance for new instances as well as reused\n                // instances when navigating to a new route\n                to.instances[name] = instance;\n                // the component instance is reused for a different route or name, so\n                // we copy any saved update or leave guards. With async setup, the\n                // mounting component will mount before the matchedRoute changes,\n                // making instance === oldInstance, so we check if guards have been\n                // added before. This works because we remove guards when\n                // unmounting/deactivating components\n                if (from && from !== to && instance && instance === oldInstance) {\n                    if (!to.leaveGuards.size) {\n                        to.leaveGuards = from.leaveGuards;\n                    }\n                    if (!to.updateGuards.size) {\n                        to.updateGuards = from.updateGuards;\n                    }\n                }\n            }\n            // trigger beforeRouteEnter next callbacks\n            if (instance &&\n                to &&\n                // if there is no instance but to and from are the same this might be\n                // the first visit\n                (!from || !isSameRouteRecord(to, from) || !oldInstance)) {\n                (to.enterCallbacks[name] || []).forEach(callback => callback(instance));\n            }\n        }, { flush: 'post' });\n        return () => {\n            const route = routeToDisplay.value;\n            // we need the value at the time we render because when we unmount, we\n            // navigated to a different location so the value is different\n            const currentName = props.name;\n            const matchedRoute = matchedRouteRef.value;\n            const ViewComponent = matchedRoute && matchedRoute.components[currentName];\n            if (!ViewComponent) {\n                return normalizeSlot(slots.default, { Component: ViewComponent, route });\n            }\n            // props from route configuration\n            const routePropsOption = matchedRoute.props[currentName];\n            const routeProps = routePropsOption\n                ? routePropsOption === true\n                    ? route.params\n                    : typeof routePropsOption === 'function'\n                        ? routePropsOption(route)\n                        : routePropsOption\n                : null;\n            const onVnodeUnmounted = vnode => {\n                // remove the instance reference to prevent leak\n                if (vnode.component.isUnmounted) {\n                    matchedRoute.instances[currentName] = null;\n                }\n            };\n            const component = h(ViewComponent, assign({}, routeProps, attrs, {\n                onVnodeUnmounted,\n                ref: viewRef,\n            }));\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                isBrowser &&\n                component.ref) {\n                // TODO: can display if it's an alias, its props\n                const info = {\n                    depth: depth.value,\n                    name: matchedRoute.name,\n                    path: matchedRoute.path,\n                    meta: matchedRoute.meta,\n                };\n                const internalInstances = isArray(component.ref)\n                    ? component.ref.map(r => r.i)\n                    : [component.ref.i];\n                internalInstances.forEach(instance => {\n                    // @ts-expect-error\n                    instance.__vrv_devtools = info;\n                });\n            }\n            return (\n            // pass the vnode to the slot as a prop.\n            // h and <component :is=\"...\"> both accept vnodes\n            normalizeSlot(slots.default, { Component: component, route }) ||\n                component);\n        };\n    },\n});\nfunction normalizeSlot(slot, data) {\n    if (!slot)\n        return null;\n    const slotContent = slot(data);\n    return slotContent.length === 1 ? slotContent[0] : slotContent;\n}\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to display the current route the user is at.\n */\nconst RouterView = RouterViewImpl;\n// warn against deprecated usage with <transition> & <keep-alive>\n// due to functional component being no longer eager in Vue 3\nfunction warnDeprecatedUsage() {\n    const instance = getCurrentInstance();\n    const parentName = instance.parent && instance.parent.type.name;\n    const parentSubTreeType = instance.parent && instance.parent.subTree && instance.parent.subTree.type;\n    if (parentName &&\n        (parentName === 'KeepAlive' || parentName.includes('Transition')) &&\n        typeof parentSubTreeType === 'object' &&\n        parentSubTreeType.name === 'RouterView') {\n        const comp = parentName === 'KeepAlive' ? 'keep-alive' : 'transition';\n        warn(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.\\n` +\n            `Use slot props instead:\\n\\n` +\n            `<router-view v-slot=\"{ Component }\">\\n` +\n            `  <${comp}>\\n` +\n            `    <component :is=\"Component\" />\\n` +\n            `  </${comp}>\\n` +\n            `</router-view>`);\n    }\n}\n\n/**\n * Copies a route location and removes any problematic properties that cannot be shown in devtools (e.g. Vue instances).\n *\n * @param routeLocation - routeLocation to format\n * @param tooltip - optional tooltip\n * @returns a copy of the routeLocation\n */\nfunction formatRouteLocation(routeLocation, tooltip) {\n    const copy = assign({}, routeLocation, {\n        // remove variables that can contain vue instances\n        matched: routeLocation.matched.map(matched => omit(matched, ['instances', 'children', 'aliasOf'])),\n    });\n    return {\n        _custom: {\n            type: null,\n            readOnly: true,\n            display: routeLocation.fullPath,\n            tooltip,\n            value: copy,\n        },\n    };\n}\nfunction formatDisplay(display) {\n    return {\n        _custom: {\n            display,\n        },\n    };\n}\n// to support multiple router instances\nlet routerId = 0;\nfunction addDevtools(app, router, matcher) {\n    // Take over router.beforeEach and afterEach\n    // make sure we are not registering the devtool twice\n    if (router.__hasDevtools)\n        return;\n    router.__hasDevtools = true;\n    // increment to support multiple router instances\n    const id = routerId++;\n    setupDevtoolsPlugin({\n        id: 'org.vuejs.router' + (id ? '.' + id : ''),\n        label: 'Vue Router',\n        packageName: 'vue-router',\n        homepage: 'https://router.vuejs.org',\n        logo: 'https://router.vuejs.org/logo.png',\n        componentStateTypes: ['Routing'],\n        app,\n    }, api => {\n        if (typeof api.now !== 'function') {\n            console.warn('[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n        }\n        // display state added by the router\n        api.on.inspectComponent((payload, ctx) => {\n            if (payload.instanceData) {\n                payload.instanceData.state.push({\n                    type: 'Routing',\n                    key: '$route',\n                    editable: false,\n                    value: formatRouteLocation(router.currentRoute.value, 'Current Route'),\n                });\n            }\n        });\n        // mark router-link as active and display tags on router views\n        api.on.visitComponentTree(({ treeNode: node, componentInstance }) => {\n            if (componentInstance.__vrv_devtools) {\n                const info = componentInstance.__vrv_devtools;\n                node.tags.push({\n                    label: (info.name ? `${info.name.toString()}: ` : '') + info.path,\n                    textColor: 0,\n                    tooltip: 'This component is rendered by &lt;router-view&gt;',\n                    backgroundColor: PINK_500,\n                });\n            }\n            // if multiple useLink are used\n            if (isArray(componentInstance.__vrl_devtools)) {\n                componentInstance.__devtoolsApi = api;\n                componentInstance.__vrl_devtools.forEach(devtoolsData => {\n                    let label = devtoolsData.route.path;\n                    let backgroundColor = ORANGE_400;\n                    let tooltip = '';\n                    let textColor = 0;\n                    if (devtoolsData.error) {\n                        label = devtoolsData.error;\n                        backgroundColor = RED_100;\n                        textColor = RED_700;\n                    }\n                    else if (devtoolsData.isExactActive) {\n                        backgroundColor = LIME_500;\n                        tooltip = 'This is exactly active';\n                    }\n                    else if (devtoolsData.isActive) {\n                        backgroundColor = BLUE_600;\n                        tooltip = 'This link is active';\n                    }\n                    node.tags.push({\n                        label,\n                        textColor,\n                        tooltip,\n                        backgroundColor,\n                    });\n                });\n            }\n        });\n        watch(router.currentRoute, () => {\n            // refresh active state\n            refreshRoutesView();\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(routerInspectorId);\n            api.sendInspectorState(routerInspectorId);\n        });\n        const navigationsLayerId = 'router:navigations:' + id;\n        api.addTimelineLayer({\n            id: navigationsLayerId,\n            label: `Router${id ? ' ' + id : ''} Navigations`,\n            color: 0x40a8c4,\n        });\n        // const errorsLayerId = 'router:errors'\n        // api.addTimelineLayer({\n        //   id: errorsLayerId,\n        //   label: 'Router Errors',\n        //   color: 0xea5455,\n        // })\n        router.onError((error, to) => {\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    title: 'Error during Navigation',\n                    subtitle: to.fullPath,\n                    logType: 'error',\n                    time: api.now(),\n                    data: { error },\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        // attached to `meta` and used to group events\n        let navigationId = 0;\n        router.beforeEach((to, from) => {\n            const data = {\n                guard: formatDisplay('beforeEach'),\n                from: formatRouteLocation(from, 'Current Location during this navigation'),\n                to: formatRouteLocation(to, 'Target location'),\n            };\n            // Used to group navigations together, hide from devtools\n            Object.defineProperty(to.meta, '__navigationId', {\n                value: navigationId++,\n            });\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    time: api.now(),\n                    title: 'Start of navigation',\n                    subtitle: to.fullPath,\n                    data,\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        router.afterEach((to, from, failure) => {\n            const data = {\n                guard: formatDisplay('afterEach'),\n            };\n            if (failure) {\n                data.failure = {\n                    _custom: {\n                        type: Error,\n                        readOnly: true,\n                        display: failure ? failure.message : '',\n                        tooltip: 'Navigation Failure',\n                        value: failure,\n                    },\n                };\n                data.status = formatDisplay('❌');\n            }\n            else {\n                data.status = formatDisplay('✅');\n            }\n            // we set here to have the right order\n            data.from = formatRouteLocation(from, 'Current Location during this navigation');\n            data.to = formatRouteLocation(to, 'Target location');\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    title: 'End of navigation',\n                    subtitle: to.fullPath,\n                    time: api.now(),\n                    data,\n                    logType: failure ? 'warning' : 'default',\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        /**\n         * Inspector of Existing routes\n         */\n        const routerInspectorId = 'router-inspector:' + id;\n        api.addInspector({\n            id: routerInspectorId,\n            label: 'Routes' + (id ? ' ' + id : ''),\n            icon: 'book',\n            treeFilterPlaceholder: 'Search routes',\n        });\n        function refreshRoutesView() {\n            // the routes view isn't active\n            if (!activeRoutesPayload)\n                return;\n            const payload = activeRoutesPayload;\n            // children routes will appear as nested\n            let routes = matcher.getRoutes().filter(route => !route.parent ||\n                // these routes have a parent with no component which will not appear in the view\n                // therefore we still need to include them\n                !route.parent.record.components);\n            // reset match state to false\n            routes.forEach(resetMatchStateOnRouteRecord);\n            // apply a match state if there is a payload\n            if (payload.filter) {\n                routes = routes.filter(route => \n                // save matches state based on the payload\n                isRouteMatching(route, payload.filter.toLowerCase()));\n            }\n            // mark active routes\n            routes.forEach(route => markRouteRecordActive(route, router.currentRoute.value));\n            payload.rootNodes = routes.map(formatRouteRecordForInspector);\n        }\n        let activeRoutesPayload;\n        api.on.getInspectorTree(payload => {\n            activeRoutesPayload = payload;\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\n                refreshRoutesView();\n            }\n        });\n        /**\n         * Display information about the currently selected route record\n         */\n        api.on.getInspectorState(payload => {\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\n                const routes = matcher.getRoutes();\n                const route = routes.find(route => route.record.__vd_id === payload.nodeId);\n                if (route) {\n                    payload.state = {\n                        options: formatRouteRecordMatcherForStateInspector(route),\n                    };\n                }\n            }\n        });\n        api.sendInspectorTree(routerInspectorId);\n        api.sendInspectorState(routerInspectorId);\n    });\n}\nfunction modifierForKey(key) {\n    if (key.optional) {\n        return key.repeatable ? '*' : '?';\n    }\n    else {\n        return key.repeatable ? '+' : '';\n    }\n}\nfunction formatRouteRecordMatcherForStateInspector(route) {\n    const { record } = route;\n    const fields = [\n        { editable: false, key: 'path', value: record.path },\n    ];\n    if (record.name != null) {\n        fields.push({\n            editable: false,\n            key: 'name',\n            value: record.name,\n        });\n    }\n    fields.push({ editable: false, key: 'regexp', value: route.re });\n    if (route.keys.length) {\n        fields.push({\n            editable: false,\n            key: 'keys',\n            value: {\n                _custom: {\n                    type: null,\n                    readOnly: true,\n                    display: route.keys\n                        .map(key => `${key.name}${modifierForKey(key)}`)\n                        .join(' '),\n                    tooltip: 'Param keys',\n                    value: route.keys,\n                },\n            },\n        });\n    }\n    if (record.redirect != null) {\n        fields.push({\n            editable: false,\n            key: 'redirect',\n            value: record.redirect,\n        });\n    }\n    if (route.alias.length) {\n        fields.push({\n            editable: false,\n            key: 'aliases',\n            value: route.alias.map(alias => alias.record.path),\n        });\n    }\n    if (Object.keys(route.record.meta).length) {\n        fields.push({\n            editable: false,\n            key: 'meta',\n            value: route.record.meta,\n        });\n    }\n    fields.push({\n        key: 'score',\n        editable: false,\n        value: {\n            _custom: {\n                type: null,\n                readOnly: true,\n                display: route.score.map(score => score.join(', ')).join(' | '),\n                tooltip: 'Score used to sort routes',\n                value: route.score,\n            },\n        },\n    });\n    return fields;\n}\n/**\n * Extracted from tailwind palette\n */\nconst PINK_500 = 0xec4899;\nconst BLUE_600 = 0x2563eb;\nconst LIME_500 = 0x84cc16;\nconst CYAN_400 = 0x22d3ee;\nconst ORANGE_400 = 0xfb923c;\n// const GRAY_100 = 0xf4f4f5\nconst DARK = 0x666666;\nconst RED_100 = 0xfee2e2;\nconst RED_700 = 0xb91c1c;\nfunction formatRouteRecordForInspector(route) {\n    const tags = [];\n    const { record } = route;\n    if (record.name != null) {\n        tags.push({\n            label: String(record.name),\n            textColor: 0,\n            backgroundColor: CYAN_400,\n        });\n    }\n    if (record.aliasOf) {\n        tags.push({\n            label: 'alias',\n            textColor: 0,\n            backgroundColor: ORANGE_400,\n        });\n    }\n    if (route.__vd_match) {\n        tags.push({\n            label: 'matches',\n            textColor: 0,\n            backgroundColor: PINK_500,\n        });\n    }\n    if (route.__vd_exactActive) {\n        tags.push({\n            label: 'exact',\n            textColor: 0,\n            backgroundColor: LIME_500,\n        });\n    }\n    if (route.__vd_active) {\n        tags.push({\n            label: 'active',\n            textColor: 0,\n            backgroundColor: BLUE_600,\n        });\n    }\n    if (record.redirect) {\n        tags.push({\n            label: typeof record.redirect === 'string'\n                ? `redirect: ${record.redirect}`\n                : 'redirects',\n            textColor: 0xffffff,\n            backgroundColor: DARK,\n        });\n    }\n    // add an id to be able to select it. Using the `path` is not possible because\n    // empty path children would collide with their parents\n    let id = record.__vd_id;\n    if (id == null) {\n        id = String(routeRecordId++);\n        record.__vd_id = id;\n    }\n    return {\n        id,\n        label: record.path,\n        tags,\n        children: route.children.map(formatRouteRecordForInspector),\n    };\n}\n//  incremental id for route records and inspector state\nlet routeRecordId = 0;\nconst EXTRACT_REGEXP_RE = /^\\/(.*)\\/([a-z]*)$/;\nfunction markRouteRecordActive(route, currentRoute) {\n    // no route will be active if matched is empty\n    // reset the matching state\n    const isExactActive = currentRoute.matched.length &&\n        isSameRouteRecord(currentRoute.matched[currentRoute.matched.length - 1], route.record);\n    route.__vd_exactActive = route.__vd_active = isExactActive;\n    if (!isExactActive) {\n        route.__vd_active = currentRoute.matched.some(match => isSameRouteRecord(match, route.record));\n    }\n    route.children.forEach(childRoute => markRouteRecordActive(childRoute, currentRoute));\n}\nfunction resetMatchStateOnRouteRecord(route) {\n    route.__vd_match = false;\n    route.children.forEach(resetMatchStateOnRouteRecord);\n}\nfunction isRouteMatching(route, filter) {\n    const found = String(route.re).match(EXTRACT_REGEXP_RE);\n    route.__vd_match = false;\n    if (!found || found.length < 3) {\n        return false;\n    }\n    // use a regexp without $ at the end to match nested routes better\n    const nonEndingRE = new RegExp(found[1].replace(/\\$$/, ''), found[2]);\n    if (nonEndingRE.test(filter)) {\n        // mark children as matches\n        route.children.forEach(child => isRouteMatching(child, filter));\n        // exception case: `/`\n        if (route.record.path !== '/' || filter === '/') {\n            route.__vd_match = route.re.test(filter);\n            return true;\n        }\n        // hide the / route\n        return false;\n    }\n    const path = route.record.path.toLowerCase();\n    const decodedPath = decode(path);\n    // also allow partial matching on the path\n    if (!filter.startsWith('/') &&\n        (decodedPath.includes(filter) || path.includes(filter)))\n        return true;\n    if (decodedPath.startsWith(filter) || path.startsWith(filter))\n        return true;\n    if (route.record.name && String(route.record.name).includes(filter))\n        return true;\n    return route.children.some(child => isRouteMatching(child, filter));\n}\nfunction omit(obj, keys) {\n    const ret = {};\n    for (const key in obj) {\n        if (!keys.includes(key)) {\n            // @ts-expect-error\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n\n/**\n * Creates a Router instance that can be used by a Vue app.\n *\n * @param options - {@link RouterOptions}\n */\nfunction createRouter(options) {\n    const matcher = createRouterMatcher(options.routes, options);\n    const parseQuery$1 = options.parseQuery || parseQuery;\n    const stringifyQuery$1 = options.stringifyQuery || stringifyQuery;\n    const routerHistory = options.history;\n    if ((process.env.NODE_ENV !== 'production') && !routerHistory)\n        throw new Error('Provide the \"history\" option when calling \"createRouter()\":' +\n            ' https://router.vuejs.org/api/interfaces/RouterOptions.html#history');\n    const beforeGuards = useCallbacks();\n    const beforeResolveGuards = useCallbacks();\n    const afterGuards = useCallbacks();\n    const currentRoute = shallowRef(START_LOCATION_NORMALIZED);\n    let pendingLocation = START_LOCATION_NORMALIZED;\n    // leave the scrollRestoration if no scrollBehavior is provided\n    if (isBrowser && options.scrollBehavior && 'scrollRestoration' in history) {\n        history.scrollRestoration = 'manual';\n    }\n    const normalizeParams = applyToParams.bind(null, paramValue => '' + paramValue);\n    const encodeParams = applyToParams.bind(null, encodeParam);\n    const decodeParams = \n    // @ts-expect-error: intentionally avoid the type check\n    applyToParams.bind(null, decode);\n    function addRoute(parentOrRoute, route) {\n        let parent;\n        let record;\n        if (isRouteName(parentOrRoute)) {\n            parent = matcher.getRecordMatcher(parentOrRoute);\n            if ((process.env.NODE_ENV !== 'production') && !parent) {\n                warn(`Parent route \"${String(parentOrRoute)}\" not found when adding child route`, route);\n            }\n            record = route;\n        }\n        else {\n            record = parentOrRoute;\n        }\n        return matcher.addRoute(record, parent);\n    }\n    function removeRoute(name) {\n        const recordMatcher = matcher.getRecordMatcher(name);\n        if (recordMatcher) {\n            matcher.removeRoute(recordMatcher);\n        }\n        else if ((process.env.NODE_ENV !== 'production')) {\n            warn(`Cannot remove non-existent route \"${String(name)}\"`);\n        }\n    }\n    function getRoutes() {\n        return matcher.getRoutes().map(routeMatcher => routeMatcher.record);\n    }\n    function hasRoute(name) {\n        return !!matcher.getRecordMatcher(name);\n    }\n    function resolve(rawLocation, currentLocation) {\n        // const resolve: Router['resolve'] = (rawLocation: RouteLocationRaw, currentLocation) => {\n        // const objectLocation = routerLocationAsObject(rawLocation)\n        // we create a copy to modify it later\n        currentLocation = assign({}, currentLocation || currentRoute.value);\n        if (typeof rawLocation === 'string') {\n            const locationNormalized = parseURL(parseQuery$1, rawLocation, currentLocation.path);\n            const matchedRoute = matcher.resolve({ path: locationNormalized.path }, currentLocation);\n            const href = routerHistory.createHref(locationNormalized.fullPath);\n            if ((process.env.NODE_ENV !== 'production')) {\n                if (href.startsWith('//'))\n                    warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n                else if (!matchedRoute.matched.length) {\n                    warn(`No match found for location with path \"${rawLocation}\"`);\n                }\n            }\n            // locationNormalized is always a new object\n            return assign(locationNormalized, matchedRoute, {\n                params: decodeParams(matchedRoute.params),\n                hash: decode(locationNormalized.hash),\n                redirectedFrom: undefined,\n                href,\n            });\n        }\n        if ((process.env.NODE_ENV !== 'production') && !isRouteLocation(rawLocation)) {\n            warn(`router.resolve() was passed an invalid location. This will fail in production.\\n- Location:`, rawLocation);\n            return resolve({});\n        }\n        let matcherLocation;\n        // path could be relative in object as well\n        if (rawLocation.path != null) {\n            if ((process.env.NODE_ENV !== 'production') &&\n                'params' in rawLocation &&\n                !('name' in rawLocation) &&\n                // @ts-expect-error: the type is never\n                Object.keys(rawLocation.params).length) {\n                warn(`Path \"${rawLocation.path}\" was passed with params but they will be ignored. Use a named route alongside params instead.`);\n            }\n            matcherLocation = assign({}, rawLocation, {\n                path: parseURL(parseQuery$1, rawLocation.path, currentLocation.path).path,\n            });\n        }\n        else {\n            // remove any nullish param\n            const targetParams = assign({}, rawLocation.params);\n            for (const key in targetParams) {\n                if (targetParams[key] == null) {\n                    delete targetParams[key];\n                }\n            }\n            // pass encoded values to the matcher, so it can produce encoded path and fullPath\n            matcherLocation = assign({}, rawLocation, {\n                params: encodeParams(targetParams),\n            });\n            // current location params are decoded, we need to encode them in case the\n            // matcher merges the params\n            currentLocation.params = encodeParams(currentLocation.params);\n        }\n        const matchedRoute = matcher.resolve(matcherLocation, currentLocation);\n        const hash = rawLocation.hash || '';\n        if ((process.env.NODE_ENV !== 'production') && hash && !hash.startsWith('#')) {\n            warn(`A \\`hash\\` should always start with the character \"#\". Replace \"${hash}\" with \"#${hash}\".`);\n        }\n        // the matcher might have merged current location params, so\n        // we need to run the decoding again\n        matchedRoute.params = normalizeParams(decodeParams(matchedRoute.params));\n        const fullPath = stringifyURL(stringifyQuery$1, assign({}, rawLocation, {\n            hash: encodeHash(hash),\n            path: matchedRoute.path,\n        }));\n        const href = routerHistory.createHref(fullPath);\n        if ((process.env.NODE_ENV !== 'production')) {\n            if (href.startsWith('//')) {\n                warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n            }\n            else if (!matchedRoute.matched.length) {\n                warn(`No match found for location with path \"${rawLocation.path != null ? rawLocation.path : rawLocation}\"`);\n            }\n        }\n        return assign({\n            fullPath,\n            // keep the hash encoded so fullPath is effectively path + encodedQuery +\n            // hash\n            hash,\n            query: \n            // if the user is using a custom query lib like qs, we might have\n            // nested objects, so we keep the query as is, meaning it can contain\n            // numbers at `$route.query`, but at the point, the user will have to\n            // use their own type anyway.\n            // https://github.com/vuejs/router/issues/328#issuecomment-649481567\n            stringifyQuery$1 === stringifyQuery\n                ? normalizeQuery(rawLocation.query)\n                : (rawLocation.query || {}),\n        }, matchedRoute, {\n            redirectedFrom: undefined,\n            href,\n        });\n    }\n    function locationAsObject(to) {\n        return typeof to === 'string'\n            ? parseURL(parseQuery$1, to, currentRoute.value.path)\n            : assign({}, to);\n    }\n    function checkCanceledNavigation(to, from) {\n        if (pendingLocation !== to) {\n            return createRouterError(8 /* ErrorTypes.NAVIGATION_CANCELLED */, {\n                from,\n                to,\n            });\n        }\n    }\n    function push(to) {\n        return pushWithRedirect(to);\n    }\n    function replace(to) {\n        return push(assign(locationAsObject(to), { replace: true }));\n    }\n    function handleRedirectRecord(to) {\n        const lastMatched = to.matched[to.matched.length - 1];\n        if (lastMatched && lastMatched.redirect) {\n            const { redirect } = lastMatched;\n            let newTargetLocation = typeof redirect === 'function' ? redirect(to) : redirect;\n            if (typeof newTargetLocation === 'string') {\n                newTargetLocation =\n                    newTargetLocation.includes('?') || newTargetLocation.includes('#')\n                        ? (newTargetLocation = locationAsObject(newTargetLocation))\n                        : // force empty params\n                            { path: newTargetLocation };\n                // @ts-expect-error: force empty params when a string is passed to let\n                // the router parse them again\n                newTargetLocation.params = {};\n            }\n            if ((process.env.NODE_ENV !== 'production') &&\n                newTargetLocation.path == null &&\n                !('name' in newTargetLocation)) {\n                warn(`Invalid redirect found:\\n${JSON.stringify(newTargetLocation, null, 2)}\\n when navigating to \"${to.fullPath}\". A redirect must contain a name or path. This will break in production.`);\n                throw new Error('Invalid redirect');\n            }\n            return assign({\n                query: to.query,\n                hash: to.hash,\n                // avoid transferring params if the redirect has a path\n                params: newTargetLocation.path != null ? {} : to.params,\n            }, newTargetLocation);\n        }\n    }\n    function pushWithRedirect(to, redirectedFrom) {\n        const targetLocation = (pendingLocation = resolve(to));\n        const from = currentRoute.value;\n        const data = to.state;\n        const force = to.force;\n        // to could be a string where `replace` is a function\n        const replace = to.replace === true;\n        const shouldRedirect = handleRedirectRecord(targetLocation);\n        if (shouldRedirect)\n            return pushWithRedirect(assign(locationAsObject(shouldRedirect), {\n                state: typeof shouldRedirect === 'object'\n                    ? assign({}, data, shouldRedirect.state)\n                    : data,\n                force,\n                replace,\n            }), \n            // keep original redirectedFrom if it exists\n            redirectedFrom || targetLocation);\n        // if it was a redirect we already called `pushWithRedirect` above\n        const toLocation = targetLocation;\n        toLocation.redirectedFrom = redirectedFrom;\n        let failure;\n        if (!force && isSameRouteLocation(stringifyQuery$1, from, targetLocation)) {\n            failure = createRouterError(16 /* ErrorTypes.NAVIGATION_DUPLICATED */, { to: toLocation, from });\n            // trigger scroll to allow scrolling to the same anchor\n            handleScroll(from, from, \n            // this is a push, the only way for it to be triggered from a\n            // history.listen is with a redirect, which makes it become a push\n            true, \n            // This cannot be the first navigation because the initial location\n            // cannot be manually navigated to\n            false);\n        }\n        return (failure ? Promise.resolve(failure) : navigate(toLocation, from))\n            .catch((error) => isNavigationFailure(error)\n            ? // navigation redirects still mark the router as ready\n                isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)\n                    ? error\n                    : markAsReady(error) // also returns the error\n            : // reject any unknown error\n                triggerError(error, toLocation, from))\n            .then((failure) => {\n            if (failure) {\n                if (isNavigationFailure(failure, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n                    if ((process.env.NODE_ENV !== 'production') &&\n                        // we are redirecting to the same location we were already at\n                        isSameRouteLocation(stringifyQuery$1, resolve(failure.to), toLocation) &&\n                        // and we have done it a couple of times\n                        redirectedFrom &&\n                        // @ts-expect-error: added only in dev\n                        (redirectedFrom._count = redirectedFrom._count\n                            ? // @ts-expect-error\n                                redirectedFrom._count + 1\n                            : 1) > 30) {\n                        warn(`Detected a possibly infinite redirection in a navigation guard when going from \"${from.fullPath}\" to \"${toLocation.fullPath}\". Aborting to avoid a Stack Overflow.\\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`);\n                        return Promise.reject(new Error('Infinite redirect in navigation guard'));\n                    }\n                    return pushWithRedirect(\n                    // keep options\n                    assign({\n                        // preserve an existing replacement but allow the redirect to override it\n                        replace,\n                    }, locationAsObject(failure.to), {\n                        state: typeof failure.to === 'object'\n                            ? assign({}, data, failure.to.state)\n                            : data,\n                        force,\n                    }), \n                    // preserve the original redirectedFrom if any\n                    redirectedFrom || toLocation);\n                }\n            }\n            else {\n                // if we fail we don't finalize the navigation\n                failure = finalizeNavigation(toLocation, from, true, replace, data);\n            }\n            triggerAfterEach(toLocation, from, failure);\n            return failure;\n        });\n    }\n    /**\n     * Helper to reject and skip all navigation guards if a new navigation happened\n     * @param to\n     * @param from\n     */\n    function checkCanceledNavigationAndReject(to, from) {\n        const error = checkCanceledNavigation(to, from);\n        return error ? Promise.reject(error) : Promise.resolve();\n    }\n    function runWithContext(fn) {\n        const app = installedApps.values().next().value;\n        // support Vue < 3.3\n        return app && typeof app.runWithContext === 'function'\n            ? app.runWithContext(fn)\n            : fn();\n    }\n    // TODO: refactor the whole before guards by internally using router.beforeEach\n    function navigate(to, from) {\n        let guards;\n        const [leavingRecords, updatingRecords, enteringRecords] = extractChangingRecords(to, from);\n        // all components here have been resolved once because we are leaving\n        guards = extractComponentsGuards(leavingRecords.reverse(), 'beforeRouteLeave', to, from);\n        // leavingRecords is already reversed\n        for (const record of leavingRecords) {\n            record.leaveGuards.forEach(guard => {\n                guards.push(guardToPromiseFn(guard, to, from));\n            });\n        }\n        const canceledNavigationCheck = checkCanceledNavigationAndReject.bind(null, to, from);\n        guards.push(canceledNavigationCheck);\n        // run the queue of per route beforeRouteLeave guards\n        return (runGuardQueue(guards)\n            .then(() => {\n            // check global guards beforeEach\n            guards = [];\n            for (const guard of beforeGuards.list()) {\n                guards.push(guardToPromiseFn(guard, to, from));\n            }\n            guards.push(canceledNavigationCheck);\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check in components beforeRouteUpdate\n            guards = extractComponentsGuards(updatingRecords, 'beforeRouteUpdate', to, from);\n            for (const record of updatingRecords) {\n                record.updateGuards.forEach(guard => {\n                    guards.push(guardToPromiseFn(guard, to, from));\n                });\n            }\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check the route beforeEnter\n            guards = [];\n            for (const record of enteringRecords) {\n                // do not trigger beforeEnter on reused views\n                if (record.beforeEnter) {\n                    if (isArray(record.beforeEnter)) {\n                        for (const beforeEnter of record.beforeEnter)\n                            guards.push(guardToPromiseFn(beforeEnter, to, from));\n                    }\n                    else {\n                        guards.push(guardToPromiseFn(record.beforeEnter, to, from));\n                    }\n                }\n            }\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // NOTE: at this point to.matched is normalized and does not contain any () => Promise<Component>\n            // clear existing enterCallbacks, these are added by extractComponentsGuards\n            to.matched.forEach(record => (record.enterCallbacks = {}));\n            // check in-component beforeRouteEnter\n            guards = extractComponentsGuards(enteringRecords, 'beforeRouteEnter', to, from, runWithContext);\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check global guards beforeResolve\n            guards = [];\n            for (const guard of beforeResolveGuards.list()) {\n                guards.push(guardToPromiseFn(guard, to, from));\n            }\n            guards.push(canceledNavigationCheck);\n            return runGuardQueue(guards);\n        })\n            // catch any navigation canceled\n            .catch(err => isNavigationFailure(err, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)\n            ? err\n            : Promise.reject(err)));\n    }\n    function triggerAfterEach(to, from, failure) {\n        // navigation is confirmed, call afterGuards\n        // TODO: wrap with error handlers\n        afterGuards\n            .list()\n            .forEach(guard => runWithContext(() => guard(to, from, failure)));\n    }\n    /**\n     * - Cleans up any navigation guards\n     * - Changes the url if necessary\n     * - Calls the scrollBehavior\n     */\n    function finalizeNavigation(toLocation, from, isPush, replace, data) {\n        // a more recent navigation took place\n        const error = checkCanceledNavigation(toLocation, from);\n        if (error)\n            return error;\n        // only consider as push if it's not the first navigation\n        const isFirstNavigation = from === START_LOCATION_NORMALIZED;\n        const state = !isBrowser ? {} : history.state;\n        // change URL only if the user did a push/replace and if it's not the initial navigation because\n        // it's just reflecting the url\n        if (isPush) {\n            // on the initial navigation, we want to reuse the scroll position from\n            // history state if it exists\n            if (replace || isFirstNavigation)\n                routerHistory.replace(toLocation.fullPath, assign({\n                    scroll: isFirstNavigation && state && state.scroll,\n                }, data));\n            else\n                routerHistory.push(toLocation.fullPath, data);\n        }\n        // accept current navigation\n        currentRoute.value = toLocation;\n        handleScroll(toLocation, from, isPush, isFirstNavigation);\n        markAsReady();\n    }\n    let removeHistoryListener;\n    // attach listener to history to trigger navigations\n    function setupListeners() {\n        // avoid setting up listeners twice due to an invalid first navigation\n        if (removeHistoryListener)\n            return;\n        removeHistoryListener = routerHistory.listen((to, _from, info) => {\n            if (!router.listening)\n                return;\n            // cannot be a redirect route because it was in history\n            const toLocation = resolve(to);\n            // due to dynamic routing, and to hash history with manual navigation\n            // (manually changing the url or calling history.hash = '#/somewhere'),\n            // there could be a redirect record in history\n            const shouldRedirect = handleRedirectRecord(toLocation);\n            if (shouldRedirect) {\n                pushWithRedirect(assign(shouldRedirect, { replace: true, force: true }), toLocation).catch(noop);\n                return;\n            }\n            pendingLocation = toLocation;\n            const from = currentRoute.value;\n            // TODO: should be moved to web history?\n            if (isBrowser) {\n                saveScrollPosition(getScrollKey(from.fullPath, info.delta), computeScrollPosition());\n            }\n            navigate(toLocation, from)\n                .catch((error) => {\n                if (isNavigationFailure(error, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n                    return error;\n                }\n                if (isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n                    // Here we could call if (info.delta) routerHistory.go(-info.delta,\n                    // false) but this is bug prone as we have no way to wait the\n                    // navigation to be finished before calling pushWithRedirect. Using\n                    // a setTimeout of 16ms seems to work but there is no guarantee for\n                    // it to work on every browser. So instead we do not restore the\n                    // history entry and trigger a new navigation as requested by the\n                    // navigation guard.\n                    // the error is already handled by router.push we just want to avoid\n                    // logging the error\n                    pushWithRedirect(assign(locationAsObject(error.to), {\n                        force: true,\n                    }), toLocation\n                    // avoid an uncaught rejection, let push call triggerError\n                    )\n                        .then(failure => {\n                        // manual change in hash history #916 ending up in the URL not\n                        // changing, but it was changed by the manual url change, so we\n                        // need to manually change it ourselves\n                        if (isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ |\n                            16 /* ErrorTypes.NAVIGATION_DUPLICATED */) &&\n                            !info.delta &&\n                            info.type === NavigationType.pop) {\n                            routerHistory.go(-1, false);\n                        }\n                    })\n                        .catch(noop);\n                    // avoid the then branch\n                    return Promise.reject();\n                }\n                // do not restore history on unknown direction\n                if (info.delta) {\n                    routerHistory.go(-info.delta, false);\n                }\n                // unrecognized error, transfer to the global handler\n                return triggerError(error, toLocation, from);\n            })\n                .then((failure) => {\n                failure =\n                    failure ||\n                        finalizeNavigation(\n                        // after navigation, all matched components are resolved\n                        toLocation, from, false);\n                // revert the navigation\n                if (failure) {\n                    if (info.delta &&\n                        // a new navigation has been triggered, so we do not want to revert, that will change the current history\n                        // entry while a different route is displayed\n                        !isNavigationFailure(failure, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n                        routerHistory.go(-info.delta, false);\n                    }\n                    else if (info.type === NavigationType.pop &&\n                        isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 16 /* ErrorTypes.NAVIGATION_DUPLICATED */)) {\n                        // manual change in hash history #916\n                        // it's like a push but lacks the information of the direction\n                        routerHistory.go(-1, false);\n                    }\n                }\n                triggerAfterEach(toLocation, from, failure);\n            })\n                // avoid warnings in the console about uncaught rejections, they are logged by triggerErrors\n                .catch(noop);\n        });\n    }\n    // Initialization and Errors\n    let readyHandlers = useCallbacks();\n    let errorListeners = useCallbacks();\n    let ready;\n    /**\n     * Trigger errorListeners added via onError and throws the error as well\n     *\n     * @param error - error to throw\n     * @param to - location we were navigating to when the error happened\n     * @param from - location we were navigating from when the error happened\n     * @returns the error as a rejected promise\n     */\n    function triggerError(error, to, from) {\n        markAsReady(error);\n        const list = errorListeners.list();\n        if (list.length) {\n            list.forEach(handler => handler(error, to, from));\n        }\n        else {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn('uncaught error during route navigation:');\n            }\n            console.error(error);\n        }\n        // reject the error no matter there were error listeners or not\n        return Promise.reject(error);\n    }\n    function isReady() {\n        if (ready && currentRoute.value !== START_LOCATION_NORMALIZED)\n            return Promise.resolve();\n        return new Promise((resolve, reject) => {\n            readyHandlers.add([resolve, reject]);\n        });\n    }\n    function markAsReady(err) {\n        if (!ready) {\n            // still not ready if an error happened\n            ready = !err;\n            setupListeners();\n            readyHandlers\n                .list()\n                .forEach(([resolve, reject]) => (err ? reject(err) : resolve()));\n            readyHandlers.reset();\n        }\n        return err;\n    }\n    // Scroll behavior\n    function handleScroll(to, from, isPush, isFirstNavigation) {\n        const { scrollBehavior } = options;\n        if (!isBrowser || !scrollBehavior)\n            return Promise.resolve();\n        const scrollPosition = (!isPush && getSavedScrollPosition(getScrollKey(to.fullPath, 0))) ||\n            ((isFirstNavigation || !isPush) &&\n                history.state &&\n                history.state.scroll) ||\n            null;\n        return nextTick()\n            .then(() => scrollBehavior(to, from, scrollPosition))\n            .then(position => position && scrollToPosition(position))\n            .catch(err => triggerError(err, to, from));\n    }\n    const go = (delta) => routerHistory.go(delta);\n    let started;\n    const installedApps = new Set();\n    const router = {\n        currentRoute,\n        listening: true,\n        addRoute,\n        removeRoute,\n        clearRoutes: matcher.clearRoutes,\n        hasRoute,\n        getRoutes,\n        resolve,\n        options,\n        push,\n        replace,\n        go,\n        back: () => go(-1),\n        forward: () => go(1),\n        beforeEach: beforeGuards.add,\n        beforeResolve: beforeResolveGuards.add,\n        afterEach: afterGuards.add,\n        onError: errorListeners.add,\n        isReady,\n        install(app) {\n            const router = this;\n            app.component('RouterLink', RouterLink);\n            app.component('RouterView', RouterView);\n            app.config.globalProperties.$router = router;\n            Object.defineProperty(app.config.globalProperties, '$route', {\n                enumerable: true,\n                get: () => unref(currentRoute),\n            });\n            // this initial navigation is only necessary on client, on server it doesn't\n            // make sense because it will create an extra unnecessary navigation and could\n            // lead to problems\n            if (isBrowser &&\n                // used for the initial navigation client side to avoid pushing\n                // multiple times when the router is used in multiple apps\n                !started &&\n                currentRoute.value === START_LOCATION_NORMALIZED) {\n                // see above\n                started = true;\n                push(routerHistory.location).catch(err => {\n                    if ((process.env.NODE_ENV !== 'production'))\n                        warn('Unexpected error when starting the router:', err);\n                });\n            }\n            const reactiveRoute = {};\n            for (const key in START_LOCATION_NORMALIZED) {\n                Object.defineProperty(reactiveRoute, key, {\n                    get: () => currentRoute.value[key],\n                    enumerable: true,\n                });\n            }\n            app.provide(routerKey, router);\n            app.provide(routeLocationKey, shallowReactive(reactiveRoute));\n            app.provide(routerViewLocationKey, currentRoute);\n            const unmountApp = app.unmount;\n            installedApps.add(app);\n            app.unmount = function () {\n                installedApps.delete(app);\n                // the router is not attached to an app anymore\n                if (installedApps.size < 1) {\n                    // invalidate the current navigation\n                    pendingLocation = START_LOCATION_NORMALIZED;\n                    removeHistoryListener && removeHistoryListener();\n                    removeHistoryListener = null;\n                    currentRoute.value = START_LOCATION_NORMALIZED;\n                    started = false;\n                    ready = false;\n                }\n                unmountApp();\n            };\n            // TODO: this probably needs to be updated so it can be used by vue-termui\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n                addDevtools(app, router, matcher);\n            }\n        },\n    };\n    // TODO: type this as NavigationGuardReturn or similar instead of any\n    function runGuardQueue(guards) {\n        return guards.reduce((promise, guard) => promise.then(() => runWithContext(guard)), Promise.resolve());\n    }\n    return router;\n}\nfunction extractChangingRecords(to, from) {\n    const leavingRecords = [];\n    const updatingRecords = [];\n    const enteringRecords = [];\n    const len = Math.max(from.matched.length, to.matched.length);\n    for (let i = 0; i < len; i++) {\n        const recordFrom = from.matched[i];\n        if (recordFrom) {\n            if (to.matched.find(record => isSameRouteRecord(record, recordFrom)))\n                updatingRecords.push(recordFrom);\n            else\n                leavingRecords.push(recordFrom);\n        }\n        const recordTo = to.matched[i];\n        if (recordTo) {\n            // the type doesn't matter because we are comparing per reference\n            if (!from.matched.find(record => isSameRouteRecord(record, recordTo))) {\n                enteringRecords.push(recordTo);\n            }\n        }\n    }\n    return [leavingRecords, updatingRecords, enteringRecords];\n}\n\n/**\n * Returns the router instance. Equivalent to using `$router` inside\n * templates.\n */\nfunction useRouter() {\n    return inject(routerKey);\n}\n/**\n * Returns the current route location. Equivalent to using `$route` inside\n * templates.\n */\nfunction useRoute(_name) {\n    return inject(routeLocationKey);\n}\n\nexport { NavigationFailureType, RouterLink, RouterView, START_LOCATION_NORMALIZED as START_LOCATION, createMemoryHistory, createRouter, createRouterMatcher, createWebHashHistory, createWebHistory, isNavigationFailure, loadRouteLocation, matchedRouteKey, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, routeLocationKey, routerKey, routerViewLocationKey, stringifyQuery, useLink, useRoute, useRouter, viewDepthKey };\n"], "x_google_ignoreList": [0, 1, 2, 3, 4], "mappings": ";;;AAAA,SAAgB,wBAAwB;AACpC,QAAO,WAAW,CAAC;AACtB;AACD,SAAgB,YAAY;AAExB,QAAQ,OAAO,cAAc,eAAe,OAAO,WAAW,cACxD,SACA,OAAO,eAAe,cAClB,aACA,CAAE;AACf;AACD,MAAa,mBAAmB,OAAO,UAAU;;;;ACXjD,MAAa,aAAa;AAC1B,MAAa,2BAA2B;;;;ACDxC,IAAI;AACJ,IAAI;AACJ,SAAgB,yBAAyB;CACrC,IAAI;AACJ,KAAI,cAAc,OACd,QAAO;AAEX,KAAI,OAAO,WAAW,eAAe,OAAO,aAAa;EACrD,YAAY;EACZ,OAAO,OAAO;CACjB,WACQ,OAAO,eAAe,iBAAiB,KAAK,WAAW,gBAAgB,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,cAAc;EAC9H,YAAY;EACZ,OAAO,WAAW,WAAW;CAChC,OAEG,YAAY;AAEhB,QAAO;AACV;AACD,SAAgB,MAAM;AAClB,QAAO,wBAAwB,GAAG,KAAK,KAAK,GAAG,KAAK,KAAK;AAC5D;;;;ACpBD,IAAa,WAAb,MAAsB;CAClB,YAAY,QAAQ,MAAM;EACtB,KAAK,SAAS;EACd,KAAK,cAAc,CAAE;EACrB,KAAK,UAAU,CAAE;EACjB,KAAK,SAAS;EACd,KAAK,OAAO;EACZ,MAAM,kBAAkB,CAAE;AAC1B,MAAI,OAAO,SACP,MAAK,MAAM,MAAM,OAAO,UAAU;GAC9B,MAAM,OAAO,OAAO,SAAS;GAC7B,gBAAgB,MAAM,KAAK;EAC9B;EAEL,MAAM,sBAAsB,CAAC,gCAAgC,EAAE,OAAO,IAAI;EAC1E,IAAI,kBAAkB,OAAO,OAAO,CAAE,GAAE,gBAAgB;AACxD,MAAI;GACA,MAAM,MAAM,aAAa,QAAQ,oBAAoB;GACrD,MAAM,OAAO,KAAK,MAAM,IAAI;GAC5B,OAAO,OAAO,iBAAiB,KAAK;EACvC,SACM,GAAG,CAET;EACD,KAAK,YAAY;GACb,cAAc;AACV,WAAO;GACV;GACD,YAAY,OAAO;AACf,QAAI;KACA,aAAa,QAAQ,qBAAqB,KAAK,UAAU,MAAM,CAAC;IACnE,SACM,GAAG,CAET;IACD,kBAAkB;GACrB;GACD,MAAM;AACF,WAAO,KAAK;GACf;EACJ;AACD,MAAI,MACA,KAAK,GAAG,0BAA0B,CAAC,UAAU,UAAU;AACnD,OAAI,aAAa,KAAK,OAAO,IACzB,KAAK,UAAU,YAAY,MAAM;EAExC,EAAC;EAEN,KAAK,YAAY,IAAI,MAAM,CAAE,GAAE,EAC3B,KAAK,CAAC,SAAS,SAAS;AACpB,OAAI,KAAK,OACL,QAAO,KAAK,OAAO,GAAG;OAGtB,QAAO,CAAC,GAAG,SAAS;IAChB,KAAK,QAAQ,KAAK;KACd,QAAQ;KACR;IACH,EAAC;GACL;EAER,EACJ;EACD,KAAK,gBAAgB,IAAI,MAAM,CAAE,GAAE,EAC/B,KAAK,CAAC,SAAS,SAAS;AACpB,OAAI,KAAK,OACL,QAAO,KAAK,OAAO;YAEd,SAAS,KACd,QAAO,KAAK;YAEP,OAAO,KAAK,KAAK,UAAU,CAAC,SAAS,KAAK,CAC/C,QAAO,CAAC,GAAG,SAAS;IAChB,KAAK,YAAY,KAAK;KAClB,QAAQ;KACR;KACA,SAAS,MAAM,CAAG;IACrB,EAAC;AACF,WAAO,KAAK,UAAU,MAAM,GAAG,KAAK;GACvC;OAGD,QAAO,CAAC,GAAG,SAAS;AAChB,WAAO,IAAI,QAAQ,CAAC,YAAY;KAC5B,KAAK,YAAY,KAAK;MAClB,QAAQ;MACR;MACA;KACH,EAAC;IACL;GACJ;EAER,EACJ;CACJ;CACD,MAAM,cAAc,QAAQ;EACxB,KAAK,SAAS;AACd,OAAK,MAAM,QAAQ,KAAK,SACpB,KAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,KAAK,KAAK;AAE7C,OAAK,MAAM,QAAQ,KAAK,aACpB,KAAK,QAAQ,MAAM,KAAK,OAAO,KAAK,QAAQ,GAAG,KAAK,KAAK,CAAC;CAEjE;AACJ;;;;ACpGD,SAAgB,oBAAoB,kBAAkB,SAAS;CAC3D,MAAM,aAAa;CACnB,MAAM,SAAS,WAAW;CAC1B,MAAM,OAAO,uBAAuB;CACpC,MAAM,cAAc,oBAAoB,WAAW;AACnD,KAAI,SAAS,OAAO,yCAAyC,CAAC,cAC1D,KAAK,KAAK,YAAY,kBAAkB,QAAQ;MAE/C;EACD,MAAM,QAAQ,cAAc,IAAI,SAAS,YAAY,QAAQ;EAC7D,MAAM,OAAO,OAAO,2BAA2B,OAAO,4BAA4B,CAAE;EACpF,KAAK,KAAK;GACN,kBAAkB;GAClB;GACA;EACH,EAAC;AACF,MAAI,OACA,QAAQ,MAAM,cAAc;CAEnC;AACJ;;;;AClBD,MAAM,YAAY,OAAO,aAAa;;;;;;;AAQtC,SAAS,iBAAiB,WAAW;AACjC,QAAQ,OAAO,cAAc,YACzB,iBAAiB,aACjB,WAAW,aACX,eAAe;AACtB;AACD,SAAS,WAAW,KAAK;AACrB,QAAQ,IAAI,cACR,IAAI,OAAO,iBAAiB,YAG3B,IAAI,WAAW,iBAAiB,IAAI,QAAQ;AACpD;AACD,MAAM,SAAS,OAAO;AACtB,SAAS,cAAc,IAAI,QAAQ;CAC/B,MAAM,YAAY,CAAE;AACpB,MAAK,MAAM,OAAO,QAAQ;EACtB,MAAM,QAAQ,OAAO;EACrB,UAAU,OAAO,QAAQ,MAAM,GACzB,MAAM,IAAI,GAAG,GACb,GAAG,MAAM;CAClB;AACD,QAAO;AACV;AACD,MAAM,OAAO,MAAM,CAAG;;;;;AAKtB,MAAM,UAAU,MAAM;AAEtB,SAAS,KAAK,KAAK;CAEf,MAAM,OAAO,MAAM,KAAK,UAAU,CAAC,MAAM,EAAE;CAC3C,QAAQ,KAAK,MAAM,SAAS,CAAC,wBAAwB,GAAI,EAAC,OAAO,KAAK,CAAC;AAC1E;;;;;;;;;;;;;;;;;;AAqBD,MAAM,UAAU;AAChB,MAAM,eAAe;AACrB,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,QAAQ;AACd,MAAM,UAAU;;;;;;;;;;;;;;;AAehB,MAAM,sBAAsB;AAC5B,MAAM,uBAAuB;AAC7B,MAAM,eAAe;AACrB,MAAM,kBAAkB;AACxB,MAAM,oBAAoB;AAC1B,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAC3B,MAAM,eAAe;;;;;;;;;AASrB,SAAS,aAAa,MAAM;AACxB,QAAO,UAAU,KAAK,KAAK,CACtB,QAAQ,aAAa,IAAI,CACzB,QAAQ,qBAAqB,IAAI,CACjC,QAAQ,sBAAsB,IAAI;AAC1C;;;;;;;AAOD,SAAS,WAAW,MAAM;AACtB,QAAO,aAAa,KAAK,CACpB,QAAQ,mBAAmB,IAAI,CAC/B,QAAQ,oBAAoB,IAAI,CAChC,QAAQ,cAAc,IAAI;AAClC;;;;;;;;AAQD,SAAS,iBAAiB,MAAM;AAC5B,QAAQ,aAAa,KAAK,CAErB,QAAQ,SAAS,MAAM,CACvB,QAAQ,cAAc,IAAI,CAC1B,QAAQ,SAAS,MAAM,CACvB,QAAQ,cAAc,MAAM,CAC5B,QAAQ,iBAAiB,IAAI,CAC7B,QAAQ,mBAAmB,IAAI,CAC/B,QAAQ,oBAAoB,IAAI,CAChC,QAAQ,cAAc,IAAI;AAClC;;;;;;AAMD,SAAS,eAAe,MAAM;AAC1B,QAAO,iBAAiB,KAAK,CAAC,QAAQ,UAAU,MAAM;AACzD;;;;;;;AAOD,SAAS,WAAW,MAAM;AACtB,QAAO,aAAa,KAAK,CAAC,QAAQ,SAAS,MAAM,CAAC,QAAQ,OAAO,MAAM;AAC1E;;;;;;;;;;AAUD,SAAS,YAAY,MAAM;AACvB,QAAO,QAAQ,OAAO,KAAK,WAAW,KAAK,CAAC,QAAQ,UAAU,MAAM;AACvE;;;;;;;;AAQD,SAAS,OAAO,MAAM;AAClB,KAAI;AACA,SAAO,mBAAmB,KAAK,KAAK;CACvC,SACM,KAAK;EACmC,KAAK,CAAC,gBAAgB,EAAE,KAAK,uBAAuB,CAAC,CAAC;CACpG;AACD,QAAO,KAAK;AACf;AAED,MAAM,oBAAoB;AAC1B,MAAM,sBAAsB,CAAC,SAAS,KAAK,QAAQ,mBAAmB,GAAG;;;;;;;;;;AAUzE,SAAS,SAASA,cAAYC,YAAU,kBAAkB,KAAK;CAC3D,IAAI,MAAM,QAAQ,CAAE,GAAE,eAAe,IAAI,OAAO;CAGhD,MAAM,UAAUA,WAAS,QAAQ,IAAI;CACrC,IAAI,YAAYA,WAAS,QAAQ,IAAI;AAErC,KAAI,UAAU,aAAa,WAAW,GAClC,YAAY;AAEhB,KAAI,YAAY,IAAI;EAChB,OAAOA,WAAS,MAAM,GAAG,UAAU;EACnC,eAAeA,WAAS,MAAM,YAAY,GAAG,UAAU,KAAK,UAAUA,WAAS,OAAO;EACtF,QAAQD,aAAW,aAAa;CACnC;AACD,KAAI,UAAU,IAAI;EACd,OAAO,QAAQC,WAAS,MAAM,GAAG,QAAQ;EAEzC,OAAOA,WAAS,MAAM,SAASA,WAAS,OAAO;CAClD;CAED,OAAO,oBAAoB,QAAQ,OAAO,OAAOA,YAAU,gBAAgB;AAE3E,QAAO;EACH,UAAU,QAAQ,gBAAgB,OAAO,eAAe;EACxD;EACA;EACA,MAAM,OAAO,KAAK;CACrB;AACJ;;;;;;;AAOD,SAAS,aAAaC,kBAAgBD,YAAU;CAC5C,MAAM,QAAQA,WAAS,QAAQC,iBAAeD,WAAS,MAAM,GAAG;AAChE,QAAOA,WAAS,QAAQ,SAAS,OAAO,SAASA,WAAS,QAAQ;AACrE;;;;;;;AAOD,SAAS,UAAU,UAAU,MAAM;AAE/B,KAAI,CAAC,QAAQ,CAAC,SAAS,aAAa,CAAC,WAAW,KAAK,aAAa,CAAC,CAC/D,QAAO;AACX,QAAO,SAAS,MAAM,KAAK,OAAO,IAAI;AACzC;;;;;;;;;;AAUD,SAAS,oBAAoBC,kBAAgB,GAAG,GAAG;CAC/C,MAAM,aAAa,EAAE,QAAQ,SAAS;CACtC,MAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,QAAQ,aAAa,MACjB,eAAe,cACf,kBAAkB,EAAE,QAAQ,aAAa,EAAE,QAAQ,YAAY,IAC/D,0BAA0B,EAAE,QAAQ,EAAE,OAAO,IAC7CA,iBAAe,EAAE,MAAM,KAAKA,iBAAe,EAAE,MAAM,IACnD,EAAE,SAAS,EAAE;AACpB;;;;;;;;AAQD,SAAS,kBAAkB,GAAG,GAAG;AAI7B,SAAQ,EAAE,WAAW,QAAQ,EAAE,WAAW;AAC7C;AACD,SAAS,0BAA0B,GAAG,GAAG;AACrC,KAAI,OAAO,KAAK,EAAE,CAAC,WAAW,OAAO,KAAK,EAAE,CAAC,OACzC,QAAO;AACX,MAAK,MAAM,OAAO,EACd,KAAI,CAAC,+BAA+B,EAAE,MAAM,EAAE,KAAK,CAC/C,QAAO;AAEf,QAAO;AACV;AACD,SAAS,+BAA+B,GAAG,GAAG;AAC1C,QAAO,QAAQ,EAAE,GACX,kBAAkB,GAAG,EAAE,GACvB,QAAQ,EAAE,GACN,kBAAkB,GAAG,EAAE,GACvB,MAAM;AACnB;;;;;;;;AAQD,SAAS,kBAAkB,GAAG,GAAG;AAC7B,QAAO,QAAQ,EAAE,GACX,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,MAAM,UAAU,EAAE,GAAG,GAC9D,EAAE,WAAW,KAAK,EAAE,OAAO;AACpC;;;;;;;AAOD,SAAS,oBAAoB,IAAI,MAAM;AACnC,KAAI,GAAG,WAAW,IAAI,CAClB,QAAO;AACX,KAA+C,CAAC,KAAK,WAAW,IAAI,EAAE;EAClE,KAAK,CAAC,gFAAgF,EAAE,GAAG,QAAQ,EAAE,KAAK,yBAAyB,EAAE,KAAK,EAAE,CAAC,CAAC;AAC9I,SAAO;CACV;AACD,KAAI,CAAC,GACD,QAAO;CACX,MAAM,eAAe,KAAK,MAAM,IAAI;CACpC,MAAM,aAAa,GAAG,MAAM,IAAI;CAChC,MAAM,gBAAgB,WAAW,WAAW,SAAS;AAGrD,KAAI,kBAAkB,QAAQ,kBAAkB,KAC5C,WAAW,KAAK,GAAG;CAEvB,IAAI,WAAW,aAAa,SAAS;CACrC,IAAI;CACJ,IAAI;AACJ,MAAK,aAAa,GAAG,aAAa,WAAW,QAAQ,cAAc;EAC/D,UAAU,WAAW;AAErB,MAAI,YAAY,IACZ;AAEJ,MAAI,YAAY,MAEZ;OAAI,WAAW,GACX;EAAW,MAKf;CACP;AACD,QAAQ,aAAa,MAAM,GAAG,SAAS,CAAC,KAAK,IAAI,GAC7C,MACA,WAAW,MAAM,WAAW,CAAC,KAAK,IAAI;AAC7C;;;;;;;;;;;;;;;;AAgBD,MAAM,4BAA4B;CAC9B,MAAM;CAEN,MAAM;CACN,QAAQ,CAAE;CACV,OAAO,CAAE;CACT,MAAM;CACN,UAAU;CACV,SAAS,CAAE;CACX,MAAM,CAAE;CACR,gBAAgB;AACnB;AAED,IAAI;CACH,SAAUC,kBAAgB;CACvBA,iBAAe,SAAS;CACxBA,iBAAe,UAAU;AAC5B,GAAE,mBAAmB,iBAAiB,CAAE,GAAE;AAC3C,IAAI;CACH,SAAUC,uBAAqB;CAC5BA,sBAAoB,UAAU;CAC9BA,sBAAoB,aAAa;CACjCA,sBAAoB,aAAa;AACpC,GAAE,wBAAwB,sBAAsB,CAAE,GAAE;;;;AAIrD,MAAM,QAAQ;;;;;;;AAQd,SAAS,cAAc,MAAM;AACzB,KAAI,CAAC,KACD,KAAI,WAAW;EAEX,MAAM,SAAS,SAAS,cAAc,OAAO;EAC7C,OAAQ,UAAU,OAAO,aAAa,OAAO,IAAK;EAElD,OAAO,KAAK,QAAQ,mBAAmB,GAAG;CAC7C,OAEG,OAAO;AAMf,KAAI,KAAK,OAAO,OAAO,KAAK,OAAO,KAC/B,OAAO,MAAM;AAGjB,QAAO,oBAAoB,KAAK;AACnC;AAED,MAAM,iBAAiB;AACvB,SAAS,WAAW,MAAMH,YAAU;AAChC,QAAO,KAAK,QAAQ,gBAAgB,IAAI,GAAGA;AAC9C;AAED,SAAS,mBAAmB,IAAI,QAAQ;CACpC,MAAM,UAAU,SAAS,gBAAgB,uBAAuB;CAChE,MAAM,SAAS,GAAG,uBAAuB;AACzC,QAAO;EACH,UAAU,OAAO;EACjB,MAAM,OAAO,OAAO,QAAQ,QAAQ,OAAO,QAAQ;EACnD,KAAK,OAAO,MAAM,QAAQ,OAAO,OAAO,OAAO;CAClD;AACJ;AACD,MAAM,wBAAwB,OAAO;CACjC,MAAM,OAAO;CACb,KAAK,OAAO;AACf;AACD,SAAS,iBAAiB,UAAU;CAChC,IAAI;AACJ,KAAI,QAAQ,UAAU;EAClB,MAAM,aAAa,SAAS;EAC5B,MAAM,eAAe,OAAO,eAAe,YAAY,WAAW,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;AAsBjF,MAA+C,OAAO,SAAS,OAAO,UAClE;OAAI,CAAC,gBAAgB,CAAC,SAAS,eAAe,SAAS,GAAG,MAAM,EAAE,CAAC,CAC/D,KAAI;IACA,MAAM,UAAU,SAAS,cAAc,SAAS,GAAG;AACnD,QAAI,gBAAgB,SAAS;KACzB,KAAK,CAAC,cAAc,EAAE,SAAS,GAAG,mDAAmD,EAAE,SAAS,GAAG,+BAA+B,CAAC,CAAC;AAEpI;IACH;GACJ,SACM,KAAK;IACR,KAAK,CAAC,cAAc,EAAE,SAAS,GAAG,0QAA0Q,CAAC,CAAC;AAE9S;GACH;EACJ;EAEL,MAAM,KAAK,OAAO,eAAe,WAC3B,eACI,SAAS,eAAe,WAAW,MAAM,EAAE,CAAC,GAC5C,SAAS,cAAc,WAAW,GACtC;AACN,MAAI,CAAC,IAAI;GAED,KAAK,CAAC,sCAAsC,EAAE,SAAS,GAAG,6BAA6B,CAAC,CAAC;AAC7F;EACH;EACD,kBAAkB,mBAAmB,IAAI,SAAS;CACrD,OAEG,kBAAkB;AAEtB,KAAI,oBAAoB,SAAS,gBAAgB,OAC7C,OAAO,SAAS,gBAAgB;MAEhC,OAAO,SAAS,gBAAgB,QAAQ,OAAO,gBAAgB,OAAO,OAAO,SAAS,gBAAgB,OAAO,OAAO,gBAAgB,MAAM,OAAO,QAAQ;AAEhK;AACD,SAAS,aAAa,MAAM,OAAO;CAC/B,MAAM,WAAW,QAAQ,QAAQ,QAAQ,MAAM,WAAW,QAAQ;AAClE,QAAO,WAAW;AACrB;AACD,MAAM,kCAAkB,IAAI;AAC5B,SAAS,mBAAmB,KAAK,gBAAgB;CAC7C,gBAAgB,IAAI,KAAK,eAAe;AAC3C;AACD,SAAS,uBAAuB,KAAK;CACjC,MAAM,SAAS,gBAAgB,IAAI,IAAI;CAEvC,gBAAgB,OAAO,IAAI;AAC3B,QAAO;AACV;;;;;AAiBD,IAAI,qBAAqB,MAAM,SAAS,WAAW,OAAO,SAAS;;;;;;AAMnE,SAAS,sBAAsB,MAAMA,YAAU;CAC3C,MAAM,EAAE,UAAU,QAAQ,MAAM,GAAGA;CAEnC,MAAM,UAAU,KAAK,QAAQ,IAAI;AACjC,KAAI,UAAU,IAAI;EACd,IAAI,WAAW,KAAK,SAAS,KAAK,MAAM,QAAQ,CAAC,GAC3C,KAAK,MAAM,QAAQ,CAAC,SACpB;EACN,IAAI,eAAe,KAAK,MAAM,SAAS;AAEvC,MAAI,aAAa,OAAO,KACpB,eAAe,MAAM;AACzB,SAAO,UAAU,cAAc,GAAG;CACrC;CACD,MAAM,OAAO,UAAU,UAAU,KAAK;AACtC,QAAO,OAAO,SAAS;AAC1B;AACD,SAAS,oBAAoB,MAAM,cAAc,iBAAiB,SAAS;CACvE,IAAI,YAAY,CAAE;CAClB,IAAI,YAAY,CAAE;CAGlB,IAAI,aAAa;CACjB,MAAM,kBAAkB,CAAC,EAAE,OAAQ,KAAK;EACpC,MAAM,KAAK,sBAAsB,MAAM,SAAS;EAChD,MAAM,OAAO,gBAAgB;EAC7B,MAAM,YAAY,aAAa;EAC/B,IAAI,QAAQ;AACZ,MAAI,OAAO;GACP,gBAAgB,QAAQ;GACxB,aAAa,QAAQ;AAErB,OAAI,cAAc,eAAe,MAAM;IACnC,aAAa;AACb;GACH;GACD,QAAQ,YAAY,MAAM,WAAW,UAAU,WAAW;EAC7D,OAEG,QAAQ,GAAG;EAOf,UAAU,QAAQ,cAAY;GAC1B,SAAS,gBAAgB,OAAO,MAAM;IAClC;IACA,MAAM,eAAe;IACrB,WAAW,QACL,QAAQ,IACJ,oBAAoB,UACpB,oBAAoB,OACxB,oBAAoB;GAC7B,EAAC;EACL,EAAC;CACL;CACD,SAAS,iBAAiB;EACtB,aAAa,gBAAgB;CAChC;CACD,SAAS,OAAO,UAAU;EAEtB,UAAU,KAAK,SAAS;EACxB,MAAM,WAAW,MAAM;GACnB,MAAM,QAAQ,UAAU,QAAQ,SAAS;AACzC,OAAI,QAAQ,IACR,UAAU,OAAO,OAAO,EAAE;EACjC;EACD,UAAU,KAAK,SAAS;AACxB,SAAO;CACV;CACD,SAAS,uBAAuB;EAC5B,MAAM,EAAE,oBAAS,GAAG;AACpB,MAAI,CAACI,UAAQ,MACT;EACJA,UAAQ,aAAa,OAAO,CAAE,GAAEA,UAAQ,OAAO,EAAE,QAAQ,uBAAuB,CAAE,EAAC,EAAE,GAAG;CAC3F;CACD,SAAS,UAAU;AACf,OAAK,MAAM,YAAY,WACnB,UAAU;EACd,YAAY,CAAE;EACd,OAAO,oBAAoB,YAAY,gBAAgB;EACvD,OAAO,oBAAoB,gBAAgB,qBAAqB;CACnE;CAED,OAAO,iBAAiB,YAAY,gBAAgB;CAGpD,OAAO,iBAAiB,gBAAgB,sBAAsB,EAC1D,SAAS,KACZ,EAAC;AACF,QAAO;EACH;EACA;EACA;CACH;AACJ;;;;AAID,SAAS,WAAW,MAAM,SAAS,SAAS,WAAW,OAAO,gBAAgB,OAAO;AACjF,QAAO;EACH;EACA;EACA;EACA;EACA,UAAU,OAAO,QAAQ;EACzB,QAAQ,gBAAgB,uBAAuB,GAAG;CACrD;AACJ;AACD,SAAS,0BAA0B,MAAM;CACrC,MAAM,EAAE,oBAAS,sBAAU,GAAG;CAE9B,MAAM,kBAAkB,EACpB,OAAO,sBAAsB,MAAMJ,WAAS,CAC/C;CACD,MAAM,eAAe,EAAE,OAAOI,UAAQ,MAAO;AAE7C,KAAI,CAAC,aAAa,OACd,eAAe,gBAAgB,OAAO;EAClC,MAAM;EACN,SAAS,gBAAgB;EACzB,SAAS;EAET,UAAUA,UAAQ,SAAS;EAC3B,UAAU;EAGV,QAAQ;CACX,GAAE,KAAK;CAEZ,SAAS,eAAe,IAAI,OAAOC,WAAS;;;;;;;;;;EAUxC,MAAM,YAAY,KAAK,QAAQ,IAAI;EACnC,MAAM,MAAM,YAAY,MACjBL,WAAS,QAAQ,SAAS,cAAc,OAAO,GAC5C,OACA,KAAK,MAAM,UAAU,IAAI,KAC7B,oBAAoB,GAAG,OAAO;AACpC,MAAI;GAGAI,UAAQC,YAAU,iBAAiB,aAAa,OAAO,IAAI,IAAI;GAC/D,aAAa,QAAQ;EACxB,SACM,KAAK;GAEJ,KAAK,iCAAiC,IAAI;GAM9CL,WAASK,YAAU,YAAY,UAAU,IAAI;EAChD;CACJ;CACD,SAAS,QAAQ,IAAI,MAAM;EACvB,MAAM,QAAQ,OAAO,CAAE,GAAED,UAAQ,OAAO,WAAW,aAAa,MAAM,MAEtE,IAAI,aAAa,MAAM,SAAS,KAAK,EAAE,MAAM,EAAE,UAAU,aAAa,MAAM,SAAU,EAAC;EACvF,eAAe,IAAI,OAAO,KAAK;EAC/B,gBAAgB,QAAQ;CAC3B;CACD,SAAS,KAAK,IAAI,MAAM;EAGpB,MAAM,eAAe,OAAO,CAAE,GAI9B,aAAa,OAAOA,UAAQ,OAAO;GAC/B,SAAS;GACT,QAAQ,uBAAuB;EAClC,EAAC;AACF,MAA+C,CAACA,UAAQ,OACpD,KAAK,kVAEmG;EAE5G,eAAe,aAAa,SAAS,cAAc,KAAK;EACxD,MAAM,QAAQ,OAAO,CAAE,GAAE,WAAW,gBAAgB,OAAO,IAAI,KAAK,EAAE,EAAE,UAAU,aAAa,WAAW,EAAG,GAAE,KAAK;EACpH,eAAe,IAAI,OAAO,MAAM;EAChC,gBAAgB,QAAQ;CAC3B;AACD,QAAO;EACH,UAAU;EACV,OAAO;EACP;EACA;CACH;AACJ;;;;;;AAMD,SAAS,iBAAiB,MAAM;CAC5B,OAAO,cAAc,KAAK;CAC1B,MAAM,oBAAoB,0BAA0B,KAAK;CACzD,MAAM,mBAAmB,oBAAoB,MAAM,kBAAkB,OAAO,kBAAkB,UAAU,kBAAkB,QAAQ;CAClI,SAAS,GAAG,OAAO,mBAAmB,MAAM;AACxC,MAAI,CAAC,kBACD,iBAAiB,gBAAgB;EACrC,QAAQ,GAAG,MAAM;CACpB;CACD,MAAM,gBAAgB,OAAO;EAEzB,UAAU;EACV;EACA;EACA,YAAY,WAAW,KAAK,MAAM,KAAK;CAC1C,GAAE,mBAAmB,iBAAiB;CACvC,OAAO,eAAe,eAAe,YAAY;EAC7C,YAAY;EACZ,KAAK,MAAM,kBAAkB,SAAS;CACzC,EAAC;CACF,OAAO,eAAe,eAAe,SAAS;EAC1C,YAAY;EACZ,KAAK,MAAM,kBAAkB,MAAM;CACtC,EAAC;AACF,QAAO;AACV;;;;;;;;AASD,SAAS,oBAAoB,OAAO,IAAI;CACpC,IAAI,YAAY,CAAE;CAClB,IAAI,QAAQ,CAAC,CAAC,OAAO,CAAE,CAAC,CAAC;CACzB,IAAI,WAAW;CACf,OAAO,cAAc,KAAK;CAC1B,SAAS,YAAYJ,YAAU,QAAQ,CAAE,GAAE;EACvC;AACA,MAAI,aAAa,MAAM,QAEnB,MAAM,OAAO,SAAS;EAE1B,MAAM,KAAK,CAACA,YAAU,KAAM,EAAC;CAChC;CACD,SAAS,iBAAiB,IAAI,MAAM,EAAE,WAAW,OAAO,EAAE;EACtD,MAAM,OAAO;GACT;GACA;GACA,MAAM,eAAe;EACxB;AACD,OAAK,MAAM,YAAY,WACnB,SAAS,IAAI,MAAM,KAAK;CAE/B;CACD,MAAM,gBAAgB;EAElB,UAAU;EAEV,OAAO,CAAE;EACT;EACA,YAAY,WAAW,KAAK,MAAM,KAAK;EACvC,QAAQ,IAAI,OAAO;GAEf,MAAM,OAAO,YAAY,EAAE;GAC3B,YAAY,IAAI,MAAM;EACzB;EACD,KAAK,IAAI,OAAO;GACZ,YAAY,IAAI,MAAM;EACzB;EACD,OAAO,UAAU;GACb,UAAU,KAAK,SAAS;AACxB,UAAO,MAAM;IACT,MAAM,QAAQ,UAAU,QAAQ,SAAS;AACzC,QAAI,QAAQ,IACR,UAAU,OAAO,OAAO,EAAE;GACjC;EACJ;EACD,UAAU;GACN,YAAY,CAAE;GACd,QAAQ,CAAC,CAAC,OAAO,CAAE,CAAC,CAAC;GACrB,WAAW;EACd;EACD,GAAG,OAAO,gBAAgB,MAAM;GAC5B,MAAM,OAAO,KAAK;GAClB,MAAM,YAIN,QAAQ,IAAI,oBAAoB,OAAO,oBAAoB;GAC3D,WAAW,KAAK,IAAI,GAAG,KAAK,IAAI,WAAW,OAAO,MAAM,SAAS,EAAE,CAAC;AACpE,OAAI,eACA,iBAAiB,KAAK,UAAU,MAAM;IAClC;IACA;GACH,EAAC;EAET;CACJ;CACD,OAAO,eAAe,eAAe,YAAY;EAC7C,YAAY;EACZ,KAAK,MAAM,MAAM,UAAU;CAC9B,EAAC;CACF,OAAO,eAAe,eAAe,SAAS;EAC1C,YAAY;EACZ,KAAK,MAAM,MAAM,UAAU;CAC9B,EAAC;AACF,QAAO;AACV;;;;;;;;;;;;;;;;;;;;;;;;;AA0BD,SAAS,qBAAqB,MAAM;CAIhC,OAAO,SAAS,OAAO,QAAQ,SAAS,WAAW,SAAS,SAAS;AAErE,KAAI,CAAC,KAAK,SAAS,IAAI,EACnB,QAAQ;AACZ,KAA+C,CAAC,KAAK,SAAS,KAAK,IAAI,CAAC,KAAK,SAAS,IAAI,EACtF,KAAK,CAAC,mCAAmC,EAAE,KAAK,aAAa,EAAE,KAAK,QAAQ,QAAQ,IAAI,CAAC,EAAE,CAAC,CAAC;AAEjG,QAAO,iBAAiB,KAAK;AAChC;AAED,SAAS,gBAAgB,OAAO;AAC5B,QAAO,OAAO,UAAU,YAAa,SAAS,OAAO,UAAU;AAClE;AACD,SAAS,YAAY,MAAM;AACvB,QAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AACtD;AAED,MAAM,0BAA0B,OAAiD,qBAA0B;;;;;AAK3G,IAAI;CACH,SAAUM,yBAAuB;;;;;CAK9BA,wBAAsBA,wBAAsB,aAAa,KAAK;;;;;CAK9DA,wBAAsBA,wBAAsB,eAAe,KAAK;;;;;CAKhEA,wBAAsBA,wBAAsB,gBAAgB,MAAM;AACrE,GAAE,0BAA0B,wBAAwB,CAAE,GAAE;AAEzD,MAAM,oBAAoB;CACtB,CAAC,GAAsC,EAAE,sBAAU,iBAAiB,EAAE;AAClE,SAAO,CAAC,eAAe,EAAE,KAAK,UAAUN,WAAS,GAAG,kBAC9C,uBAAuB,KAAK,UAAU,gBAAgB,GACtD,IAAI;CACb;CACD,CAAC,GAA8C,EAAE,MAAM,IAAK,EAAE;AAC1D,SAAO,CAAC,iBAAiB,EAAE,KAAK,SAAS,MAAM,EAAE,eAAe,GAAG,CAAC,yBAAyB,CAAC;CACjG;CACD,CAAC,GAAuC,EAAE,MAAM,IAAI,EAAE;AAClD,SAAO,CAAC,yBAAyB,EAAE,KAAK,SAAS,MAAM,EAAE,GAAG,SAAS,yBAAyB,CAAC;CAClG;CACD,CAAC,GAAyC,EAAE,MAAM,IAAI,EAAE;AACpD,SAAO,CAAC,2BAA2B,EAAE,KAAK,SAAS,MAAM,EAAE,GAAG,SAAS,wBAAwB,CAAC;CACnG;CACD,CAAC,IAA2C,EAAE,MAAM,IAAI,EAAE;AACtD,SAAO,CAAC,mDAAmD,EAAE,KAAK,SAAS,EAAE,CAAC;CACjF;AACJ;;;;;;;AAOD,SAAS,kBAAkB,MAAM,QAAQ;AAGjC,QAAO,OAAO,IAAI,MAAM,kBAAkB,MAAM,OAAO,GAAG;EACtD;GACC,0BAA0B;CAC9B,GAAE,OAAO;AAQjB;AACD,SAAS,oBAAoB,OAAO,MAAM;AACtC,QAAQ,iBAAiB,SACrB,2BAA2B,UAC1B,QAAQ,QAAQ,CAAC,EAAE,MAAM,OAAO;AACxC;AACD,MAAM,kBAAkB;CAAC;CAAU;CAAS;AAAO;AACnD,SAAS,eAAe,IAAI;AACxB,KAAI,OAAO,OAAO,SACd,QAAO;AACX,KAAI,GAAG,QAAQ,KACX,QAAO,GAAG;CACd,MAAMA,aAAW,CAAE;AACnB,MAAK,MAAM,OAAO,gBACd,KAAI,OAAO,IACPA,WAAS,OAAO,GAAG;AAE3B,QAAO,KAAK,UAAUA,YAAU,MAAM,EAAE;AAC3C;AAGD,MAAM,qBAAqB;AAC3B,MAAM,2BAA2B;CAC7B,WAAW;CACX,QAAQ;CACR,OAAO;CACP,KAAK;AACR;AAED,MAAM,iBAAiB;;;;;;;;AAQvB,SAAS,eAAe,UAAU,cAAc;CAC5C,MAAM,UAAU,OAAO,CAAE,GAAE,0BAA0B,aAAa;CAElE,MAAM,QAAQ,CAAE;CAEhB,IAAI,UAAU,QAAQ,QAAQ,MAAM;CAEpC,MAAM,OAAO,CAAE;AACf,MAAK,MAAM,WAAW,UAAU;EAE5B,MAAM,gBAAgB,QAAQ,SAAS,CAAE,IAAG,CAAC,EAAwB;AAErE,MAAI,QAAQ,UAAU,CAAC,QAAQ,QAC3B,WAAW;AACf,OAAK,IAAI,aAAa,GAAG,aAAa,QAAQ,QAAQ,cAAc;GAChE,MAAM,QAAQ,QAAQ;GAEtB,IAAI,kBAAkB,MACjB,QAAQ,YAAY,MAA0C;AACnE,OAAI,MAAM,SAAS,GAA0B;AAEzC,QAAI,CAAC,YACD,WAAW;IACf,WAAW,MAAM,MAAM,QAAQ,gBAAgB,OAAO;IACtD,mBAAmB;GACtB,WACQ,MAAM,SAAS,GAAyB;IAC7C,MAAM,EAAE,OAAO,YAAY,UAAU,QAAQ,GAAG;IAChD,KAAK,KAAK;KACN,MAAM;KACN;KACA;IACH,EAAC;IACF,MAAMO,OAAK,SAAS,SAAS;AAE7B,QAAIA,SAAO,oBAAoB;KAC3B,mBAAmB;AAEnB,SAAI;MACW,CAAS,EAALA,KAAJ,AAAS;KACvB,SACM,KAAK;AACR,YAAM,IAAI,MAAM,CAAC,iCAAiC,EAAE,MAAM,GAAG,EAAEA,KAAG,GAAG,CAAC,GAClE,IAAI;KACX;IACJ;IAED,IAAI,aAAa,aAAa,CAAC,IAAI,EAAEA,KAAG,QAAQ,EAAEA,KAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAEA,KAAG,CAAC,CAAC;AAEtE,QAAI,CAAC,YACD,aAGI,YAAY,QAAQ,SAAS,IACvB,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC,GACpB,MAAM;AACpB,QAAI,UACA,cAAc;IAClB,WAAW;IACX,mBAAmB;AACnB,QAAI,UACA,mBAAmB;AACvB,QAAI,YACA,mBAAmB;AACvB,QAAIA,SAAO,MACP,mBAAmB;GAC1B;GACD,cAAc,KAAK,gBAAgB;EACtC;EAGD,MAAM,KAAK,cAAc;CAC5B;AAED,KAAI,QAAQ,UAAU,QAAQ,KAAK;EAC/B,MAAM,IAAI,MAAM,SAAS;EACzB,MAAM,GAAG,MAAM,GAAG,SAAS,MAAM;CACpC;AAED,KAAI,CAAC,QAAQ,QACT,WAAW;AACf,KAAI,QAAQ,KACR,WAAW;UAEN,QAAQ,UAAU,CAAC,QAAQ,SAAS,IAAI,EAC7C,WAAW;CACf,MAAM,KAAK,IAAI,OAAO,SAAS,QAAQ,YAAY,KAAK;CACxD,SAAS,MAAM,MAAM;EACjB,MAAM,QAAQ,KAAK,MAAM,GAAG;EAC5B,MAAM,SAAS,CAAE;AACjB,MAAI,CAAC,MACD,QAAO;AACX,OAAK,IAAI,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;GACnC,MAAM,QAAQ,MAAM,MAAM;GAC1B,MAAM,MAAM,KAAK,IAAI;GACrB,OAAO,IAAI,QAAQ,SAAS,IAAI,aAAa,MAAM,MAAM,IAAI,GAAG;EACnE;AACD,SAAO;CACV;CACD,SAAS,UAAU,QAAQ;EACvB,IAAI,OAAO;EAEX,IAAI,uBAAuB;AAC3B,OAAK,MAAM,WAAW,UAAU;AAC5B,OAAI,CAAC,wBAAwB,CAAC,KAAK,SAAS,IAAI,EAC5C,QAAQ;GACZ,uBAAuB;AACvB,QAAK,MAAM,SAAS,QAChB,KAAI,MAAM,SAAS,GACf,QAAQ,MAAM;YAET,MAAM,SAAS,GAAyB;IAC7C,MAAM,EAAE,OAAO,YAAY,UAAU,GAAG;IACxC,MAAM,QAAQ,SAAS,SAAS,OAAO,SAAS;AAChD,QAAI,QAAQ,MAAM,IAAI,CAAC,WACnB,OAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,MAAM,yDAAyD,CAAC;IAEvG,MAAM,OAAO,QAAQ,MAAM,GACrB,MAAM,KAAK,IAAI,GACf;AACN,QAAI,CAAC,KACD,KAAI,UAEA;SAAI,QAAQ,SAAS,EAEjB,KAAI,KAAK,SAAS,IAAI,EAClB,OAAO,KAAK,MAAM,GAAG,GAAG;UAGxB,uBAAuB;IAC9B,MAGD,OAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAC;IAE3D,QAAQ;GACX;EAER;AAED,SAAO,QAAQ;CAClB;AACD,QAAO;EACH;EACA;EACA;EACA;EACA;CACH;AACJ;;;;;;;;;;AAUD,SAAS,kBAAkB,GAAG,GAAG;CAC7B,IAAI,IAAI;AACR,QAAO,IAAI,EAAE,UAAU,IAAI,EAAE,QAAQ;EACjC,MAAM,OAAO,EAAE,KAAK,EAAE;AAEtB,MAAI,KACA,QAAO;EACX;CACH;AAGD,KAAI,EAAE,SAAS,EAAE,OACb,QAAO,EAAE,WAAW,KAAK,EAAE,OAAO,KAC5B,KACA;UAED,EAAE,SAAS,EAAE,OAClB,QAAO,EAAE,WAAW,KAAK,EAAE,OAAO,KAC5B,IACA;AAEV,QAAO;AACV;;;;;;;;AAQD,SAAS,uBAAuB,GAAG,GAAG;CAClC,IAAI,IAAI;CACR,MAAM,SAAS,EAAE;CACjB,MAAM,SAAS,EAAE;AACjB,QAAO,IAAI,OAAO,UAAU,IAAI,OAAO,QAAQ;EAC3C,MAAM,OAAO,kBAAkB,OAAO,IAAI,OAAO,GAAG;AAEpD,MAAI,KACA,QAAO;EACX;CACH;AACD,KAAI,KAAK,IAAI,OAAO,SAAS,OAAO,OAAO,KAAK,GAAG;AAC/C,MAAI,oBAAoB,OAAO,CAC3B,QAAO;AACX,MAAI,oBAAoB,OAAO,CAC3B,QAAO;CACd;AAED,QAAO,OAAO,SAAS,OAAO;AAOjC;;;;;;;AAOD,SAAS,oBAAoB,OAAO;CAChC,MAAM,OAAO,MAAM,MAAM,SAAS;AAClC,QAAO,MAAM,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK;AACtD;AAED,MAAM,aAAa;CACf,MAAM;CACN,OAAO;AACV;AACD,MAAM,iBAAiB;AAIvB,SAAS,aAAa,MAAM;AACxB,KAAI,CAAC,KACD,QAAO,CAAC,CAAE,CAAC;AACf,KAAI,SAAS,IACT,QAAO,CAAC,CAAC,UAAW,CAAC;AACzB,KAAI,CAAC,KAAK,WAAW,IAAI,CACrB,OAAM,IAAI,MACJ,CAAC,sCAAsC,EAAE,KAAK,cAAc,EAAE,KAAK,EAAE,CAAC;CAIhF,SAAS,MAAM,SAAS;AACpB,QAAM,IAAI,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,EAAE,OAAO,GAAG,EAAE,SAAS;CAC3D;CACD,IAAI,QAAQ;CACZ,IAAI,gBAAgB;CACpB,MAAM,SAAS,CAAE;CAGjB,IAAI;CACJ,SAAS,kBAAkB;AACvB,MAAI,SACA,OAAO,KAAK,QAAQ;EACxB,UAAU,CAAE;CACf;CAED,IAAI,IAAI;CAER,IAAI;CAEJ,IAAI,SAAS;CAEb,IAAI,WAAW;CACf,SAAS,gBAAgB;AACrB,MAAI,CAAC,OACD;AACJ,MAAI,UAAU,GACV,QAAQ,KAAK;GACT,MAAM;GACN,OAAO;EACV,EAAC;WAEG,UAAU,KACf,UAAU,KACV,UAAU,GAAuC;AACjD,OAAI,QAAQ,SAAS,MAAM,SAAS,OAAO,SAAS,MAChD,MAAM,CAAC,oBAAoB,EAAE,OAAO,4CAA4C,CAAC,CAAC;GACtF,QAAQ,KAAK;IACT,MAAM;IACN,OAAO;IACP,QAAQ;IACR,YAAY,SAAS,OAAO,SAAS;IACrC,UAAU,SAAS,OAAO,SAAS;GACtC,EAAC;EACL,OAEG,MAAM,kCAAkC;EAE5C,SAAS;CACZ;CACD,SAAS,kBAAkB;EACvB,UAAU;CACb;AACD,QAAO,IAAI,KAAK,QAAQ;EACpB,OAAO,KAAK;AACZ,MAAI,SAAS,QAAQ,UAAU,GAAoC;GAC/D,gBAAgB;GAChB,QAAQ;AACR;EACH;AACD,UAAQ,OAAR;GACI,KAAK;AACD,QAAI,SAAS,KAAK;AACd,SAAI,QACA,eAAe;KAEnB,iBAAiB;IACpB,WACQ,SAAS,KAAK;KACnB,eAAe;KACf,QAAQ;IACX,OAEG,iBAAiB;AAErB;GACJ,KAAK;IACD,iBAAiB;IACjB,QAAQ;AACR;GACJ,KAAK;AACD,QAAI,SAAS,KACT,QAAQ;aAEH,eAAe,KAAK,KAAK,EAC9B,iBAAiB;SAEhB;KACD,eAAe;KACf,QAAQ;AAER,SAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KACzC;IACP;AACD;GACJ,KAAK;AAMD,QAAI,SAAS,IAET,KAAI,SAAS,SAAS,SAAS,MAAM,MACjC,WAAW,SAAS,MAAM,GAAG,GAAG,GAAG;SAEnC,QAAQ;SAGZ,YAAY;AAEhB;GACJ,KAAK;IAED,eAAe;IACf,QAAQ;AAER,QAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KACzC;IACJ,WAAW;AACX;GACJ;IACI,MAAM,gBAAgB;AACtB;EACP;CACJ;AACD,KAAI,UAAU,GACV,MAAM,CAAC,oCAAoC,EAAE,OAAO,CAAC,CAAC,CAAC;CAC3D,eAAe;CACf,iBAAiB;AAEjB,QAAO;AACV;AAED,SAAS,yBAAyB,QAAQ,QAAQ,SAAS;CACvD,MAAM,SAAS,eAAe,aAAa,OAAO,KAAK,EAAE,QAAQ;CAEpB;EACzC,MAAM,+BAAe,IAAI;AACzB,OAAK,MAAM,OAAO,OAAO,MAAM;AAC3B,OAAI,aAAa,IAAI,IAAI,KAAK,EAC1B,KAAK,CAAC,mCAAmC,EAAE,IAAI,KAAK,YAAY,EAAE,OAAO,KAAK,0DAA0D,CAAC,CAAC;GAC9I,aAAa,IAAI,IAAI,KAAK;EAC7B;CACJ;CACD,MAAM,UAAU,OAAO,QAAQ;EAC3B;EACA;EAEA,UAAU,CAAE;EACZ,OAAO,CAAE;CACZ,EAAC;AACF,KAAI,QAIA;MAAI,CAAC,QAAQ,OAAO,YAAY,CAAC,OAAO,OAAO,SAC3C,OAAO,SAAS,KAAK,QAAQ;CAAC;AAEtC,QAAO;AACV;;;;;;;;AASD,SAAS,oBAAoB,QAAQ,eAAe;CAEhD,MAAM,WAAW,CAAE;CACnB,MAAM,6BAAa,IAAI;CACvB,gBAAgB,aAAa;EAAE,QAAQ;EAAO,KAAK;EAAM,WAAW;CAAO,GAAE,cAAc;CAC3F,SAAS,iBAAiB,MAAM;AAC5B,SAAO,WAAW,IAAI,KAAK;CAC9B;CACD,SAAS,SAAS,QAAQ,QAAQ,gBAAgB;EAE9C,MAAM,YAAY,CAAC;EACnB,MAAM,uBAAuB,qBAAqB,OAAO;EAErD,mCAAmC,sBAAsB,OAAO;EAGpE,qBAAqB,UAAU,kBAAkB,eAAe;EAChE,MAAM,UAAU,aAAa,eAAe,OAAO;EAEnD,MAAM,oBAAoB,CAAC,oBAAqB;AAChD,MAAI,WAAW,QAAQ;GACnB,MAAM,UAAU,OAAO,OAAO,UAAU,WAAW,CAAC,OAAO,KAAM,IAAG,OAAO;AAC3E,QAAK,MAAM,SAAS,SAChB,kBAAkB,KAGlB,qBAAqB,OAAO,CAAE,GAAE,sBAAsB;IAGlD,YAAY,iBACN,eAAe,OAAO,aACtB,qBAAqB;IAC3B,MAAM;IAEN,SAAS,iBACH,eAAe,SACf;GAGT,EAAC,CAAC,CAAC;EAEX;EACD,IAAI;EACJ,IAAI;AACJ,OAAK,MAAM,oBAAoB,mBAAmB;GAC9C,MAAM,EAAE,MAAM,GAAG;AAIjB,OAAI,UAAU,KAAK,OAAO,KAAK;IAC3B,MAAM,aAAa,OAAO,OAAO;IACjC,MAAM,kBAAkB,WAAW,WAAW,SAAS,OAAO,MAAM,KAAK;IACzE,iBAAiB,OACb,OAAO,OAAO,QAAQ,QAAQ,kBAAkB;GACvD;AACD,OAA+C,iBAAiB,SAAS,IACrE,OAAM,IAAI,MAAM;GAIpB,UAAU,yBAAyB,kBAAkB,QAAQ,QAAQ;AACrE,OAA+C,UAAU,KAAK,OAAO,KACjE,iCAAiC,SAAS,OAAO;AAGrD,OAAI,gBAAgB;IAChB,eAAe,MAAM,KAAK,QAAQ;IAE9B,gBAAgB,gBAAgB,QAAQ;GAE/C,OACI;IAED,kBAAkB,mBAAmB;AACrC,QAAI,oBAAoB,SACpB,gBAAgB,MAAM,KAAK,QAAQ;AAGvC,QAAI,aAAa,OAAO,QAAQ,CAAC,cAAc,QAAQ,EAAE;KAEjD,wBAAwB,QAAQ,OAAO;KAE3C,YAAY,OAAO,KAAK;IAC3B;GACJ;AAGD,OAAI,YAAY,QAAQ,EACpB,cAAc,QAAQ;AAE1B,OAAI,qBAAqB,UAAU;IAC/B,MAAM,WAAW,qBAAqB;AACtC,SAAK,IAAI,IAAI,GAAG,IAAI,SAAS,QAAQ,KACjC,SAAS,SAAS,IAAI,SAAS,kBAAkB,eAAe,SAAS,GAAG;GAEnF;GAGD,iBAAiB,kBAAkB;EAKtC;AACD,SAAO,kBACD,MAAM;GAEJ,YAAY,gBAAgB;EAC/B,IACC;CACT;CACD,SAAS,YAAY,YAAY;AAC7B,MAAI,YAAY,WAAW,EAAE;GACzB,MAAM,UAAU,WAAW,IAAI,WAAW;AAC1C,OAAI,SAAS;IACT,WAAW,OAAO,WAAW;IAC7B,SAAS,OAAO,SAAS,QAAQ,QAAQ,EAAE,EAAE;IAC7C,QAAQ,SAAS,QAAQ,YAAY;IACrC,QAAQ,MAAM,QAAQ,YAAY;GACrC;EACJ,OACI;GACD,MAAM,QAAQ,SAAS,QAAQ,WAAW;AAC1C,OAAI,QAAQ,IAAI;IACZ,SAAS,OAAO,OAAO,EAAE;AACzB,QAAI,WAAW,OAAO,MAClB,WAAW,OAAO,WAAW,OAAO,KAAK;IAC7C,WAAW,SAAS,QAAQ,YAAY;IACxC,WAAW,MAAM,QAAQ,YAAY;GACxC;EACJ;CACJ;CACD,SAAS,YAAY;AACjB,SAAO;CACV;CACD,SAAS,cAAc,SAAS;EAC5B,MAAM,QAAQ,mBAAmB,SAAS,SAAS;EACnD,SAAS,OAAO,OAAO,GAAG,QAAQ;AAElC,MAAI,QAAQ,OAAO,QAAQ,CAAC,cAAc,QAAQ,EAC9C,WAAW,IAAI,QAAQ,OAAO,MAAM,QAAQ;CACnD;CACD,SAAS,QAAQP,YAAU,iBAAiB;EACxC,IAAI;EACJ,IAAI,SAAS,CAAE;EACf,IAAI;EACJ,IAAI;AACJ,MAAI,UAAUA,cAAYA,WAAS,MAAM;GACrC,UAAU,WAAW,IAAIA,WAAS,KAAK;AACvC,OAAI,CAAC,QACD,OAAM,kBAAkB,GAAsC,EAC1D,qBACH,EAAC;GAEuC;IACzC,MAAM,gBAAgB,OAAO,KAAKA,WAAS,UAAU,CAAE,EAAC,CAAC,OAAO,eAAa,CAAC,QAAQ,KAAK,KAAK,OAAK,EAAE,SAAS,UAAU,CAAC;AAC3H,QAAI,cAAc,QACd,KAAK,CAAC,4BAA4B,EAAE,cAAc,KAAK,SAAO,CAAC,8HAA8H,CAAC,CAAC;GAEtM;GACD,OAAO,QAAQ,OAAO;GACtB,SAAS,OAET,mBAAmB,gBAAgB,QAGnC,QAAQ,KACH,OAAO,OAAK,CAAC,EAAE,SAAS,CACxB,OAAO,QAAQ,SAAS,QAAQ,OAAO,KAAK,OAAO,OAAK,EAAE,SAAS,GAAG,CAAE,EAAC,CACzE,IAAI,OAAK,EAAE,KAAK,CAAC,EAGtBA,WAAS,UACL,mBAAmBA,WAAS,QAAQ,QAAQ,KAAK,IAAI,OAAK,EAAE,KAAK,CAAC,CAAC;GAEvE,OAAO,QAAQ,UAAU,OAAO;EACnC,WACQA,WAAS,QAAQ,MAAM;GAG5B,OAAOA,WAAS;AAChB,OAA+C,CAAC,KAAK,WAAW,IAAI,EAChE,KAAK,CAAC,wDAAwD,EAAE,KAAK,iDAAiD,EAAE,KAAK,sHAAsH,CAAC,CAAC;GAEzP,UAAU,SAAS,KAAK,OAAK,EAAE,GAAG,KAAK,KAAK,CAAC;AAE7C,OAAI,SAAS;IAET,SAAS,QAAQ,MAAM,KAAK;IAC5B,OAAO,QAAQ,OAAO;GACzB;EAEJ,OACI;GAED,UAAU,gBAAgB,OACpB,WAAW,IAAI,gBAAgB,KAAK,GACpC,SAAS,KAAK,OAAK,EAAE,GAAG,KAAK,gBAAgB,KAAK,CAAC;AACzD,OAAI,CAAC,QACD,OAAM,kBAAkB,GAAsC;IAC1D;IACA;GACH,EAAC;GACN,OAAO,QAAQ,OAAO;GAGtB,SAAS,OAAO,CAAE,GAAE,gBAAgB,QAAQA,WAAS,OAAO;GAC5D,OAAO,QAAQ,UAAU,OAAO;EACnC;EACD,MAAM,UAAU,CAAE;EAClB,IAAI,gBAAgB;AACpB,SAAO,eAAe;GAElB,QAAQ,QAAQ,cAAc,OAAO;GACrC,gBAAgB,cAAc;EACjC;AACD,SAAO;GACH;GACA;GACA;GACA;GACA,MAAM,gBAAgB,QAAQ;EACjC;CACJ;CAED,OAAO,QAAQ,WAAS,SAAS,MAAM,CAAC;CACxC,SAAS,cAAc;EACnB,SAAS,SAAS;EAClB,WAAW,OAAO;CACrB;AACD,QAAO;EACH;EACA;EACA;EACA;EACA;EACA;CACH;AACJ;AACD,SAAS,mBAAmB,QAAQ,MAAM;CACtC,MAAM,YAAY,CAAE;AACpB,MAAK,MAAM,OAAO,KACd,KAAI,OAAO,QACP,UAAU,OAAO,OAAO;AAEhC,QAAO;AACV;;;;;;;AAOD,SAAS,qBAAqB,QAAQ;CAClC,MAAM,aAAa;EACf,MAAM,OAAO;EACb,UAAU,OAAO;EACjB,MAAM,OAAO;EACb,MAAM,OAAO,QAAQ,CAAE;EACvB,SAAS,OAAO;EAChB,aAAa,OAAO;EACpB,OAAO,qBAAqB,OAAO;EACnC,UAAU,OAAO,YAAY,CAAE;EAC/B,WAAW,CAAE;EACb,6BAAa,IAAI;EACjB,8BAAc,IAAI;EAClB,gBAAgB,CAAE;EAGlB,YAAY,gBAAgB,SACtB,OAAO,cAAc,OACrB,OAAO,aAAa,EAAE,SAAS,OAAO,UAAW;CAC1D;CAID,OAAO,eAAe,YAAY,QAAQ,EACtC,OAAO,CAAE,EACZ,EAAC;AACF,QAAO;AACV;;;;;;AAMD,SAAS,qBAAqB,QAAQ;CAClC,MAAM,cAAc,CAAE;CAEtB,MAAM,QAAQ,OAAO,SAAS;AAC9B,KAAI,eAAe,QACf,YAAY,UAAU;KAKtB,MAAK,MAAM,QAAQ,OAAO,YACtB,YAAY,QAAQ,OAAO,UAAU,WAAW,MAAM,QAAQ;AAEtE,QAAO;AACV;;;;;AAKD,SAAS,cAAc,QAAQ;AAC3B,QAAO,QAAQ;AACX,MAAI,OAAO,OAAO,QACd,QAAO;EACX,SAAS,OAAO;CACnB;AACD,QAAO;AACV;;;;;;AAMD,SAAS,gBAAgB,SAAS;AAC9B,QAAO,QAAQ,OAAO,CAAC,MAAM,WAAW,OAAO,MAAM,OAAO,KAAK,EAAE,CAAE,EAAC;AACzE;AACD,SAAS,aAAa,UAAU,gBAAgB;CAC5C,MAAM,UAAU,CAAE;AAClB,MAAK,MAAM,OAAO,UACd,QAAQ,OAAO,OAAO,iBAAiB,eAAe,OAAO,SAAS;AAE1E,QAAO;AACV;AACD,SAAS,YAAY,GAAG,GAAG;AACvB,QAAQ,EAAE,SAAS,EAAE,QACjB,EAAE,aAAa,EAAE,YACjB,EAAE,eAAe,EAAE;AAC1B;;;;;;;AAOD,SAAS,gBAAgB,GAAG,GAAG;AAC3B,MAAK,MAAM,OAAO,EAAE,KAChB,KAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM,IAAI,CAAC,CAC1D,QAAO,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,4BAA4B,EAAE,EAAE,OAAO,KAAK,wCAAwC,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC;AAE9I,MAAK,MAAM,OAAO,EAAE,KAChB,KAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM,IAAI,CAAC,CAC1D,QAAO,KAAK,CAAC,OAAO,EAAE,EAAE,OAAO,KAAK,4BAA4B,EAAE,EAAE,OAAO,KAAK,wCAAwC,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC;AAEjJ;;;;;;;AAOD,SAAS,mCAAmC,sBAAsB,QAAQ;AACtE,KAAI,UACA,OAAO,OAAO,QACd,CAAC,qBAAqB,QACtB,CAAC,qBAAqB,MACtB,KAAK,CAAC,iBAAiB,EAAE,OAAO,OAAO,OAAO,KAAK,CAAC,0OAA0O,CAAC,CAAC;AAEvS;AACD,SAAS,wBAAwB,QAAQ,QAAQ;AAC7C,MAAK,IAAI,WAAW,QAAQ,UAAU,WAAW,SAAS,OACtD,KAAI,SAAS,OAAO,SAAS,OAAO,KAChC,OAAM,IAAI,MAAM,CAAC,eAAe,EAAE,OAAO,OAAO,KAAK,CAAC,sBAAsB,EAAE,WAAW,WAAW,UAAU,aAAa,sHAAsH,CAAC;AAG7P;AACD,SAAS,iCAAiC,QAAQ,QAAQ;AACtD,MAAK,MAAM,OAAO,OAAO,KACrB,KAAI,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,MAAM,IAAI,CAAC,CAC9C,QAAO,KAAK,CAAC,eAAe,EAAE,OAAO,OAAO,KAAK,wCAAwC,EAAE,IAAI,KAAK,iBAAiB,EAAE,OAAO,OAAO,KAAK,EAAE,CAAC,CAAC;AAEzJ;;;;;;;;;;AAUD,SAAS,mBAAmB,SAAS,UAAU;CAE3C,IAAI,QAAQ;CACZ,IAAI,QAAQ,SAAS;AACrB,QAAO,UAAU,OAAO;EACpB,MAAM,MAAO,QAAQ,SAAU;EAC/B,MAAM,YAAY,uBAAuB,SAAS,SAAS,KAAK;AAChE,MAAI,YAAY,GACZ,QAAQ;OAGR,QAAQ,MAAM;CAErB;CAED,MAAM,oBAAoB,qBAAqB,QAAQ;AACvD,KAAI,mBAAmB;EACnB,QAAQ,SAAS,YAAY,mBAAmB,QAAQ,EAAE;AAC1D,MAA+C,QAAQ,GAEnD,KAAK,CAAC,wBAAwB,EAAE,kBAAkB,OAAO,KAAK,cAAc,EAAE,QAAQ,OAAO,KAAK,CAAC,CAAC,CAAC;CAE5G;AACD,QAAO;AACV;AACD,SAAS,qBAAqB,SAAS;CACnC,IAAI,WAAW;AACf,QAAQ,WAAW,SAAS,OACxB,KAAI,YAAY,SAAS,IACrB,uBAAuB,SAAS,SAAS,KAAK,EAC9C,QAAO;AAGf;AACH;;;;;;;;AAQD,SAAS,YAAY,EAAE,QAAQ,EAAE;AAC7B,QAAO,CAAC,EAAE,OAAO,QACZ,OAAO,cAAc,OAAO,KAAK,OAAO,WAAW,CAAC,UACrD,OAAO;AACd;;;;;;;;;;AAWD,SAAS,WAAW,QAAQ;CACxB,MAAM,QAAQ,CAAE;AAGhB,KAAI,WAAW,MAAM,WAAW,IAC5B,QAAO;CACX,MAAM,eAAe,OAAO,OAAO;CACnC,MAAM,gBAAgB,eAAe,OAAO,MAAM,EAAE,GAAG,QAAQ,MAAM,IAAI;AACzE,MAAK,IAAI,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;EAE1C,MAAM,cAAc,aAAa,GAAG,QAAQ,SAAS,IAAI;EAEzD,MAAM,QAAQ,YAAY,QAAQ,IAAI;EACtC,MAAM,MAAM,OAAO,QAAQ,IAAI,cAAc,YAAY,MAAM,GAAG,MAAM,CAAC;EACzE,MAAM,QAAQ,QAAQ,IAAI,OAAO,OAAO,YAAY,MAAM,QAAQ,EAAE,CAAC;AACrE,MAAI,OAAO,OAAO;GAEd,IAAI,eAAe,MAAM;AACzB,OAAI,CAAC,QAAQ,aAAa,EACtB,eAAe,MAAM,OAAO,CAAC,YAAa;GAE9C,aAAa,KAAK,MAAM;EAC3B,OAEG,MAAM,OAAO;CAEpB;AACD,QAAO;AACV;;;;;;;;;;AAUD,SAAS,eAAe,OAAO;CAC3B,IAAI,SAAS;AACb,MAAK,IAAI,OAAO,OAAO;EACnB,MAAM,QAAQ,MAAM;EACpB,MAAM,eAAe,IAAI;AACzB,MAAI,SAAS,MAAM;AAEf,OAAI,UAAU,QACV,WAAW,OAAO,SAAS,MAAM,MAAM;AAE3C;EACH;EAED,MAAM,SAAS,QAAQ,MAAM,GACvB,MAAM,IAAI,OAAK,KAAK,iBAAiB,EAAE,CAAC,GACxC,CAAC,SAAS,iBAAiB,MAAM,AAAC;EACxC,OAAO,QAAQ,aAAS;AAGpB,OAAIQ,YAAU,QAAW;IAErB,WAAW,OAAO,SAAS,MAAM,MAAM;AACvC,QAAIA,WAAS,MACT,UAAU,MAAMA;GACvB;EACJ,EAAC;CACL;AACD,QAAO;AACV;;;;;;;;;AASD,SAAS,eAAe,OAAO;CAC3B,MAAM,kBAAkB,CAAE;AAC1B,MAAK,MAAM,OAAO,OAAO;EACrB,MAAM,QAAQ,MAAM;AACpB,MAAI,UAAU,QACV,gBAAgB,OAAO,QAAQ,MAAM,GAC/B,MAAM,IAAI,OAAM,KAAK,OAAO,OAAO,KAAK,EAAG,GAC3C,SAAS,OACL,QACA,KAAK;CAEtB;AACD,QAAO;AACV;;;;;;;;AASD,MAAM,kBAAkB,OAAiD,+BAAoC;;;;;;;AAO7G,MAAM,eAAe,OAAiD,oBAAyB;;;;;;;AAO/F,MAAM,YAAY,OAAiD,SAAc;;;;;;;AAOjF,MAAM,mBAAmB,OAAiD,iBAAsB;;;;;;;AAOhG,MAAM,wBAAwB,OAAiD,uBAA4B;;;;AAK3G,SAAS,eAAe;CACpB,IAAI,WAAW,CAAE;CACjB,SAAS,IAAI,SAAS;EAClB,SAAS,KAAK,QAAQ;AACtB,SAAO,MAAM;GACT,MAAM,IAAI,SAAS,QAAQ,QAAQ;AACnC,OAAI,IAAI,IACJ,SAAS,OAAO,GAAG,EAAE;EAC5B;CACJ;CACD,SAAS,QAAQ;EACb,WAAW,CAAE;CAChB;AACD,QAAO;EACH;EACA,MAAM,MAAM,SAAS,OAAO;EAC5B;CACH;AACJ;AAED,SAAS,cAAc,QAAQ,MAAM,OAAO;CACxC,MAAM,iBAAiB,MAAM;EACzB,OAAO,MAAM,OAAO,MAAM;CAC7B;CACD,YAAY,eAAe;CAC3B,cAAc,eAAe;CAC7B,YAAY,MAAM;EACd,OAAO,MAAM,IAAI,MAAM;CAC1B,EAAC;CACF,OAAO,MAAM,IAAI,MAAM;AAC1B;;;;;;;;AAQD,SAAS,mBAAmB,YAAY;AACpC,KAA+C,CAAC,oBAAoB,EAAE;EAClE,KAAK,yGAAyG;AAC9G;CACH;CACD,MAAM,eAAe,OAAO,iBAE5B,CAAE,EAAC,CAAC;AACJ,KAAI,CAAC,cAAc;EAEX,KAAK,2LAA2L;AACpM;CACH;CACD,cAAc,cAAc,eAAe,WAAW;AACzD;;;;;;;;AAQD,SAAS,oBAAoB,aAAa;AACtC,KAA+C,CAAC,oBAAoB,EAAE;EAClE,KAAK,0GAA0G;AAC/G;CACH;CACD,MAAM,eAAe,OAAO,iBAE5B,CAAE,EAAC,CAAC;AACJ,KAAI,CAAC,cAAc;EAEX,KAAK,4LAA4L;AACrM;CACH;CACD,cAAc,cAAc,gBAAgB,YAAY;AAC3D;AACD,SAAS,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,iBAAiB,QAAM,IAAI,EAAE;CAElF,MAAM,qBAAqB,WAEtB,OAAO,eAAe,QAAQ,OAAO,eAAe,SAAS,CAAE;AACpE,QAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;EAC1C,MAAM,OAAO,CAAC,UAAU;AACpB,OAAI,UAAU,OACV,OAAO,kBAAkB,GAAuC;IAC5D;IACA;GACH,EAAC,CAAC;YAEE,iBAAiB,OACtB,OAAO,MAAM;YAER,gBAAgB,MAAM,EAC3B,OAAO,kBAAkB,GAA8C;IACnE,MAAM;IACN,IAAI;GACP,EAAC,CAAC;QAEF;AACD,QAAI,sBAEA,OAAO,eAAe,UAAU,sBAChC,OAAO,UAAU,YACjB,mBAAmB,KAAK,MAAM;IAElC,SAAS;GACZ;EACJ;EAED,MAAM,cAAc,eAAe,MAAM,MAAM,KAAK,UAAU,OAAO,UAAU,OAAO,IAAI,MAAgD,oBAAoB,MAAM,IAAI,KAAK,CAAQ,CAAC;EACtL,IAAI,YAAY,QAAQ,QAAQ,YAAY;AAC5C,MAAI,MAAM,SAAS,GACf,YAAY,UAAU,KAAK,KAAK;AACpC,MAA+C,MAAM,SAAS,GAAG;GAC7D,MAAM,UAAU,CAAC,+CAA+C,EAAE,MAAM,OAAO,OAAM,MAAM,OAAO,OAAM,GAAG,GAAG,EAAE,MAAM,UAAU,CAAC,wHAAwH,CAAC;AAC1P,OAAI,OAAO,gBAAgB,YAAY,UAAU,aAC7C,YAAY,UAAU,KAAK,mBAAiB;AAExC,QAAI,CAAC,KAAK,SAAS;KACf,KAAK,QAAQ;AACb,YAAO,QAAQ,uBAAO,IAAI,MAAM,4BAA4B;IAC/D;AACD,WAAO;GACV,EAAC;YAEG,gBAAgB,QAErB;QAAI,CAAC,KAAK,SAAS;KACf,KAAK,QAAQ;KACb,uBAAO,IAAI,MAAM,4BAA4B;AAC7C;IACH;;EAER;EACD,UAAU,MAAM,SAAO,OAAO,IAAI,CAAC;CACtC;AACJ;AACD,SAAS,oBAAoB,MAAM,IAAI,MAAM;CACzC,IAAI,SAAS;AACb,QAAO,WAAY;AACf,MAAI,aAAa,GACb,KAAK,CAAC,uFAAuF,EAAE,KAAK,SAAS,MAAM,EAAE,GAAG,SAAS,+FAA+F,CAAC,CAAC;EAEtO,KAAK,UAAU;AACf,MAAI,WAAW,GACX,KAAK,MAAM,MAAM,UAAU;CAClC;AACJ;AACD,SAAS,wBAAwB,SAAS,WAAW,IAAI,MAAM,iBAAiB,QAAM,IAAI,EAAE;CACxF,MAAM,SAAS,CAAE;AACjB,MAAK,MAAM,UAAU,SAAS;AAC1B,MAA+C,CAAC,OAAO,cAAc,CAAC,OAAO,SAAS,QAClF,KAAK,CAAC,kBAAkB,EAAE,OAAO,KAAK,4DAAoC,CAC5C,CAAC;AAEnC,OAAK,MAAM,QAAQ,OAAO,YAAY;GAClC,IAAI,eAAe,OAAO,WAAW;AAEjC,OAAI,CAAC,gBACA,OAAO,iBAAiB,YACrB,OAAO,iBAAiB,YAAa;IACzC,KAAK,CAAC,WAAW,EAAE,KAAK,uBAAuB,EAAE,OAAO,KAAK,sCAAQ,EAChC,OAAO,aAAa,CAAC,EAAE,CAAC,CAAC;AAG9D,UAAM,IAAI,MAAM;GACnB,WACQ,UAAU,cAAc;IAG7B,KAAK,CAAC,WAAW,EAAE,KAAK,uBAAuB,EAAE,OAAO,KAAK,2LAAO,CAItC,CAAC;IAC/B,MAAM,UAAU;IAChB,eAAe,MAAM;GACxB,WACQ,aAAa,iBAElB,CAAC,aAAa,qBAAqB;IACnC,aAAa,sBAAsB;IACnC,KAAK,CAAC,WAAW,EAAE,KAAK,uBAAuB,EAAE,OAAO,KAAK,kJAAa,CAGf,CAAC;GAC/D;AAGL,OAAI,cAAc,sBAAsB,CAAC,OAAO,UAAU,MACtD;AACJ,OAAI,iBAAiB,aAAa,EAAE;IAEhC,MAAM,UAAU,aAAa,aAAa;IAC1C,MAAM,QAAQ,QAAQ;IACtB,SACI,OAAO,KAAK,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,eAAe,CAAC;GACnF,OACI;IAED,IAAI,mBAAmB,cAAc;AACrC,QAA+C,EAAE,WAAW,mBAAmB;KAC3E,KAAK,CAAC,WAAW,EAAE,KAAK,uBAAuB,EAAE,OAAO,KAAK,0LAA0L,CAAC,CAAC;KACzP,mBAAmB,QAAQ,QAAQ,iBAAiB;IACvD;IACD,OAAO,KAAK,MAAM,iBAAiB,KAAK,cAAY;AAChD,SAAI,CAAC,SACD,OAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,KAAK,MAAM,EAAE,OAAO,KAAK,CAAC,CAAC;KAC9E,MAAM,oBAAoB,WAAW,SAAS,GACxC,SAAS,UACT;KAEN,OAAO,KAAK,QAAQ;KAGpB,OAAO,WAAW,QAAQ;KAE1B,MAAM,UAAU,kBAAkB,aAAa;KAC/C,MAAM,QAAQ,QAAQ;AACtB,YAAQ,SACJ,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM,eAAe,EAAE;IACxE,EAAC,CAAC;GACN;EACJ;CACJ;AACD,QAAO;AACV;;;;;;AAMD,SAAS,kBAAkB,OAAO;AAC9B,QAAO,MAAM,QAAQ,MAAM,YAAU,OAAO,SAAS,GAC/C,QAAQ,uBAAO,IAAI,MAAM,uCAAuC,GAChE,QAAQ,IAAI,MAAM,QAAQ,IAAI,YAAU,OAAO,cAC7C,QAAQ,IAAI,OAAO,KAAK,OAAO,WAAW,CAAC,OAAO,CAAC,UAAU,SAAS;EAClE,MAAM,eAAe,OAAO,WAAW;AACvC,MAAI,OAAO,iBAAiB,cACxB,EAAE,iBAAiB,eACnB,SAAS,KAAK,cAAc,CAAC,KAAK,cAAY;AAC1C,OAAI,CAAC,SACD,QAAO,QAAQ,uBAAO,IAAI,MAAM,CAAC,4BAA4B,EAAE,KAAK,MAAM,EAAE,OAAO,KAAK,uDAAuD,CAAC,EAAE;GACtJ,MAAM,oBAAoB,WAAW,SAAS,GACxC,SAAS,UACT;GAEN,OAAO,KAAK,QAAQ;GAGpB,OAAO,WAAW,QAAQ;AAC1B;EACH,EAAC,CAAC;AAEP,SAAO;CACV,GAAE,CAAE,EAAC,CAAC,CAAC,CAAC,CAAC,KAAK,MAAM,MAAM;AACtC;;;;;;AASD,SAAS,QAAQ,OAAO;CACpB,MAAM,SAAS,OAAO,UAAU;CAChC,MAAM,eAAe,OAAO,iBAAiB;CAC7C,IAAI,cAAc;CAClB,IAAI,aAAa;CACjB,MAAM,QAAQ,SAAS,MAAM;EACzB,MAAM,KAAK,MAAM,MAAM,GAAG;AAC1B,MAAgD,CAAC,eAAe,OAAO,YAAa;AAChF,OAAI,CAAC,gBAAgB,GAAG,CACpB,KAAI,aACA,KAAK,CAAC,+CAA+C,CAAC,EAAE,IAAI,CAAC,gBAAgB,CAAC,EAAE,YAAY,CAAC,UAAU,CAAC,EAAE,MAAM;QAGhH,KAAK,CAAC,+CAA+C,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,MAAM;GAGxF,aAAa;GACb,cAAc;EACjB;AACD,SAAO,OAAO,QAAQ,GAAG;CAC5B,EAAC;CACF,MAAM,oBAAoB,SAAS,MAAM;EACrC,MAAM,EAAE,SAAS,GAAG,MAAM;EAC1B,MAAM,EAAE,QAAQ,GAAG;EACnB,MAAM,eAAe,QAAQ,SAAS;EACtC,MAAM,iBAAiB,aAAa;AACpC,MAAI,CAAC,gBAAgB,CAAC,eAAe,OACjC,QAAO;EACX,MAAM,QAAQ,eAAe,UAAU,kBAAkB,KAAK,MAAM,aAAa,CAAC;AAClF,MAAI,QAAQ,GACR,QAAO;EAEX,MAAM,mBAAmB,gBAAgB,QAAQ,SAAS,GAAG;AAC7D,SAEA,SAAS,KAIL,gBAAgB,aAAa,KAAK,oBAElC,eAAe,eAAe,SAAS,GAAG,SAAS,mBACjD,eAAe,UAAU,kBAAkB,KAAK,MAAM,QAAQ,SAAS,GAAG,CAAC,GAC3E;CACT,EAAC;CACF,MAAM,WAAW,SAAS,MAAM,kBAAkB,QAAQ,MACtD,eAAe,aAAa,QAAQ,MAAM,MAAM,OAAO,CAAC;CAC5D,MAAM,gBAAgB,SAAS,MAAM,kBAAkB,QAAQ,MAC3D,kBAAkB,UAAU,aAAa,QAAQ,SAAS,KAC1D,0BAA0B,aAAa,QAAQ,MAAM,MAAM,OAAO,CAAC;CACvE,SAAS,SAAS,IAAI,CAAE,GAAE;AACtB,MAAI,WAAW,EAAE,EAAE;GACf,MAAM,IAAI,OAAO,MAAM,MAAM,QAAQ,GAAG,YAAY,QAAQ,MAAM,MAAM,GAAG,CAE1E,CAAC,MAAM,KAAK;AACb,OAAI,MAAM,kBACN,OAAO,aAAa,eACpB,yBAAyB,UACzB,SAAS,oBAAoB,MAAM,EAAE;AAEzC,UAAO;EACV;AACD,SAAO,QAAQ,SAAS;CAC3B;AAED,KAA0E,WAAW;EACjF,MAAM,WAAW,oBAAoB;AACrC,MAAI,UAAU;GACV,MAAM,sBAAsB;IACxB,OAAO,MAAM;IACb,UAAU,SAAS;IACnB,eAAe,cAAc;IAC7B,OAAO;GACV;GAED,SAAS,iBAAiB,SAAS,kBAAkB,CAAE;GAEvD,SAAS,eAAe,KAAK,oBAAoB;GACjD,YAAY,MAAM;IACd,oBAAoB,QAAQ,MAAM;IAClC,oBAAoB,WAAW,SAAS;IACxC,oBAAoB,gBAAgB,cAAc;IAClD,oBAAoB,QAAQ,gBAAgB,MAAM,MAAM,GAAG,CAAC,GACtD,OACA;GACT,GAAE,EAAE,OAAO,OAAQ,EAAC;EACxB;CACJ;;;;AAID,QAAO;EACH;EACA,MAAM,SAAS,MAAM,MAAM,MAAM,KAAK;EACtC;EACA;EACA;CACH;AACJ;AACD,SAAS,kBAAkB,QAAQ;AAC/B,QAAO,OAAO,WAAW,IAAI,OAAO,KAAK;AAC5C;AACD,MAAM,iCAA+B,gBAAgB;CACjD,MAAM;CACN,cAAc,EAAE,MAAM,EAAG;CACzB,OAAO;EACH,IAAI;GACA,MAAM,CAAC,QAAQ,MAAO;GACtB,UAAU;EACb;EACD,SAAS;EACT,aAAa;EAEb,kBAAkB;EAClB,QAAQ;EACR,kBAAkB;GACd,MAAM;GACN,SAAS;EACZ;EACD,gBAAgB;CACnB;CACD;CACA,MAAM,OAAO,EAAE,OAAO,EAAE;EACpB,MAAM,OAAO,SAAS,QAAQ,MAAM,CAAC;EACrC,MAAM,EAAE,SAAS,GAAG,OAAO,UAAU;EACrC,MAAM,UAAU,SAAS,OAAO;IAC3B,aAAa,MAAM,aAAa,QAAQ,iBAAiB,qBAAqB,GAAG,KAAK;IAMtF,aAAa,MAAM,kBAAkB,QAAQ,sBAAsB,2BAA2B,GAAG,KAAK;EAC1G,GAAE;AACH,SAAO,MAAM;GACT,MAAM,WAAW,MAAM,WAAW,kBAAkB,MAAM,QAAQ,KAAK,CAAC;AACxE,UAAO,MAAM,SACP,WACA,EAAE,KAAK;IACL,gBAAgB,KAAK,gBACf,MAAM,mBACN;IACN,MAAM,KAAK;IAGX,SAAS,KAAK;IACd,OAAO,QAAQ;GAClB,GAAE,SAAS;EACnB;CACJ;AACJ,EAAC;;;;AAMF,MAAM,aAAa;AACnB,SAAS,WAAW,GAAG;AAEnB,KAAI,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,SACxC;AAEJ,KAAI,EAAE,iBACF;AAEJ,KAAI,EAAE,WAAW,UAAa,EAAE,WAAW,EACvC;AAGJ,KAAI,EAAE,iBAAiB,EAAE,cAAc,cAAc;EAEjD,MAAM,SAAS,EAAE,cAAc,aAAa,SAAS;AACrD,MAAI,cAAc,KAAK,OAAO,CAC1B;CACP;AAED,KAAI,EAAE,gBACF,EAAE,gBAAgB;AACtB,QAAO;AACV;AACD,SAAS,eAAe,OAAO,OAAO;AAClC,MAAK,MAAM,OAAO,OAAO;EACrB,MAAM,aAAa,MAAM;EACzB,MAAM,aAAa,MAAM;AACzB,MAAI,OAAO,eAAe,UACtB;OAAI,eAAe,WACf,QAAO;EAAM,WAGb,CAAC,QAAQ,WAAW,IACpB,WAAW,WAAW,WAAW,UACjC,WAAW,KAAK,CAAC,OAAO,MAAM,UAAU,WAAW,GAAG,CACtD,QAAO;CAElB;AACD,QAAO;AACV;;;;;AAKD,SAAS,gBAAgB,QAAQ;AAC7B,QAAO,SAAU,OAAO,UAAU,OAAO,QAAQ,OAAO,OAAO,OAAQ;AAC1E;;;;;;;AAOD,MAAM,eAAe,CAAC,WAAW,aAAa,iBAAiB,aAAa,OACtE,YACA,eAAe,OACX,cACA;AAEV,MAAM,iCAA+B,gBAAgB;CACjD,MAAM;CAEN,cAAc;CACd,OAAO;EACH,MAAM;GACF,MAAM;GACN,SAAS;EACZ;EACD,OAAO;CACV;CAGD,cAAc,EAAE,MAAM,EAAG;CACzB,MAAM,OAAO,EAAE,OAAO,OAAO,EAAE;EACgB,qBAAqB;EAChE,MAAM,gBAAgB,OAAO,sBAAsB;EACnD,MAAM,iBAAiB,SAAS,MAAM,MAAM,SAAS,cAAc,MAAM;EACzE,MAAM,gBAAgB,OAAO,cAAc,EAAE;EAG7C,MAAM,QAAQ,SAAS,MAAM;GACzB,IAAI,eAAe,MAAM,cAAc;GACvC,MAAM,EAAE,SAAS,GAAG,eAAe;GACnC,IAAI;AACJ,WAAQ,eAAe,QAAQ,kBAC3B,CAAC,aAAa,YACd;AAEJ,UAAO;EACV,EAAC;EACF,MAAM,kBAAkB,SAAS,MAAM,eAAe,MAAM,QAAQ,MAAM,OAAO;EACjF,QAAQ,cAAc,SAAS,MAAM,MAAM,QAAQ,EAAE,CAAC;EACtD,QAAQ,iBAAiB,gBAAgB;EACzC,QAAQ,uBAAuB,eAAe;EAC9C,MAAM,UAAU,KAAK;EAGrB,MAAM,MAAM;GAAC,QAAQ;GAAO,gBAAgB;GAAO,MAAM;EAAK,GAAE,CAAC,CAAC,UAAU,IAAI,KAAK,EAAE,CAAC,aAAa,MAAM,QAAQ,KAAK;AAEpH,OAAI,IAAI;IAGJ,GAAG,UAAU,QAAQ;AAOrB,QAAI,QAAQ,SAAS,MAAM,YAAY,aAAa,aAAa;AAC7D,SAAI,CAAC,GAAG,YAAY,MAChB,GAAG,cAAc,KAAK;AAE1B,SAAI,CAAC,GAAG,aAAa,MACjB,GAAG,eAAe,KAAK;IAE9B;GACJ;AAED,OAAI,YACA,OAGC,CAAC,QAAQ,CAAC,kBAAkB,IAAI,KAAK,IAAI,CAAC,eAC1C,GAAG,eAAe,SAAS,CAAE,GAAE,QAAQ,cAAY,SAAS,SAAS,CAAC;EAE9E,GAAE,EAAE,OAAO,OAAQ,EAAC;AACrB,SAAO,MAAM;GACT,MAAM,QAAQ,eAAe;GAG7B,MAAM,cAAc,MAAM;GAC1B,MAAM,eAAe,gBAAgB;GACrC,MAAM,gBAAgB,gBAAgB,aAAa,WAAW;AAC9D,OAAI,CAAC,cACD,QAAO,cAAc,MAAM,SAAS;IAAE,WAAW;IAAe;GAAO,EAAC;GAG5E,MAAM,mBAAmB,aAAa,MAAM;GAC5C,MAAM,aAAa,mBACb,qBAAqB,OACjB,MAAM,SACN,OAAO,qBAAqB,aACxB,iBAAiB,MAAM,GACvB,mBACR;GACN,MAAM,mBAAmB,WAAS;AAE9B,QAAI,MAAM,UAAU,aAChB,aAAa,UAAU,eAAe;GAE7C;GACD,MAAM,YAAY,EAAE,eAAe,OAAO,CAAE,GAAE,YAAY,OAAO;IAC7D;IACA,KAAK;GACR,EAAC,CAAC;AACH,OACI,aACA,UAAU,KAAK;IAEf,MAAM,OAAO;KACT,OAAO,MAAM;KACb,MAAM,aAAa;KACnB,MAAM,aAAa;KACnB,MAAM,aAAa;IACtB;IACD,MAAM,oBAAoB,QAAQ,UAAU,IAAI,GAC1C,UAAU,IAAI,IAAI,OAAK,EAAE,EAAE,GAC3B,CAAC,UAAU,IAAI,CAAE;IACvB,kBAAkB,QAAQ,cAAY;KAElC,SAAS,iBAAiB;IAC7B,EAAC;GACL;AACD,UAGA,cAAc,MAAM,SAAS;IAAE,WAAW;IAAW;GAAO,EAAC,IACzD;EACP;CACJ;AACJ,EAAC;AACF,SAAS,cAAc,MAAM,MAAM;AAC/B,KAAI,CAAC,KACD,QAAO;CACX,MAAM,cAAc,KAAK,KAAK;AAC9B,QAAO,YAAY,WAAW,IAAI,YAAY,KAAK;AACtD;;;;AAMD,MAAM,aAAa;AAGnB,SAAS,sBAAsB;CAC3B,MAAM,WAAW,oBAAoB;CACrC,MAAM,aAAa,SAAS,UAAU,SAAS,OAAO,KAAK;CAC3D,MAAM,oBAAoB,SAAS,UAAU,SAAS,OAAO,WAAW,SAAS,OAAO,QAAQ;AAChG,KAAI,eACC,eAAe,eAAe,WAAW,SAAS,aAAa,KAChE,OAAO,sBAAsB,YAC7B,kBAAkB,SAAS,cAAc;EACzC,MAAM,OAAO,eAAe,cAAc,eAAe;EACzD,KAAK,CAGA;;;;GAAG,EAAE,KAAK,0CAAG,EAEP,KAAK,iBAAG,CACC,CAAC;CACxB;AACJ;;;;;;;;AASD,SAAS,oBAAoB,eAAe,SAAS;CACjD,MAAM,OAAO,OAAO,CAAE,GAAE,eAAe,EAEnC,SAAS,cAAc,QAAQ,IAAI,aAAW,KAAK,SAAS;EAAC;EAAa;EAAY;CAAU,EAAC,CAAC,CACrG,EAAC;AACF,QAAO,EACH,SAAS;EACL,MAAM;EACN,UAAU;EACV,SAAS,cAAc;EACvB;EACA,OAAO;CACV,EACJ;AACJ;AACD,SAAS,cAAc,SAAS;AAC5B,QAAO,EACH,SAAS,EACL,QACH,EACJ;AACJ;AAED,IAAI,WAAW;AACf,SAAS,YAAY,KAAK,QAAQ,SAAS;AAGvC,KAAI,OAAO,cACP;CACJ,OAAO,gBAAgB;CAEvB,MAAM,KAAK;CACX,oBAAoB;EAChB,IAAI,sBAAsB,KAAK,MAAM,KAAK;EAC1C,OAAO;EACP,aAAa;EACb,UAAU;EACV,MAAM;EACN,qBAAqB,CAAC,SAAU;EAChC;CACH,GAAE,SAAO;AACN,MAAI,OAAO,IAAI,QAAQ,YACnB,QAAQ,KAAK,wNAAwN;EAGzO,IAAI,GAAG,iBAAiB,CAAC,SAAS,QAAQ;AACtC,OAAI,QAAQ,cACR,QAAQ,aAAa,MAAM,KAAK;IAC5B,MAAM;IACN,KAAK;IACL,UAAU;IACV,OAAO,oBAAoB,OAAO,aAAa,OAAO,gBAAgB;GACzE,EAAC;EAET,EAAC;EAEF,IAAI,GAAG,mBAAmB,CAAC,EAAE,UAAU,MAAM,mBAAmB,KAAK;AACjE,OAAI,kBAAkB,gBAAgB;IAClC,MAAM,OAAO,kBAAkB;IAC/B,KAAK,KAAK,KAAK;KACX,QAAQ,KAAK,OAAO,GAAG,KAAK,KAAK,UAAU,CAAC,EAAE,CAAC,GAAG,MAAM,KAAK;KAC7D,WAAW;KACX,SAAS;KACT,iBAAiB;IACpB,EAAC;GACL;AAED,OAAI,QAAQ,kBAAkB,eAAe,EAAE;IAC3C,kBAAkB,gBAAgB;IAClC,kBAAkB,eAAe,QAAQ,kBAAgB;KACrD,IAAI,QAAQ,aAAa,MAAM;KAC/B,IAAI,kBAAkB;KACtB,IAAI,UAAU;KACd,IAAI,YAAY;AAChB,SAAI,aAAa,OAAO;MACpB,QAAQ,aAAa;MACrB,kBAAkB;MAClB,YAAY;KACf,WACQ,aAAa,eAAe;MACjC,kBAAkB;MAClB,UAAU;KACb,WACQ,aAAa,UAAU;MAC5B,kBAAkB;MAClB,UAAU;KACb;KACD,KAAK,KAAK,KAAK;MACX;MACA;MACA;MACA;KACH,EAAC;IACL,EAAC;GACL;EACJ,EAAC;EACF,MAAM,OAAO,cAAc,MAAM;GAE7B,mBAAmB;GACnB,IAAI,uBAAuB;GAC3B,IAAI,kBAAkB,kBAAkB;GACxC,IAAI,mBAAmB,kBAAkB;EAC5C,EAAC;EACF,MAAM,qBAAqB,wBAAwB;EACnD,IAAI,iBAAiB;GACjB,IAAI;GACJ,OAAO,CAAC,MAAM,EAAE,KAAK,MAAM,KAAK,GAAG,YAAY,CAAC;GAChD,OAAO;EACV,EAAC;EAOF,OAAO,QAAQ,CAAC,OAAO,OAAO;GAC1B,IAAI,iBAAiB;IACjB,SAAS;IACT,OAAO;KACH,OAAO;KACP,UAAU,GAAG;KACb,SAAS;KACT,MAAM,IAAI,KAAK;KACf,MAAM,EAAE,MAAO;KACf,SAAS,GAAG,KAAK;IACpB;GACJ,EAAC;EACL,EAAC;EAEF,IAAI,eAAe;EACnB,OAAO,WAAW,CAAC,IAAI,SAAS;GAC5B,MAAM,OAAO;IACT,OAAO,cAAc,aAAa;IAClC,MAAM,oBAAoB,MAAM,0CAA0C;IAC1E,IAAI,oBAAoB,IAAI,kBAAkB;GACjD;GAED,OAAO,eAAe,GAAG,MAAM,kBAAkB,EAC7C,OAAO,eACV,EAAC;GACF,IAAI,iBAAiB;IACjB,SAAS;IACT,OAAO;KACH,MAAM,IAAI,KAAK;KACf,OAAO;KACP,UAAU,GAAG;KACb;KACA,SAAS,GAAG,KAAK;IACpB;GACJ,EAAC;EACL,EAAC;EACF,OAAO,UAAU,CAAC,IAAI,MAAM,YAAY;GACpC,MAAM,OAAO,EACT,OAAO,cAAc,YAAY,CACpC;AACD,OAAI,SAAS;IACT,KAAK,UAAU,EACX,SAAS;KACL,MAAM;KACN,UAAU;KACV,SAAS,UAAU,QAAQ,UAAU;KACrC,SAAS;KACT,OAAO;IACV,EACJ;IACD,KAAK,SAAS,cAAc,IAAI;GACnC,OAEG,KAAK,SAAS,cAAc,IAAI;GAGpC,KAAK,OAAO,oBAAoB,MAAM,0CAA0C;GAChF,KAAK,KAAK,oBAAoB,IAAI,kBAAkB;GACpD,IAAI,iBAAiB;IACjB,SAAS;IACT,OAAO;KACH,OAAO;KACP,UAAU,GAAG;KACb,MAAM,IAAI,KAAK;KACf;KACA,SAAS,UAAU,YAAY;KAC/B,SAAS,GAAG,KAAK;IACpB;GACJ,EAAC;EACL,EAAC;;;;EAIF,MAAM,oBAAoB,sBAAsB;EAChD,IAAI,aAAa;GACb,IAAI;GACJ,OAAO,YAAY,KAAK,MAAM,KAAK;GACnC,MAAM;GACN,uBAAuB;EAC1B,EAAC;EACF,SAAS,oBAAoB;AAEzB,OAAI,CAAC,oBACD;GACJ,MAAM,UAAU;GAEhB,IAAI,SAAS,QAAQ,WAAW,CAAC,OAAO,WAAS,CAAC,MAAM,UAGpD,CAAC,MAAM,OAAO,OAAO,WAAW;GAEpC,OAAO,QAAQ,6BAA6B;AAE5C,OAAI,QAAQ,QACR,SAAS,OAAO,OAAO,WAEvB,gBAAgB,OAAO,QAAQ,OAAO,aAAa,CAAC,CAAC;GAGzD,OAAO,QAAQ,WAAS,sBAAsB,OAAO,OAAO,aAAa,MAAM,CAAC;GAChF,QAAQ,YAAY,OAAO,IAAI,8BAA8B;EAChE;EACD,IAAI;EACJ,IAAI,GAAG,iBAAiB,aAAW;GAC/B,sBAAsB;AACtB,OAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAC/C,mBAAmB;EAE1B,EAAC;;;;EAIF,IAAI,GAAG,kBAAkB,aAAW;AAChC,OAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAAmB;IAClE,MAAM,SAAS,QAAQ,WAAW;IAClC,MAAM,QAAQ,OAAO,KAAK,aAASC,QAAM,OAAO,YAAY,QAAQ,OAAO;AAC3E,QAAI,OACA,QAAQ,QAAQ,EACZ,SAAS,0CAA0C,MAAM,CAC5D;GAER;EACJ,EAAC;EACF,IAAI,kBAAkB,kBAAkB;EACxC,IAAI,mBAAmB,kBAAkB;CAC5C,EAAC;AACL;AACD,SAAS,eAAe,KAAK;AACzB,KAAI,IAAI,SACJ,QAAO,IAAI,aAAa,MAAM;KAG9B,QAAO,IAAI,aAAa,MAAM;AAErC;AACD,SAAS,0CAA0C,OAAO;CACtD,MAAM,EAAE,QAAQ,GAAG;CACnB,MAAM,SAAS,CACX;EAAE,UAAU;EAAO,KAAK;EAAQ,OAAO,OAAO;CAAM,CACvD;AACD,KAAI,OAAO,QAAQ,MACf,OAAO,KAAK;EACR,UAAU;EACV,KAAK;EACL,OAAO,OAAO;CACjB,EAAC;CAEN,OAAO,KAAK;EAAE,UAAU;EAAO,KAAK;EAAU,OAAO,MAAM;CAAI,EAAC;AAChE,KAAI,MAAM,KAAK,QACX,OAAO,KAAK;EACR,UAAU;EACV,KAAK;EACL,OAAO,EACH,SAAS;GACL,MAAM;GACN,UAAU;GACV,SAAS,MAAM,KACV,IAAI,SAAO,GAAG,IAAI,OAAO,eAAe,IAAI,EAAE,CAAC,CAC/C,KAAK,IAAI;GACd,SAAS;GACT,OAAO,MAAM;EAChB,EACJ;CACJ,EAAC;AAEN,KAAI,OAAO,YAAY,MACnB,OAAO,KAAK;EACR,UAAU;EACV,KAAK;EACL,OAAO,OAAO;CACjB,EAAC;AAEN,KAAI,MAAM,MAAM,QACZ,OAAO,KAAK;EACR,UAAU;EACV,KAAK;EACL,OAAO,MAAM,MAAM,IAAI,WAAS,MAAM,OAAO,KAAK;CACrD,EAAC;AAEN,KAAI,OAAO,KAAK,MAAM,OAAO,KAAK,CAAC,QAC/B,OAAO,KAAK;EACR,UAAU;EACV,KAAK;EACL,OAAO,MAAM,OAAO;CACvB,EAAC;CAEN,OAAO,KAAK;EACR,KAAK;EACL,UAAU;EACV,OAAO,EACH,SAAS;GACL,MAAM;GACN,UAAU;GACV,SAAS,MAAM,MAAM,IAAI,WAAS,MAAM,KAAK,KAAK,CAAC,CAAC,KAAK,MAAM;GAC/D,SAAS;GACT,OAAO,MAAM;EAChB,EACJ;CACJ,EAAC;AACF,QAAO;AACV;;;;AAID,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,aAAa;AAEnB,MAAM,OAAO;AACb,MAAM,UAAU;AAChB,MAAM,UAAU;AAChB,SAAS,8BAA8B,OAAO;CAC1C,MAAM,OAAO,CAAE;CACf,MAAM,EAAE,QAAQ,GAAG;AACnB,KAAI,OAAO,QAAQ,MACf,KAAK,KAAK;EACN,OAAO,OAAO,OAAO,KAAK;EAC1B,WAAW;EACX,iBAAiB;CACpB,EAAC;AAEN,KAAI,OAAO,SACP,KAAK,KAAK;EACN,OAAO;EACP,WAAW;EACX,iBAAiB;CACpB,EAAC;AAEN,KAAI,MAAM,YACN,KAAK,KAAK;EACN,OAAO;EACP,WAAW;EACX,iBAAiB;CACpB,EAAC;AAEN,KAAI,MAAM,kBACN,KAAK,KAAK;EACN,OAAO;EACP,WAAW;EACX,iBAAiB;CACpB,EAAC;AAEN,KAAI,MAAM,aACN,KAAK,KAAK;EACN,OAAO;EACP,WAAW;EACX,iBAAiB;CACpB,EAAC;AAEN,KAAI,OAAO,UACP,KAAK,KAAK;EACN,OAAO,OAAO,OAAO,aAAa,WAC5B,CAAC,UAAU,EAAE,OAAO,UAAU,GAC9B;EACN,WAAW;EACX,iBAAiB;CACpB,EAAC;CAIN,IAAI,KAAK,OAAO;AAChB,KAAI,MAAM,MAAM;EACZ,KAAK,OAAO,gBAAgB;EAC5B,OAAO,UAAU;CACpB;AACD,QAAO;EACH;EACA,OAAO,OAAO;EACd;EACA,UAAU,MAAM,SAAS,IAAI,8BAA8B;CAC9D;AACJ;AAED,IAAI,gBAAgB;AACpB,MAAM,oBAAoB;AAC1B,SAAS,sBAAsB,OAAO,cAAc;CAGhD,MAAM,gBAAgB,aAAa,QAAQ,UACvC,kBAAkB,aAAa,QAAQ,aAAa,QAAQ,SAAS,IAAI,MAAM,OAAO;CAC1F,MAAM,mBAAmB,MAAM,cAAc;AAC7C,KAAI,CAAC,eACD,MAAM,cAAc,aAAa,QAAQ,KAAK,WAAS,kBAAkB,OAAO,MAAM,OAAO,CAAC;CAElG,MAAM,SAAS,QAAQ,gBAAc,sBAAsB,YAAY,aAAa,CAAC;AACxF;AACD,SAAS,6BAA6B,OAAO;CACzC,MAAM,aAAa;CACnB,MAAM,SAAS,QAAQ,6BAA6B;AACvD;AACD,SAAS,gBAAgB,OAAO,QAAQ;CACpC,MAAM,QAAQ,OAAO,MAAM,GAAG,CAAC,MAAM,kBAAkB;CACvD,MAAM,aAAa;AACnB,KAAI,CAAC,SAAS,MAAM,SAAS,EACzB,QAAO;CAGX,MAAM,cAAc,IAAI,OAAO,MAAM,GAAG,QAAQ,OAAO,GAAG,EAAE,MAAM;AAClE,KAAI,YAAY,KAAK,OAAO,EAAE;EAE1B,MAAM,SAAS,QAAQ,WAAS,gBAAgB,OAAO,OAAO,CAAC;AAE/D,MAAI,MAAM,OAAO,SAAS,OAAO,WAAW,KAAK;GAC7C,MAAM,aAAa,MAAM,GAAG,KAAK,OAAO;AACxC,UAAO;EACV;AAED,SAAO;CACV;CACD,MAAM,OAAO,MAAM,OAAO,KAAK,aAAa;CAC5C,MAAM,cAAc,OAAO,KAAK;AAEhC,KAAI,CAAC,OAAO,WAAW,IAAI,KACtB,YAAY,SAAS,OAAO,IAAI,KAAK,SAAS,OAAO,EACtD,QAAO;AACX,KAAI,YAAY,WAAW,OAAO,IAAI,KAAK,WAAW,OAAO,CACzD,QAAO;AACX,KAAI,MAAM,OAAO,QAAQ,OAAO,MAAM,OAAO,KAAK,CAAC,SAAS,OAAO,CAC/D,QAAO;AACX,QAAO,MAAM,SAAS,KAAK,WAAS,gBAAgB,OAAO,OAAO,CAAC;AACtE;AACD,SAAS,KAAK,KAAK,MAAM;CACrB,MAAM,MAAM,CAAE;AACd,MAAK,MAAM,OAAO,IACd,KAAI,CAAC,KAAK,SAAS,IAAI,EAEnB,IAAI,OAAO,IAAI;AAGvB,QAAO;AACV;;;;;;AAOD,SAAS,aAAa,SAAS;CAC3B,MAAM,UAAU,oBAAoB,QAAQ,QAAQ,QAAQ;CAC5D,MAAM,eAAe,QAAQ,cAAc;CAC3C,MAAM,mBAAmB,QAAQ,kBAAkB;CACnD,MAAM,gBAAgB,QAAQ;AAC9B,KAA+C,CAAC,cAC5C,OAAM,IAAI,MAAM;CAEpB,MAAM,eAAe,cAAc;CACnC,MAAM,sBAAsB,cAAc;CAC1C,MAAM,cAAc,cAAc;CAClC,MAAM,eAAe,WAAW,0BAA0B;CAC1D,IAAI,kBAAkB;AAEtB,KAAI,aAAa,QAAQ,kBAAkB,uBAAuB,SAC9D,QAAQ,oBAAoB;CAEhC,MAAM,kBAAkB,cAAc,KAAK,MAAM,gBAAc,KAAK,WAAW;CAC/E,MAAM,eAAe,cAAc,KAAK,MAAM,YAAY;CAC1D,MAAM,eAEN,cAAc,KAAK,MAAM,OAAO;CAChC,SAAS,SAAS,eAAe,OAAO;EACpC,IAAI;EACJ,IAAI;AACJ,MAAI,YAAY,cAAc,EAAE;GAC5B,SAAS,QAAQ,iBAAiB,cAAc;AAChD,OAA+C,CAAC,QAC5C,KAAK,CAAC,cAAc,EAAE,OAAO,cAAc,CAAC,mCAAmC,CAAC,EAAE,MAAM;GAE5F,SAAS;EACZ,OAEG,SAAS;AAEb,SAAO,QAAQ,SAAS,QAAQ,OAAO;CAC1C;CACD,SAAS,YAAY,MAAM;EACvB,MAAM,gBAAgB,QAAQ,iBAAiB,KAAK;AACpD,MAAI,eACA,QAAQ,YAAY,cAAc;OAGlC,KAAK,CAAC,kCAAkC,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC;CAEjE;CACD,SAAS,YAAY;AACjB,SAAO,QAAQ,WAAW,CAAC,IAAI,kBAAgB,aAAa,OAAO;CACtE;CACD,SAAS,SAAS,MAAM;AACpB,SAAO,CAAC,CAAC,QAAQ,iBAAiB,KAAK;CAC1C;CACD,SAAS,QAAQ,aAAa,iBAAiB;EAI3C,kBAAkB,OAAO,CAAE,GAAE,mBAAmB,aAAa,MAAM;AACnE,MAAI,OAAO,gBAAgB,UAAU;GACjC,MAAM,qBAAqB,SAAS,cAAc,aAAa,gBAAgB,KAAK;GACpF,MAAMC,iBAAe,QAAQ,QAAQ,EAAE,MAAM,mBAAmB,KAAM,GAAE,gBAAgB;GACxF,MAAMC,SAAO,cAAc,WAAW,mBAAmB,SAAS;AAE9D,OAAIA,OAAK,WAAW,KAAK,EACrB,KAAK,CAAC,UAAU,EAAE,YAAY,eAAe,EAAEA,OAAK,0DAA0D,CAAC,CAAC;YAC3G,CAACD,eAAa,QAAQ,QAC3B,KAAK,CAAC,uCAAuC,EAAE,YAAY,CAAC,CAAC,CAAC;AAItE,UAAO,OAAO,oBAAoBA,gBAAc;IAC5C,QAAQ,aAAaA,eAAa,OAAO;IACzC,MAAM,OAAO,mBAAmB,KAAK;IACrC,gBAAgB;IAChB;GACH,EAAC;EACL;AACD,MAA+C,CAAC,gBAAgB,YAAY,EAAE;GAC1E,KAAK,CAAC,2FAA2F,CAAC,EAAE,YAAY;AAChH,UAAO,QAAQ,CAAE,EAAC;EACrB;EACD,IAAI;AAEJ,MAAI,YAAY,QAAQ,MAAM;AAC1B,OACI,YAAY,eACZ,EAAE,UAAU,gBAEZ,OAAO,KAAK,YAAY,OAAO,CAAC,QAChC,KAAK,CAAC,MAAM,EAAE,YAAY,KAAK,8FAA8F,CAAC,CAAC;GAEnI,kBAAkB,OAAO,CAAE,GAAE,aAAa,EACtC,MAAM,SAAS,cAAc,YAAY,MAAM,gBAAgB,KAAK,CAAC,KACxE,EAAC;EACL,OACI;GAED,MAAM,eAAe,OAAO,CAAE,GAAE,YAAY,OAAO;AACnD,QAAK,MAAM,OAAO,aACd,KAAI,aAAa,QAAQ,MACrB,OAAO,aAAa;GAI5B,kBAAkB,OAAO,CAAE,GAAE,aAAa,EACtC,QAAQ,aAAa,aAAa,CACrC,EAAC;GAGF,gBAAgB,SAAS,aAAa,gBAAgB,OAAO;EAChE;EACD,MAAM,eAAe,QAAQ,QAAQ,iBAAiB,gBAAgB;EACtE,MAAM,OAAO,YAAY,QAAQ;AACjC,MAA+C,QAAQ,CAAC,KAAK,WAAW,IAAI,EACxE,KAAK,CAAC,gEAAgE,EAAE,KAAK,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC;EAIrG,aAAa,SAAS,gBAAgB,aAAa,aAAa,OAAO,CAAC;EACxE,MAAM,WAAW,aAAa,kBAAkB,OAAO,CAAE,GAAE,aAAa;GACpE,MAAM,WAAW,KAAK;GACtB,MAAM,aAAa;EACtB,EAAC,CAAC;EACH,MAAM,OAAO,cAAc,WAAW,SAAS;AAE3C,MAAI,KAAK,WAAW,KAAK,EACrB,KAAK,CAAC,UAAU,EAAE,YAAY,eAAe,EAAE,KAAK,0DAA0D,CAAC,CAAC;WAE3G,CAAC,aAAa,QAAQ,QAC3B,KAAK,CAAC,uCAAuC,EAAE,YAAY,QAAQ,OAAO,YAAY,OAAO,YAAY,CAAC,CAAC,CAAC;AAGpH,SAAO,OAAO;GACV;GAGA;GACA,OAMA,qBAAqB,iBACf,eAAe,YAAY,MAAM,GAChC,YAAY,SAAS,CAAE;EACjC,GAAE,cAAc;GACb,gBAAgB;GAChB;EACH,EAAC;CACL;CACD,SAAS,iBAAiB,IAAI;AAC1B,SAAO,OAAO,OAAO,WACf,SAAS,cAAc,IAAI,aAAa,MAAM,KAAK,GACnD,OAAO,CAAE,GAAE,GAAG;CACvB;CACD,SAAS,wBAAwB,IAAI,MAAM;AACvC,MAAI,oBAAoB,GACpB,QAAO,kBAAkB,GAAyC;GAC9D;GACA;EACH,EAAC;CAET;CACD,SAAS,KAAK,IAAI;AACd,SAAO,iBAAiB,GAAG;CAC9B;CACD,SAAS,QAAQ,IAAI;AACjB,SAAO,KAAK,OAAO,iBAAiB,GAAG,EAAE,EAAE,SAAS,KAAM,EAAC,CAAC;CAC/D;CACD,SAAS,qBAAqB,IAAI;EAC9B,MAAM,cAAc,GAAG,QAAQ,GAAG,QAAQ,SAAS;AACnD,MAAI,eAAe,YAAY,UAAU;GACrC,MAAM,EAAE,UAAU,GAAG;GACrB,IAAI,oBAAoB,OAAO,aAAa,aAAa,SAAS,GAAG,GAAG;AACxE,OAAI,OAAO,sBAAsB,UAAU;IACvC,oBACI,kBAAkB,SAAS,IAAI,IAAI,kBAAkB,SAAS,IAAI,GAC3D,oBAAoB,iBAAiB,kBAAkB,GAEtD,EAAE,MAAM,kBAAmB;IAGvC,kBAAkB,SAAS,CAAE;GAChC;AACD,OACI,kBAAkB,QAAQ,QAC1B,EAAE,UAAU,oBAAoB;IAChC,KAAK,CAAC,yBAAyB,EAAE,KAAK,UAAU,mBAAmB,MAAM,EAAE,CAAC,uBAAuB,EAAE,GAAG,SAAS,yEAAyE,CAAC,CAAC;AAC5L,UAAM,IAAI,MAAM;GACnB;AACD,UAAO,OAAO;IACV,OAAO,GAAG;IACV,MAAM,GAAG;IAET,QAAQ,kBAAkB,QAAQ,OAAO,CAAE,IAAG,GAAG;GACpD,GAAE,kBAAkB;EACxB;CACJ;CACD,SAAS,iBAAiB,IAAI,gBAAgB;EAC1C,MAAM,iBAAkB,kBAAkB,QAAQ,GAAG;EACrD,MAAM,OAAO,aAAa;EAC1B,MAAM,OAAO,GAAG;EAChB,MAAM,QAAQ,GAAG;EAEjB,MAAML,YAAU,GAAG,YAAY;EAC/B,MAAM,iBAAiB,qBAAqB,eAAe;AAC3D,MAAI,eACA,QAAO,iBAAiB,OAAO,iBAAiB,eAAe,EAAE;GAC7D,OAAO,OAAO,mBAAmB,WAC3B,OAAO,CAAE,GAAE,MAAM,eAAe,MAAM,GACtC;GACN;GACA;EACH,EAAC,EAEF,kBAAkB,eAAe;EAErC,MAAM,aAAa;EACnB,WAAW,iBAAiB;EAC5B,IAAI;AACJ,MAAI,CAAC,SAAS,oBAAoB,kBAAkB,MAAM,eAAe,EAAE;GACvE,UAAU,kBAAkB,IAA2C;IAAE,IAAI;IAAY;GAAM,EAAC;GAEhG,aAAa,MAAM,MAGnB,MAGA,MAAM;EACT;AACD,UAAQ,UAAU,QAAQ,QAAQ,QAAQ,GAAG,SAAS,YAAY,KAAK,EAClE,MAAM,CAAC,UAAU,oBAAoB,MAAM,GAExC,oBAAoB,OAAO,EAA6C,GAClE,QACA,YAAY,MAAM,GAExB,aAAa,OAAO,YAAY,KAAK,CAAC,CACzC,KAAK,CAACO,cAAY;AACnB,OAAIA,WACA;QAAI,oBAAoBA,WAAS,EAA6C,EAAE;AAC5E,SAEI,oBAAoB,kBAAkB,QAAQA,UAAQ,GAAG,EAAE,WAAW,IAEtE,mBAEC,eAAe,SAAS,eAAe,SAEhC,eAAe,SAAS,IAC1B,KAAK,IAAI;MACf,KAAK,CAAC,gFAAgF,EAAE,KAAK,SAAS,MAAM,EAAE,WAAW,SAAS,uPAAuP,CAAC,CAAC;AAC3X,aAAO,QAAQ,uBAAO,IAAI,MAAM,yCAAyC;KAC5E;AACD,YAAO,iBAEP,OAAO,EAEH,mBACH,GAAE,iBAAiBA,UAAQ,GAAG,EAAE;MAC7B,OAAO,OAAOA,UAAQ,OAAO,WACvB,OAAO,CAAE,GAAE,MAAMA,UAAQ,GAAG,MAAM,GAClC;MACN;KACH,EAAC,EAEF,kBAAkB,WAAW;IAChC;UAIDA,YAAU,mBAAmB,YAAY,MAAM,MAAMP,WAAS,KAAK;GAEvE,iBAAiB,YAAY,MAAMO,UAAQ;AAC3C,UAAOA;EACV,EAAC;CACL;;;;;;CAMD,SAAS,iCAAiC,IAAI,MAAM;EAChD,MAAM,QAAQ,wBAAwB,IAAI,KAAK;AAC/C,SAAO,QAAQ,QAAQ,OAAO,MAAM,GAAG,QAAQ,SAAS;CAC3D;CACD,SAAS,eAAe,IAAI;EACxB,MAAM,MAAM,cAAc,QAAQ,CAAC,MAAM,CAAC;AAE1C,SAAO,OAAO,OAAO,IAAI,mBAAmB,aACtC,IAAI,eAAe,GAAG,GACtB,IAAI;CACb;CAED,SAAS,SAAS,IAAI,MAAM;EACxB,IAAI;EACJ,MAAM,CAAC,gBAAgB,iBAAiB,gBAAgB,GAAG,uBAAuB,IAAI,KAAK;EAE3F,SAAS,wBAAwB,eAAe,SAAS,EAAE,oBAAoB,IAAI,KAAK;AAExF,OAAK,MAAM,UAAU,gBACjB,OAAO,YAAY,QAAQ,WAAS;GAChC,OAAO,KAAK,iBAAiB,OAAO,IAAI,KAAK,CAAC;EACjD,EAAC;EAEN,MAAM,0BAA0B,iCAAiC,KAAK,MAAM,IAAI,KAAK;EACrF,OAAO,KAAK,wBAAwB;AAEpC,SAAQ,cAAc,OAAO,CACxB,KAAK,MAAM;GAEZ,SAAS,CAAE;AACX,QAAK,MAAM,SAAS,aAAa,MAAM,EACnC,OAAO,KAAK,iBAAiB,OAAO,IAAI,KAAK,CAAC;GAElD,OAAO,KAAK,wBAAwB;AACpC,UAAO,cAAc,OAAO;EAC/B,EAAC,CACG,KAAK,MAAM;GAEZ,SAAS,wBAAwB,iBAAiB,qBAAqB,IAAI,KAAK;AAChF,QAAK,MAAM,UAAU,iBACjB,OAAO,aAAa,QAAQ,WAAS;IACjC,OAAO,KAAK,iBAAiB,OAAO,IAAI,KAAK,CAAC;GACjD,EAAC;GAEN,OAAO,KAAK,wBAAwB;AAEpC,UAAO,cAAc,OAAO;EAC/B,EAAC,CACG,KAAK,MAAM;GAEZ,SAAS,CAAE;AACX,QAAK,MAAM,UAAU,gBAEjB,KAAI,OAAO,YACP,KAAI,QAAQ,OAAO,YAAY,CAC3B,MAAK,MAAM,eAAe,OAAO,aAC7B,OAAO,KAAK,iBAAiB,aAAa,IAAI,KAAK,CAAC;QAGxD,OAAO,KAAK,iBAAiB,OAAO,aAAa,IAAI,KAAK,CAAC;GAIvE,OAAO,KAAK,wBAAwB;AAEpC,UAAO,cAAc,OAAO;EAC/B,EAAC,CACG,KAAK,MAAM;GAGZ,GAAG,QAAQ,QAAQ,YAAW,OAAO,iBAAiB,CAAE,EAAE;GAE1D,SAAS,wBAAwB,iBAAiB,oBAAoB,IAAI,MAAM,eAAe;GAC/F,OAAO,KAAK,wBAAwB;AAEpC,UAAO,cAAc,OAAO;EAC/B,EAAC,CACG,KAAK,MAAM;GAEZ,SAAS,CAAE;AACX,QAAK,MAAM,SAAS,oBAAoB,MAAM,EAC1C,OAAO,KAAK,iBAAiB,OAAO,IAAI,KAAK,CAAC;GAElD,OAAO,KAAK,wBAAwB;AACpC,UAAO,cAAc,OAAO;EAC/B,EAAC,CAEG,MAAM,SAAO,oBAAoB,KAAK,EAAwC,GAC7E,MACA,QAAQ,OAAO,IAAI,CAAC;CAC7B;CACD,SAAS,iBAAiB,IAAI,MAAM,SAAS;EAGzC,YACK,MAAM,CACN,QAAQ,WAAS,eAAe,MAAM,MAAM,IAAI,MAAM,QAAQ,CAAC,CAAC;CACxE;;;;;;CAMD,SAAS,mBAAmB,YAAY,MAAM,QAAQP,WAAS,MAAM;EAEjE,MAAM,QAAQ,wBAAwB,YAAY,KAAK;AACvD,MAAI,MACA,QAAO;EAEX,MAAM,oBAAoB,SAAS;EACnC,MAAM,QAAQ,CAAC,YAAY,CAAE,IAAG,QAAQ;AAGxC,MAAI,OAGA,KAAIA,aAAW,mBACX,cAAc,QAAQ,WAAW,UAAU,OAAO,EAC9C,QAAQ,qBAAqB,SAAS,MAAM,OAC/C,GAAE,KAAK,CAAC;OAET,cAAc,KAAK,WAAW,UAAU,KAAK;EAGrD,aAAa,QAAQ;EACrB,aAAa,YAAY,MAAM,QAAQ,kBAAkB;EACzD,aAAa;CAChB;CACD,IAAI;CAEJ,SAAS,iBAAiB;AAEtB,MAAI,sBACA;EACJ,wBAAwB,cAAc,OAAO,CAAC,IAAI,OAAO,SAAS;AAC9D,OAAI,CAAC,OAAO,UACR;GAEJ,MAAM,aAAa,QAAQ,GAAG;GAI9B,MAAM,iBAAiB,qBAAqB,WAAW;AACvD,OAAI,gBAAgB;IAChB,iBAAiB,OAAO,gBAAgB;KAAE,SAAS;KAAM,OAAO;IAAM,EAAC,EAAE,WAAW,CAAC,MAAM,KAAK;AAChG;GACH;GACD,kBAAkB;GAClB,MAAM,OAAO,aAAa;AAE1B,OAAI,WACA,mBAAmB,aAAa,KAAK,UAAU,KAAK,MAAM,EAAE,uBAAuB,CAAC;GAExF,SAAS,YAAY,KAAK,CACrB,MAAM,CAAC,UAAU;AAClB,QAAI,oBAAoB,OAAO,GAAgF,CAC3G,QAAO;AAEX,QAAI,oBAAoB,OAAO,EAA6C,EAAE;KAU1E,iBAAiB,OAAO,iBAAiB,MAAM,GAAG,EAAE,EAChD,OAAO,KACV,EAAC,EAAE,WAEH,CACI,KAAK,aAAW;AAIjB,UAAI,oBAAoB,SAAS,GACa,IAC1C,CAAC,KAAK,SACN,KAAK,SAAS,eAAe,KAC7B,cAAc,GAAG,IAAI,MAAM;KAElC,EAAC,CACG,MAAM,KAAK;AAEhB,YAAO,QAAQ,QAAQ;IAC1B;AAED,QAAI,KAAK,OACL,cAAc,GAAG,CAAC,KAAK,OAAO,MAAM;AAGxC,WAAO,aAAa,OAAO,YAAY,KAAK;GAC/C,EAAC,CACG,KAAK,CAAC,YAAY;IACnB,UACI,WACI,mBAEA,YAAY,MAAM,MAAM;AAEhC,QAAI,SACA;SAAI,KAAK,SAGL,CAAC,oBAAoB,SAAS,EAAwC,EACtE,cAAc,GAAG,CAAC,KAAK,OAAO,MAAM;cAE/B,KAAK,SAAS,eAAe,OAClC,oBAAoB,SAAS,GAAkF,EAG/G,cAAc,GAAG,IAAI,MAAM;IAC9B;IAEL,iBAAiB,YAAY,MAAM,QAAQ;GAC9C,EAAC,CAEG,MAAM,KAAK;EACnB,EAAC;CACL;CAED,IAAI,gBAAgB,cAAc;CAClC,IAAI,iBAAiB,cAAc;CACnC,IAAI;;;;;;;;;CASJ,SAAS,aAAa,OAAO,IAAI,MAAM;EACnC,YAAY,MAAM;EAClB,MAAM,OAAO,eAAe,MAAM;AAClC,MAAI,KAAK,QACL,KAAK,QAAQ,aAAW,QAAQ,OAAO,IAAI,KAAK,CAAC;OAEhD;GAEG,KAAK,0CAA0C;GAEnD,QAAQ,MAAM,MAAM;EACvB;AAED,SAAO,QAAQ,OAAO,MAAM;CAC/B;CACD,SAAS,UAAU;AACf,MAAI,SAAS,aAAa,UAAU,0BAChC,QAAO,QAAQ,SAAS;AAC5B,SAAO,IAAI,QAAQ,CAACQ,WAAS,WAAW;GACpC,cAAc,IAAI,CAACA,WAAS,MAAO,EAAC;EACvC;CACJ;CACD,SAAS,YAAY,KAAK;AACtB,MAAI,CAAC,OAAO;GAER,QAAQ,CAAC;GACT,gBAAgB;GAChB,cACK,MAAM,CACN,QAAQ,CAAC,CAACA,WAAS,OAAO,KAAM,MAAM,OAAO,IAAI,GAAGA,WAAS,CAAE;GACpE,cAAc,OAAO;EACxB;AACD,SAAO;CACV;CAED,SAAS,aAAa,IAAI,MAAM,QAAQ,mBAAmB;EACvD,MAAM,EAAE,gBAAgB,GAAG;AAC3B,MAAI,CAAC,aAAa,CAAC,eACf,QAAO,QAAQ,SAAS;EAC5B,MAAM,iBAAkB,CAAC,UAAU,uBAAuB,aAAa,GAAG,UAAU,EAAE,CAAC,KACjF,qBAAqB,CAAC,WACpB,QAAQ,SACR,QAAQ,MAAM,UAClB;AACJ,SAAO,UAAU,CACZ,KAAK,MAAM,eAAe,IAAI,MAAM,eAAe,CAAC,CACpD,KAAK,cAAY,YAAY,iBAAiB,SAAS,CAAC,CACxD,MAAM,SAAO,aAAa,KAAK,IAAI,KAAK,CAAC;CACjD;CACD,MAAM,KAAK,CAAC,UAAU,cAAc,GAAG,MAAM;CAC7C,IAAI;CACJ,MAAM,gCAAgB,IAAI;CAC1B,MAAM,SAAS;EACX;EACA,WAAW;EACX;EACA;EACA,aAAa,QAAQ;EACrB;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAM,MAAM,GAAG,GAAG;EAClB,SAAS,MAAM,GAAG,EAAE;EACpB,YAAY,aAAa;EACzB,eAAe,oBAAoB;EACnC,WAAW,YAAY;EACvB,SAAS,eAAe;EACxB;EACA,QAAQ,KAAK;GACT,MAAMC,WAAS;GACf,IAAI,UAAU,cAAc,WAAW;GACvC,IAAI,UAAU,cAAc,WAAW;GACvC,IAAI,OAAO,iBAAiB,UAAUA;GACtC,OAAO,eAAe,IAAI,OAAO,kBAAkB,UAAU;IACzD,YAAY;IACZ,KAAK,MAAM,MAAM,aAAa;GACjC,EAAC;AAIF,OAAI,aAGA,CAAC,WACD,aAAa,UAAU,2BAA2B;IAElD,UAAU;IACV,KAAK,cAAc,SAAS,CAAC,MAAM,SAAO;KAElC,KAAK,8CAA8C,IAAI;IAC9D,EAAC;GACL;GACD,MAAM,gBAAgB,CAAE;AACxB,QAAK,MAAM,OAAO,2BACd,OAAO,eAAe,eAAe,KAAK;IACtC,KAAK,MAAM,aAAa,MAAM;IAC9B,YAAY;GACf,EAAC;GAEN,IAAI,QAAQ,WAAWA,SAAO;GAC9B,IAAI,QAAQ,kBAAkB,gBAAgB,cAAc,CAAC;GAC7D,IAAI,QAAQ,uBAAuB,aAAa;GAChD,MAAM,aAAa,IAAI;GACvB,cAAc,IAAI,IAAI;GACtB,IAAI,UAAU,WAAY;IACtB,cAAc,OAAO,IAAI;AAEzB,QAAI,cAAc,OAAO,GAAG;KAExB,kBAAkB;KAClB,yBAAyB,uBAAuB;KAChD,wBAAwB;KACxB,aAAa,QAAQ;KACrB,UAAU;KACV,QAAQ;IACX;IACD,YAAY;GACf;AAED,OAA0E,WACtE,YAAY,KAAKA,UAAQ,QAAQ;EAExC;CACJ;CAED,SAAS,cAAc,QAAQ;AAC3B,SAAO,OAAO,OAAO,CAAC,SAAS,UAAU,QAAQ,KAAK,MAAM,eAAe,MAAM,CAAC,EAAE,QAAQ,SAAS,CAAC;CACzG;AACD,QAAO;AACV;AACD,SAAS,uBAAuB,IAAI,MAAM;CACtC,MAAM,iBAAiB,CAAE;CACzB,MAAM,kBAAkB,CAAE;CAC1B,MAAM,kBAAkB,CAAE;CAC1B,MAAM,MAAM,KAAK,IAAI,KAAK,QAAQ,QAAQ,GAAG,QAAQ,OAAO;AAC5D,MAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK;EAC1B,MAAM,aAAa,KAAK,QAAQ;AAChC,MAAI,WACA,KAAI,GAAG,QAAQ,KAAK,YAAU,kBAAkB,QAAQ,WAAW,CAAC,EAChE,gBAAgB,KAAK,WAAW;OAEhC,eAAe,KAAK,WAAW;EAEvC,MAAM,WAAW,GAAG,QAAQ;AAC5B,MAAI,UAEA;OAAI,CAAC,KAAK,QAAQ,KAAK,YAAU,kBAAkB,QAAQ,SAAS,CAAC,EACjE,gBAAgB,KAAK,SAAS;EACjC;CAER;AACD,QAAO;EAAC;EAAgB;EAAiB;CAAgB;AAC5D;;;;;AAMD,SAAS,YAAY;AACjB,QAAO,OAAO,UAAU;AAC3B;;;;;AAKD,SAAS,SAAS,OAAO;AACrB,QAAO,OAAO,iBAAiB;AAClC"}