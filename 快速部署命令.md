# 🚀 快速部署命令参考

## 📋 本地开发部署

### 1. 前置准备
```bash
# 安装Docker和Docker Compose
curl -fsSL https://get.docker.com | bash
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

### 2. 项目配置
```bash
# 克隆项目（如果从Git）
git clone <your-repo-url>
cd ai-knowledge-base

# 配置环境变量
cp .env.example .env
nano .env  # 编辑配置

# 安装前端依赖（用于移除console.log）
cd frontend-app
npm install
cd ..
```

### 3. 一键部署
```bash
# 给部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

## 🏗️ 手动部署步骤

### 1. 构建镜像
```bash
# 构建后端镜像
docker build -t ai-knowledge-backend ./backend

# 构建前端镜像
docker build -t ai-knowledge-frontend ./frontend-app
```

### 2. 启动服务
```bash
# 启动所有服务
docker-compose up -d

# 查看启动状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 验证部署
```bash
# 检查前端
curl http://localhost

# 检查后端API
curl http://localhost:8000/docs

# 检查数据库连接
docker-compose exec backend python -c "from app.core.database import engine; print('DB OK')"
```

## 🔧 常用维护命令

### 1. 服务管理
```bash
# 停止所有服务
docker-compose down

# 重启服务
docker-compose restart

# 重启特定服务
docker-compose restart backend

# 查看服务状态
docker-compose ps

# 查看资源使用
docker stats
```

### 2. 日志管理
```bash
# 查看所有日志
docker-compose logs

# 实时查看日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f postgres
docker-compose logs -f redis

# 查看最近50行日志
docker-compose logs --tail=50
```

### 3. 数据管理
```bash
# 进入数据库容器
docker-compose exec postgres psql -U postgres -d ai_knowledge_base

# 备份数据库
docker-compose exec postgres pg_dump -U postgres ai_knowledge_base > backup.sql

# 恢复数据库
docker-compose exec -T postgres psql -U postgres ai_knowledge_base < backup.sql

# 进入Redis容器
docker-compose exec redis redis-cli

# 查看上传文件
docker-compose exec backend ls -la /app/uploads
```

### 4. 更新部署
```bash
# 拉取最新代码
git pull

# 重新构建并部署
docker-compose up --build -d

# 仅重新构建特定服务
docker-compose up --build -d backend
```

## 🐛 故障排除命令

### 1. 诊断问题
```bash
# 查看容器状态
docker-compose ps

# 查看容器详细信息
docker inspect ai-knowledge-backend

# 进入容器调试
docker-compose exec backend bash
docker-compose exec frontend sh

# 查看网络连接
docker network ls
docker network inspect ai-knowledge_ai-network
```

### 2. 清理和重置
```bash
# 停止并删除容器
docker-compose down

# 删除所有相关容器和网络
docker-compose down --volumes --remove-orphans

# 清理未使用的镜像
docker system prune -f

# 完全重置（谨慎使用）
docker-compose down --volumes
docker system prune -a -f
```

### 3. 端口和网络问题
```bash
# 查看端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :8000

# 测试网络连通性
docker-compose exec frontend ping backend
docker-compose exec backend ping postgres

# 查看防火墙状态
ufw status  # Ubuntu
firewall-cmd --list-all  # CentOS
```

## 📊 监控命令

### 1. 性能监控
```bash
# 实时资源使用
docker stats

# 查看磁盘使用
df -h
docker system df

# 查看内存使用
free -h

# 查看CPU使用
top
htop
```

### 2. 健康检查
```bash
# 检查服务健康状态
curl -f http://localhost/api/health || echo "Backend unhealthy"
curl -f http://localhost || echo "Frontend unhealthy"

# 检查数据库连接
docker-compose exec postgres pg_isready -U postgres

# 检查Redis连接
docker-compose exec redis redis-cli ping
```

## 🔐 安全相关命令

### 1. 权限检查
```bash
# 检查文件权限
ls -la backend/uploads/
ls -la .env

# 修复权限问题
sudo chown -R $USER:$USER .
chmod 600 .env
```

### 2. 密钥管理
```bash
# 生成安全的密钥
openssl rand -hex 32

# 检查环境变量
docker-compose exec backend env | grep -E "(SECRET|KEY|PASSWORD)"
```

## 🚀 生产环境命令

### 1. 优化部署
```bash
# 生产环境部署（无构建缓存）
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d --no-cache

# 设置资源限制
docker-compose up -d --scale backend=2

# 启用自动重启
docker update --restart=unless-stopped $(docker-compose ps -q)
```

### 2. 备份策略
```bash
# 创建完整备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p backups/$DATE

# 备份数据库
docker-compose exec postgres pg_dump -U postgres ai_knowledge_base > backups/$DATE/database.sql

# 备份上传文件
tar -czf backups/$DATE/uploads.tar.gz backend/uploads/

# 备份配置文件
cp .env backups/$DATE/
cp docker-compose.yml backups/$DATE/

echo "备份完成: backups/$DATE"
```
