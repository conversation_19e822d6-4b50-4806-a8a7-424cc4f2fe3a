# AI模型配置指南

## 概述

本系统支持多种AI提供商，通过OpenAI兼容的API接口进行调用。配置方式为：**基础URL + 模型名称**。

## 支持的AI提供商

### 1. OpenAI
- **基础URL**: `https://api.openai.com/v1`
- **常用模型**:
  - `gpt-4` - GPT-4模型
  - `gpt-4-turbo` - GPT-4 Turbo
  - `gpt-3.5-turbo` - GPT-3.5 Turbo
- **API密钥**: 需要OpenAI官方API密钥

### 2. SiliconFlow (硅基流动)
- **基础URL**: `https://api.siliconflow.cn/v1`
- **常用模型**:
  - `Qwen/Qwen2.5-7B-Instruct` - 通义千问2.5
  - `Qwen/Qwen2.5-14B-Instruct` - 通义千问2.5 14B
  - `deepseek-ai/DeepSeek-V2.5` - DeepSeek V2.5
- **API密钥**: 需要SiliconFlow平台API密钥

### 3. DeepSeek
- **基础URL**: `https://api.deepseek.com/v1`
- **常用模型**:
  - `deepseek-chat` - DeepSeek Chat
  - `deepseek-coder` - DeepSeek Coder
- **API密钥**: 需要DeepSeek官方API密钥

### 4. Google Gemini
- **基础URL**: `https://generativelanguage.googleapis.com/v1beta`
- **常用模型**:
  - `gemini-2.5-flash` - Gemini 2.5 Flash
  - `gemini-pro` - Gemini Pro
- **API密钥**: 需要Google AI Studio API密钥

## 配置步骤

### 管理员配置

1. **添加AI提供商**:
   ```
   名称: openai
   显示名称: OpenAI
   基础URL: https://api.openai.com/v1
   描述: OpenAI官方API服务
   ```

2. **添加AI模型**:
   ```
   提供商: OpenAI
   模型名称: gpt-4
   显示名称: GPT-4
   最大Token: 8000
   系统API密钥: sk-xxx... (可选)
   ```

### 用户配置

1. **设置个人API密钥**:
   - 进入用户设置页面
   - 选择对应的AI提供商
   - 输入个人API密钥
   - 测试连接

2. **选择默认模型**:
   - 在聊天界面选择模型
   - 系统会记住您的选择

## API调用流程

1. **请求构建**:
   ```
   POST {base_url}/chat/completions
   Headers:
     Authorization: Bearer {api_key}
     Content-Type: application/json
   
   Body:
   {
     "model": "{model_name}",
     "messages": [...],
     "stream": true
   }
   ```

2. **响应处理**:
   - 支持流式响应
   - 自动解析SSE格式
   - 错误处理和重试

## 常见问题

### Q: 如何添加新的AI提供商？
A: 只要提供商支持OpenAI兼容的API格式，就可以添加：
1. 在管理员界面添加提供商，设置正确的基础URL
2. 添加对应的模型，设置正确的模型名称
3. 用户配置API密钥即可使用

### Q: 为什么某些模型无法使用？
A: 可能的原因：
1. API密钥无效或过期
2. 模型名称不正确
3. 基础URL配置错误
4. 提供商服务不可用

### Q: 如何测试模型连接？
A: 
1. 管理员可以在模型管理界面测试系统密钥
2. 用户可以在设置界面测试个人密钥
3. 系统会发送测试请求验证连接

## 技术实现

### 客户端适配
系统使用统一的OpenAI客户端，通过不同的base_url适配各种提供商：

```python
# SiliconFlow
client = AsyncOpenAI(
    api_key=api_key,
    base_url="https://api.siliconflow.cn/v1"
)

# Google Gemini
client = AsyncOpenAI(
    api_key=api_key,
    base_url="https://generativelanguage.googleapis.com/v1beta"
)
```

### 错误处理
系统会自动识别和处理各种错误：
- API密钥错误
- 模型不存在
- 配额不足
- 网络连接问题

### 优先级
API密钥使用优先级：
1. 用户个人密钥
2. 系统提供的密钥
3. 如果都没有，提示用户配置
