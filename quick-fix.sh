#!/bin/bash

echo "� AI知识库完整部署脚本"
echo "=========================="

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root用户运行此脚本"
    exit 1
fi

# 检查必要文件
echo "🔍 检查必要文件..."
if [ ! -f "docker-compose.prod.yml" ]; then
    echo "❌ docker-compose.prod.yml 文件不存在"
    exit 1
fi

if [ ! -f "nginx.prod.conf" ]; then
    echo "❌ nginx.prod.conf 文件不存在"
    exit 1
fi

if [ ! -d "ssl" ]; then
    echo "⚠️  ssl 目录不存在，将创建临时目录"
    mkdir -p ssl
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker compose down 2>/dev/null || true
docker compose -f docker-compose.prod.yml down 2>/dev/null || true

# 清理Docker资源
echo "🧹 清理Docker资源..."
docker system prune -f
docker builder prune -f

# 检查前端构建产物
if [ -d "frontend-app/dist" ]; then
    echo "✅ 发现已有前端构建产物"
else
    echo "🏗️ 使用Docker构建前端..."
    docker run --rm \
        -v "$(pwd)/frontend-app:/app" \
        -w /app \
        node:20-alpine \
        sh -c "npm ci && npm run build-only"

    if [ ! -d "frontend-app/dist" ]; then
        echo "❌ 前端构建失败"
        exit 1
    fi
fi

# 构建Docker镜像
echo "🐳 构建Docker镜像..."
docker compose -f docker-compose.prod.yml build --no-cache

# 启动服务
echo "🚀 启动服务..."
docker compose -f docker-compose.prod.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 90

# 检查服务状态
echo "📊 检查服务状态..."
docker compose -f docker-compose.prod.yml ps

# 检查服务健康状态
echo "🔍 检查服务健康状态..."
for i in {1..5}; do
    echo "尝试 $i/5: 检查服务响应..."

    # 检查HTTP
    if curl -s -o /dev/null -w "%{http_code}" http://localhost | grep -q "200\|301\|302"; then
        echo "✅ HTTP服务正常"
        break
    else
        echo "⏳ HTTP服务还未就绪，等待10秒..."
        sleep 10
    fi
done

# 显示服务日志
echo "📋 显示最近日志..."
echo "=== 前端日志 ==="
docker compose -f docker-compose.prod.yml logs frontend --tail=5
echo ""
echo "=== 后端日志 ==="
docker compose -f docker-compose.prod.yml logs backend --tail=5
echo ""
echo "=== 数据库日志 ==="
docker compose -f docker-compose.prod.yml logs postgres --tail=3

# 显示容器状态
echo ""
echo "📊 容器详细状态:"
docker compose -f docker-compose.prod.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"

echo ""
echo "🎉 部署完成！"
echo "=========================="
echo "🌐 访问地址:"
echo "  - HTTPS: https://aiknowledgebase.csicollege.cn"
echo "  - HTTP: http://aiknowledgebase.csicollege.cn"
echo ""
echo "📝 默认账户:"
echo "  - 管理员: admin / testpass123"
echo "  - 测试用户: testuser / testpass123"
echo ""
echo "🔧 管理命令:"
echo "  查看日志: docker compose -f docker-compose.prod.yml logs -f"
echo "  重启服务: docker compose -f docker-compose.prod.yml restart"
echo "  停止服务: docker compose -f docker-compose.prod.yml down"
echo ""
echo "🔍 故障排除:"
echo "  1. 如果无法访问，检查防火墙端口80和443"
echo "  2. 如果SSL错误，检查证书文件在ssl/目录下"
echo "  3. 查看详细日志: docker compose -f docker-compose.prod.yml logs -f [service_name]"
