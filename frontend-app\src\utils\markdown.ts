import { marked } from 'marked'
import hljs from 'highlight.js'
// 使用适合暗色模式的主题
import 'highlight.js/styles/atom-one-dark.css'

// 配置marked
marked.setOptions({
  highlight: function(code, lang) {
    const codeStr = String(code || '')
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(codeStr, { language: lang }).value
      } catch (err) {
        console.error('代码高亮失败:', err)
      }
    }
    return hljs.highlightAuto(codeStr).value
  },
  breaks: true,
  gfm: true
})

// 自定义渲染器
const renderer = new marked.Renderer()

// 自定义代码块渲染
renderer.code = function(token) {
  // 从token对象中提取属性
  const codeStr = String(token.text || '')
  const language = token.lang || ''
  const validLang = language && hljs.getLanguage(language) ? language : 'plaintext'
  const highlightedCode = hljs.highlight(codeStr, { language: validLang }).value

  return `
    <div class="code-block-container relative group rounded-xl overflow-hidden border border-gray-200 dark:border-gray-700 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 shadow-lg">
      <div class="code-block-header flex items-center justify-between bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 px-4 py-3 border-b border-gray-200 dark:border-gray-600">
        <div class="flex items-center space-x-2">
          <div class="flex space-x-1">
            <div class="w-3 h-3 rounded-full bg-red-400"></div>
            <div class="w-3 h-3 rounded-full bg-yellow-400"></div>
            <div class="w-3 h-3 rounded-full bg-green-400"></div>
          </div>
          <span class="text-sm font-medium text-gray-600 dark:text-gray-300">代码</span>
        </div>
        <button
          class="copy-code-btn opacity-0 group-hover:opacity-100 transition-all duration-200 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-3 py-1.5 rounded-lg text-xs font-medium shadow-md hover:shadow-lg transform hover:scale-105"
          onclick="copyCode(this)"
          data-code="${encodeURIComponent(codeStr)}"
        >
          <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
          </svg>
          复制
        </button>
      </div>
      <div class="relative">
        <pre class="hljs p-6 overflow-x-auto text-sm leading-relaxed bg-transparent"><code class="language-${validLang}">${highlightedCode}</code></pre>
        <div class="absolute bottom-3 right-3 bg-black/20 dark:bg-white/10 backdrop-blur-sm px-2 py-1 rounded-md">
          <span class="text-xs font-mono text-gray-600 dark:text-gray-300">${validLang}</span>
        </div>
      </div>
    </div>
  `
}

// 自定义行内代码渲染
renderer.codespan = function(token) {
  const codeStr = String(token.text || '')
  return `<code class="inline-code bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-md text-sm font-mono border border-blue-200 dark:border-blue-700/50">${codeStr}</code>`
}

// 自定义链接渲染
renderer.link = function(token) {
  const hrefStr = String(token.href || '')
  const titleStr = String(token.title || '')
  const textStr = this.parser.parseInline(token.tokens || [])
  return `<a href="${hrefStr}" title="${titleStr}" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:text-purple-600 dark:hover:text-purple-400 transition-colors duration-200 underline decoration-2 underline-offset-2 hover:decoration-purple-500">${textStr}</a>`
}

// 自定义表格渲染
renderer.table = function(token) {
  const headerStr = this.parser.parseBlock(token.header || [])
  const bodyStr = this.parser.parseBlock(token.rows || [])
  return `
    <div class="table-container overflow-x-auto my-6 rounded-xl border border-gray-200 dark:border-gray-700 shadow-lg">
      <table class="min-w-full">
        <thead class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/30 dark:to-purple-900/30">
          ${headerStr}
        </thead>
        <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
          ${bodyStr}
        </tbody>
      </table>
    </div>
  `
}

marked.use({ renderer })

/**
 * 渲染markdown文本为HTML
 */
export function renderMarkdown(text: string): string {
  try {
    return marked(text)
  } catch (error) {
    console.error('Markdown渲染失败:', error)
    console.error('输入文本:', text)
    return `<pre>${text}</pre>`
  }
}

// 全局复制代码函数
declare global {
  interface Window {
    copyCode: (button: HTMLButtonElement) => void
  }
}

window.copyCode = function(button: HTMLButtonElement) {
  const code = decodeURIComponent(button.getAttribute('data-code') || '')
  navigator.clipboard.writeText(code).then(() => {
    const originalText = button.innerHTML
    button.innerHTML = `
      <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
      </svg>
      已复制
    `
    button.style.background = 'linear-gradient(135deg, #10b981, #059669)'

    setTimeout(() => {
      button.innerHTML = originalText
      button.style.background = 'linear-gradient(135deg, #3b82f6, #8b5cf6)'
    }, 2000)
  }).catch(err => {
    console.error('复制失败:', err)
    button.innerHTML = '复制失败'
    setTimeout(() => {
      button.innerHTML = `
        <svg class="w-3 h-3 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
        </svg>
        复制
      `
    }, 2000)
  })
}

/**
 * 复制代码到剪贴板
 */
export function copyCode(button: HTMLButtonElement) {
  const code = decodeURIComponent(button.dataset.code || '')
  
  if (navigator.clipboard) {
    navigator.clipboard.writeText(code).then(() => {
      // 显示复制成功提示
      const originalText = button.textContent
      button.textContent = '已复制!'
      button.classList.add('bg-green-500')
      button.classList.remove('bg-blue-500')
      
      setTimeout(() => {
        button.textContent = originalText
        button.classList.remove('bg-green-500')
        button.classList.add('bg-blue-500')
      }, 2000)
    }).catch(err => {
      console.error('复制失败:', err)
      fallbackCopyTextToClipboard(code)
    })
  } else {
    fallbackCopyTextToClipboard(code)
  }
}

/**
 * 降级复制方法
 */
function fallbackCopyTextToClipboard(text: string) {
  const textArea = document.createElement('textarea')
  textArea.value = text
  textArea.style.position = 'fixed'
  textArea.style.left = '-999999px'
  textArea.style.top = '-999999px'
  document.body.appendChild(textArea)
  textArea.focus()
  textArea.select()
  
  try {
    document.execCommand('copy')
    console.log('使用降级方法复制成功')
  } catch (err) {
    console.error('降级复制方法也失败了:', err)
  }
  
  document.body.removeChild(textArea)
}

/**
 * 复制整个消息内容
 */
export function copyMessage(content: string) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(content).then(() => {
      // 这里可以显示成功提示，由调用方处理
      console.log('消息复制成功')
    }).catch(err => {
      console.error('消息复制失败:', err)
      fallbackCopyTextToClipboard(content)
    })
  } else {
    fallbackCopyTextToClipboard(content)
  }
}

// 将copyCode函数挂载到全局，供HTML中的onclick使用
if (typeof window !== 'undefined') {
  (window as any).copyCode = copyCode
}
